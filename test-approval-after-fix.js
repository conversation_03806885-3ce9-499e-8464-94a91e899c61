#!/usr/bin/env node

// Test the approval flow after RLS policy fix
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhmemh2cmtuamd3dHJrZ3lpbmpmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA2ODI4MiwiZXhwIjoyMDY1NjQ0MjgyfQ._scXNK1IgQuVok-iIBVxQYmcWntrI2VLInx1_Cz8knI';

console.log('🔧 Testing Approval Flow After RLS Fix');

async function testApprovalAfterFix() {
  const supabase = createClient(supabaseUrl, serviceRoleKey, {
    auth: { autoRefreshToken: false, persistSession: false }
  });

  try {
    console.log('\n1️⃣ Checking console admin users...');
    
    // Check for console admin users
    const { data: consoleAdmins, error: adminError } = await supabase
      .from('users')
      .select('*')
      .eq('role', 'admin')
      .is('company_id', null);

    if (adminError) {
      console.error('❌ Failed to fetch console admins:', adminError);
    } else {
      console.log(`✅ Found ${consoleAdmins?.length || 0} console admin users`);
      consoleAdmins?.forEach(admin => {
        console.log(`   👤 ${admin.email} (${admin.id}) - role: ${admin.role}, company_id: ${admin.company_id || 'null'}`);
      });
    }

    console.log('\n2️⃣ Testing company creation with service role...');
    
    // Test company creation with service role (this should work)
    const testCompanyData = {
      name: 'Test Company After RLS Fix',
      domain: 'test-rls-fix-' + Date.now() + '.com',
      registration_status: 'approved',
      business_type: 'staffing_agency',
      recruitment_enabled: true,
      bench_sales_enabled: true,
      primary_contact: {
        name: 'Test User',
        email: '<EMAIL>',
        phone: '************'
      }
    };

    const { data: testCompany, error: testError } = await supabase
      .from('companies')
      .insert(testCompanyData)
      .select('id')
      .single();

    if (testError) {
      console.error('❌ Test company creation failed:', testError);
    } else {
      console.log('✅ Test company created successfully:', testCompany?.id);
      
      // Clean up
      await supabase.from('companies').delete().eq('id', testCompany.id);
      console.log('   ♻️ Test company cleaned up');
    }

    console.log('\n3️⃣ Checking pending business registrations...');
    
    const { data: pendingRegs, error: regError } = await supabase
      .from('business_registrations')
      .select('*')
      .eq('status', 'pending')
      .limit(3);

    if (regError) {
      console.error('❌ Failed to fetch pending registrations:', regError);
    } else {
      console.log(`✅ Found ${pendingRegs?.length || 0} pending registrations`);
      
      if (pendingRegs && pendingRegs.length > 0) {
        console.log('\n4️⃣ Testing full approval simulation...');
        
        const testReg = pendingRegs[0];
        console.log(`   📋 Testing with: ${testReg.company_name} (${testReg.id})`);
        
        // Simulate the exact approval flow
        const approvalData = {
          name: testReg.company_name,
          domain: testReg.company_domain,
          registration_status: 'approved',
          business_type: 'staffing_agency',
          recruitment_enabled: true,
          bench_sales_enabled: true,
          primary_contact: {
            name: testReg.contact_person_name,
            email: testReg.contact_person_email,
            phone: testReg.contact_person_phone
          }
        };

        console.log('   💼 Creating company with approval data...');
        const { data: approvalCompany, error: approvalError } = await supabase
          .from('companies')
          .insert(approvalData)
          .select('id')
          .single();

        if (approvalError) {
          console.error('❌ Approval simulation failed:', approvalError);
          console.error('   Error details:', JSON.stringify(approvalError, null, 2));
        } else {
          console.log('✅ Approval simulation successful:', approvalCompany?.id);
          
          // Test updating the business registration
          console.log('   📝 Testing registration update...');
          const { error: updateError } = await supabase
            .from('business_registrations')
            .update({
              status: 'approved',
              reviewed_by: 'test-console-admin',
              reviewed_at: new Date().toISOString(),
              activated_company_id: approvalCompany.id
            })
            .eq('id', testReg.id);

          if (updateError) {
            console.error('❌ Registration update failed:', updateError);
          } else {
            console.log('✅ Registration update successful');
            
            // Revert the changes for testing
            await supabase
              .from('business_registrations')
              .update({
                status: 'pending',
                reviewed_by: null,
                reviewed_at: null,
                activated_company_id: null
              })
              .eq('id', testReg.id);
            
            console.log('   ♻️ Registration reverted to pending state');
          }
          
          // Clean up the test company
          await supabase.from('companies').delete().eq('id', approvalCompany.id);
          console.log('   ♻️ Test company cleaned up');
        }
      }
    }

    console.log('\n✅ Approval Flow Test Complete');
    console.log('\n📋 Next Steps:');
    console.log('   1. Try the approval process in the console app');
    console.log('   2. Check browser console for any detailed error messages');
    console.log('   3. Verify console user has admin role and null company_id');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testApprovalAfterFix().catch(console.error);
