#!/usr/bin/env node

// Test email configuration
import { config } from 'dotenv'
import nodemailer from 'nodemailer'

config()

console.log('🔍 Testing Email Configuration...\n')

console.log('Environment Variables:')
console.log('SMTP_HOST:', process.env.SMTP_HOST)
console.log('SMTP_PORT:', process.env.SMTP_PORT)
console.log('SMTP_USER:', process.env.SMTP_USER)
console.log('SMTP_PASS:', process.env.SMTP_PASS ? '***SET***' : 'NOT SET')
console.log('EMAIL_FROM_ADDRESS:', process.env.EMAIL_FROM_ADDRESS)
console.log('EMAIL_FROM_NAME:', process.env.EMAIL_FROM_NAME)
console.log('')

// Create transporter with same config as the app
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.zeptomail.in',
  port: parseInt(process.env.SMTP_PORT || '587', 10),
  secure: (process.env.SMTP_PORT || '587') === '465',
  auth: {
    user: process.env.SMTP_USER || 'emailapikey',
    pass: process.env.SMTP_PASS || ''
  }
})

async function testConnection() {
  try {
    console.log('🧪 Testing SMTP connection...')
    await transporter.verify()
    console.log('✅ SMTP connection successful!')
    
    // Test sending an email
    console.log('📧 Testing email sending...')
    const testEmail = {
      from: `"${process.env.EMAIL_FROM_NAME || 'ProcureServe'}" <${process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'}>`,
      to: '<EMAIL>', // This won't actually send since it's not a real email
      subject: 'Test Email - Configuration Check',
      html: '<h1>Test Email</h1><p>This is a test email to verify configuration.</p>',
      text: 'Test Email\n\nThis is a test email to verify configuration.'
    }
    
    console.log('Email configuration looks good!')
    console.log('From:', testEmail.from)
    console.log('Subject:', testEmail.subject)
    
  } catch (error) {
    console.error('❌ SMTP connection failed:')
    console.error('Error:', error.message)
    
    if (error.code === 'EAUTH') {
      console.error('\n🔐 Authentication failed. Please check:')
      console.error('1. SMTP_PASS is set correctly with your ZeptoMail API key')
      console.error('2. Your ZeptoMail account is active')
      console.error('3. The API key has proper permissions')
    }
    
    if (error.code === 'ECONNECTION' || error.code === 'ETIMEOUT') {
      console.error('\n🌐 Connection failed. Please check:')
      console.error('1. Internet connection')
      console.error('2. SMTP_HOST and SMTP_PORT are correct')
      console.error('3. Firewall settings')
    }
  }
}

testConnection()