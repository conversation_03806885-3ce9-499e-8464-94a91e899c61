# Dependencies
node_modules/
**/node_modules/

# Environment variables
.env
.env.local
.env.production
.env.development
.env.*
!.env.example
!.env.test

# Build outputs
dist/
build/
**/dist/
**/.svelte-kit/
**/.output/
**/.next/
**/.nuxt/
**/build/

# Deployment
.vercel/
.netlify/
.wrangler/

# Database & Backend
.supabase/
supabase/.branches/
supabase/.temp/
*.db
*.sqlite

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/
*.lcov

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Development tools
.vscode/
.idea/
*.swp
*.swo

# Temporary files
*.tmp
*.temp
.cache/
.temp/

# Debug scripts output (can be regenerated)
scripts/debug/output/
scripts/debug/logs/
scripts/debug/temp/

# Any generated test data files
scripts/debug/generated-*.json
scripts/debug/test-output-*.log

# Package manager files
.pnpm-store/
.yarn/
.npm/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Playwright artifacts (keeping for reference but already removed)
playwright-report/
playwright-report-*/
.test-results/
test-results/
.playwright/
*.trace.zip

# Generated types (if auto-generated)
**/database-types/generated/
**/shared-types/generated/

# Local Supabase data
supabase/volumes/
supabase/logs/

# Certificate files
*.pem
*.key
*.crt

# Backup files
*.backup
*.bak
*~

# Vite specific
vite.config.js.timestamp-*
vite.config.ts.timestamp-*
