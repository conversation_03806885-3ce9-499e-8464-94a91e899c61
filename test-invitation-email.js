import { Resend } from 'resend'

// Test with same configuration as the app
const RESEND_API_KEY = 're_8MBgcdYb_DaURr46BfLnvsymyY1jqxqjt'
const FROM_EMAIL = '<EMAIL>'

const resend = new Resend(RESEND_API_KEY)

console.log('🧪 Testing Resend API for ProcureServe Console...')

async function testInvitationEmail() {
  try {
    const { data, error } = await resend.emails.send({
      from: `ProcureServe Console <${FROM_EMAIL}>`,
      to: ['<EMAIL>'],
      subject: 'Test - Console Invitation Email',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>ProcureServe Console Invitation</title>
        </head>
        <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #6366f1; margin: 0; font-size: 24px;">ProcureServe</h1>
            <p style="color: #6b7280; margin: 5px 0;">Internal Console Access</p>
          </div>

          <div style="background: #f8fafc; border-radius: 8px; padding: 30px;">
            <h2 style="color: #1f2937; margin: 0 0 20px 0;">✅ Email Service Test Successful!</h2>
            
            <p>This is a test of the ProcureServe console invitation email system.</p>
            
            <p>If you receive this email, it means:</p>
            <ul>
              <li>✅ Resend API integration is working</li>
              <li>✅ Environment variables are configured correctly</li>
              <li>✅ Email service is ready for production</li>
            </ul>

            <div style="text-align: center; margin: 30px 0;">
              <a href="http://localhost:3008/users" style="background: #6366f1; color: white; padding: 12px 24px; border-radius: 6px; text-decoration: none; font-weight: 500; display: inline-block;">
                Go to Console
              </a>
            </div>

            <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; padding: 15px; margin: 20px 0;">
              <p style="margin: 0; color: #92400e;"><strong>Next Step:</strong> Try clicking the "Resend" button in the console app.</p>
            </div>
          </div>

        </body>
        </html>
      `
    })

    if (error) {
      console.error('❌ Email test failed:', error)
      return false
    }

    console.log('✅ Email sent successfully!')
    console.log('📧 Message ID:', data.id)
    console.log('📩 Check your <NAME_EMAIL>')
    return true

  } catch (error) {
    console.error('❌ Email test error:', error)
    return false
  }
}

testInvitationEmail()
