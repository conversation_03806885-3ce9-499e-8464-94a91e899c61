# Environment Setup for Business Registration Flow

This guide shows the required environment variables for the secure business registration and activation flow using Resend.

## Required Environment Variables

### Customer App (.env file)

```bash
# Supabase Configuration
PUBLIC_SUPABASE_URL=your_supabase_project_url
PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Site Configuration
PUBLIC_SITE_URL=https://yourdomain.com

# Email Service Configuration (same as candidate-app)
RESEND_API_KEY=your_resend_api_key
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=ProcureServe

# Database
DATABASE_URL=your_database_connection_string
```

### Console App (.env file)

```bash
# Supabase Configuration
PUBLIC_SUPABASE_URL=your_supabase_project_url
PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Email Service Configuration (same as candidate-app)
RESEND_API_KEY=your_resend_api_key
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=ProcureServe

# Site URLs
PUBLIC_SITE_URL=https://yourdomain.com
PUBLIC_CUSTOMER_APP_URL=https://customer.yourdomain.com

# Database
DATABASE_URL=your_database_connection_string
```

## Resend Configuration Steps

### 1. Create Resend Account
1. Go to [resend.com](https://resend.com)
2. Sign up for an account
3. Verify your email address

### 2. Add Your Domain
1. In the Resend dashboard, go to "Domains"
2. Click "Add Domain"
3. Enter your domain (e.g., `yourdomain.com`)
4. Follow the DNS configuration steps
5. Wait for domain verification

### 3. Create API Key
1. Go to "API Keys" in the dashboard
2. Click "Create API Key"
3. Give it a name (e.g., "PSII Production")
4. Copy the API key (starts with `re_`)
5. Add it to your environment variables as `RESEND_API_KEY`

### 4. Configure From Address
1. Set `EMAIL_FROM_ADDRESS` to an email using your verified domain
2. Common examples:
   - `<EMAIL>`
   - `<EMAIL>`
   - `<EMAIL>`

## Testing Configuration

### Development Environment
For development, you can use Resend's test mode:

```bash
# Development settings
RESEND_API_KEY=re_your_test_api_key
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=ProcureServe (Dev)
PUBLIC_SITE_URL=http://localhost:5173
```

### Test Email Sending
You can test email configuration by:

1. Running the customer app
2. Going to `/register/business`
3. Submitting a test registration
4. Checking the console logs for email sending status

## Security Considerations

### Environment Variable Security
- **Never commit** `.env` files to version control
- Use different API keys for development and production
- Rotate API keys regularly
- Use environment-specific configurations

### Domain Security
- Only use verified domains in production
- Configure SPF, DKIM, and DMARC records
- Monitor email delivery rates in Resend dashboard

## Troubleshooting

### Common Issues

1. **"API key not found" error**
   - Check that `RESEND_API_KEY` is set correctly
   - Verify the API key is active in Resend dashboard

2. **"Domain not verified" error**
   - Complete domain verification in Resend
   - Check DNS records are properly configured

3. **Emails not being delivered**
   - Check spam folders
   - Verify domain reputation
   - Review Resend delivery logs

4. **Rate limiting errors**
   - Check your Resend plan limits
   - Implement proper error handling
   - Consider upgrading plan if needed

### Debug Commands

```bash
# Test environment variables are loaded
echo $RESEND_API_KEY
echo $EMAIL_FROM_ADDRESS

# Check Resend API connectivity (requires curl)
curl -X POST 'https://api.resend.com/emails' \
  -H 'Authorization: Bearer YOUR_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Test Email",
    "text": "This is a test email."
  }'
```

## Environment Variable Validation

The system will automatically validate environment variables on startup:

- ✅ `RESEND_API_KEY` - Required for email sending
- ✅ `EMAIL_FROM_ADDRESS` - Required for from address
- ✅ `EMAIL_FROM_NAME` - Optional, defaults to "ProcureServe"
- ✅ `PUBLIC_SITE_URL` - Required for activation links

Missing required variables will show clear error messages in the console.

---

This configuration ensures consistent email handling across all PSII applications using the same Resend account and settings. 