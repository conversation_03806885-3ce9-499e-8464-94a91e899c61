#!/usr/bin/env node

// Quick verification that activation token is valid
import { createClient } from '@supabase/supabase-js';
import { createHash } from 'crypto';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const token = '2ff4c0669a5207ac2e252f11e3c62f532003af73c914e55d62c821ae7de4a111';
const hashedToken = createHash('sha256').update(token).digest('hex');

const supabase = createClient(
  process.env.PUBLIC_SUPABASE_URL, 
  process.env.SUPABASE_SERVICE_ROLE_KEY, 
  { auth: { autoRefreshToken: false, persistSession: false } }
);

console.log('🔍 Verifying activation token...');
console.log('Token:', token.substring(0, 16) + '...');
console.log('Hash:', hashedToken.substring(0, 16) + '...');

try {
  const { data: activationToken, error } = await supabase
    .from('activation_tokens')
    .select(`
      *,
      companies:company_id (name, domain),
      business_registrations:registration_id (company_name, contact_person_name)
    `)
    .eq('token_hash', hashedToken)
    .eq('used', false)
    .gt('expires_at', new Date().toISOString())
    .single();

  if (error || !activationToken) {
    console.log('❌ Token not found or expired');
    console.log('Error:', error?.message);
  } else {
    console.log('✅ Token is valid!');
    console.log('Contact:', activationToken.contact_name);
    console.log('Email:', activationToken.contact_email);
    console.log('Company:', activationToken.companies?.name || activationToken.business_registrations?.company_name);
    console.log('Expires:', new Date(activationToken.expires_at).toLocaleDateString());
    console.log('\n🔗 Activation URL:');
    console.log(`http://localhost:3004/activate?token=${token}`);
    console.log('\n📝 Ready to test activation in browser!');
  }
} catch (error) {
  console.error('❌ Verification failed:', error);
}
