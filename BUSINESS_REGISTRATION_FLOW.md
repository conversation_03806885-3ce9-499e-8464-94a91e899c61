# Business Registration & Activation Flow

This document outlines the complete secure business registration and activation flow implemented in the PSII system, using Resend for transactional emails instead of Supabase mail.

## Overview

The business registration flow consists of several stages:
1. **Business Registration** - Companies submit their registration details
2. **Admin Review** - Console app users review and approve/reject applications
3. **Account Activation** - Approved companies receive secure activation links
4. **Password Setup** - Users set passwords and activate their accounts
5. **Expired Link Handling** - Secure process for requesting new activation links

## Key Features

### 🔐 Security Features
- **Secure Token Generation**: 32-byte cryptographically secure tokens
- **Token Expiration**: 7-day expiry for all activation links
- **Rate Limiting**: Max 3 activation requests per hour per email
- **Single-Use Tokens**: Tokens are invalidated after successful activation
- **Email Enumeration Protection**: Generic responses to prevent email discovery

### 📧 Email Service Integration
- **Resend Primary**: Uses Resend API as the primary email provider
- **Fallback Support**: Falls back to Supabase if Resend fails
- **Professional Templates**: Rich HTML and text email templates
- **Template Customization**: Support for custom email templates per company

### 🔄 User Experience
- **Graceful Error Handling**: Clear error messages and recovery options
- **Expired Link Recovery**: Easy process to request new activation links
- **Progress Indicators**: Clear status updates throughout the process
- **Mobile Responsive**: Works seamlessly across all devices

## Technical Implementation

### File Structure

```
apps/customer-app/src/routes/auth/
├── activate/
│   ├── +page.server.ts        # Activation logic
│   └── +page.svelte          # Activation UI
└── request-activation/
    ├── +page.server.ts        # New link request logic
    └── +page.svelte          # New link request UI

apps/console-app/src/routes/companies/pending/[id]/
└── +page.server.ts           # Admin approval logic

packages/email-service/
├── providers.ts              # Email provider implementations
├── manager.ts                # Email service orchestration
├── templates.ts              # Email templates
└── index.ts                  # Public API

apps/customer-app/src/lib/
└── business-registration-emails.ts  # Business-specific email service
```

### Database Schema

```sql
-- Activation tokens table
CREATE TABLE public.activation_tokens (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  company_id UUID NOT NULL REFERENCES public.companies(id),
  token TEXT NOT NULL UNIQUE,
  contact_email TEXT NOT NULL,
  contact_name TEXT NOT NULL,
  process_permissions TEXT[] DEFAULT ARRAY['recruitment', 'bench_sales'],
  used BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMPTZ,
  activated_user_id UUID REFERENCES auth.users(id),
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Configuration

### Environment Variables

```bash
# Required for email service (same as candidate-app)
RESEND_API_KEY=your_resend_api_key
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=ProcureServe

# Supabase configuration
PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Application URLs
PUBLIC_SITE_URL=https://yourdomain.com
```

### Email Provider Priority

1. **Resend** (Primary) - Used if `RESEND_API_KEY` is configured
2. **Supabase** (Fallback) - Used if Resend fails or is not configured
3. **SMTP** (Optional) - Can be configured for custom SMTP servers

## Flow Details

### 1. Business Registration

```mermaid
graph TD
    A[Company Submits Registration] --> B[Form Validation]
    B --> C[Store in business_leads]
    C --> D[Send Acknowledgment Email]
    D --> E[Admin Notification]
```

**Key Points:**
- Form validation ensures data integrity
- Acknowledgment email sent immediately
- Admin team notified of new applications

### 2. Admin Review Process

```mermaid
graph TD
    A[Admin Reviews Application] --> B{Approve or Reject?}
    B -->|Approve| C[Create Company Record]
    B -->|Reject| D[Send Rejection Email]
    C --> E[Generate Secure Token]
    E --> F[Store Activation Token]
    F --> G[Send Activation Email]
    D --> H[Update Lead Status]
    G --> H
```

**Key Points:**
- Secure 32-byte token generation
- 7-day expiration period
- Professional email templates
- Automatic status updates

### 3. Account Activation

```mermaid
graph TD
    A[User Clicks Activation Link] --> B[Validate Token]
    B -->|Valid| C[Show Password Form]
    B -->|Invalid/Expired| D[Redirect to Request New Link]
    C --> E[User Sets Password]
    E --> F[Create Supabase Auth User]
    F --> G[Create Customer App User]
    G --> H[Mark Token as Used]
    H --> I[Update Company Status]
    I --> J[Redirect to Success Page]
```

**Key Points:**
- Token validation with expiry check
- Secure password requirements
- Atomic user creation process
- Graceful error handling

### 4. Expired Link Handling

```mermaid
graph TD
    A[User Accesses Expired Link] --> B[Redirect to Request Page]
    B --> C[Pre-fill Email if Available]
    C --> D[User Submits Request]
    D --> E[Validate Email & Rate Limits]
    E -->|Valid| F[Generate New Token]
    E -->|Rate Limited| G[Show Rate Limit Error]
    F --> H[Send New Activation Email]
    H --> I[Show Success Message]
```

**Key Points:**
- Rate limiting prevents abuse
- Pre-filled forms for better UX
- New token invalidates previous ones
- Clear success/error messaging

## Security Considerations

### Token Security
- **Cryptographically Secure**: Uses `crypto.randomBytes(32)`
- **Unique Constraint**: Database ensures token uniqueness
- **Expiration**: 7-day automatic expiry
- **Single Use**: Tokens marked as used after activation

### Rate Limiting
- **3 requests per hour** per email address
- **Database-backed**: Persistent across server restarts
- **IP + Email**: Combined rate limiting strategy

### Email Security
- **No Email Enumeration**: Generic responses for security
- **HTTPS Links**: All activation links use HTTPS
- **Domain Validation**: Links only work for configured domains

## Email Templates

### Available Templates

1. **registration_submitted** - Acknowledgment after registration
2. **registration_approved** - Approval with activation link
3. **registration_rejected** - Rejection with next steps
4. **activation_link_renewed** - New activation link (when requested)

### Template Variables

```typescript
interface BusinessRegistrationEmailData {
  company_name: string
  contact_name: string
  contact_email: string
  submission_date: string
  registration_id: string
}

interface BusinessApprovalEmailData extends BusinessRegistrationEmailData {
  activation_link: string
  company_id: string
}
```

## Error Handling

### Common Error Scenarios

1. **Expired Token**: Redirect to request new link page
2. **Invalid Token**: Show error with request new link option
3. **Already Used Token**: Inform user and provide login link
4. **Rate Limited**: Clear message with retry time
5. **Email Service Failure**: Fallback to alternative provider

### User-Friendly Messages

- Clear, non-technical language
- Actionable next steps
- Support contact information
- Appropriate visual indicators (icons, colors)

## Monitoring & Maintenance

### Recommended Monitoring

1. **Email Delivery Rates**: Monitor via Resend dashboard
2. **Token Expiry**: Track unused expired tokens
3. **Activation Success Rate**: Monitor completion rates
4. **Error Rates**: Track failed activations

### Maintenance Tasks

```sql
-- Clean up expired tokens (run monthly)
SELECT public.cleanup_expired_activation_tokens();

-- Monitor activation success rates
SELECT 
  DATE_TRUNC('day', created_at) as date,
  COUNT(*) as total_tokens,
  COUNT(CASE WHEN used = true THEN 1 END) as activated,
  ROUND(COUNT(CASE WHEN used = true THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM activation_tokens 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY date DESC;
```

## Testing

### Test Scenarios

1. **Happy Path**: Complete registration → approval → activation
2. **Expired Token**: Test expired link handling and new link request
3. **Rate Limiting**: Test multiple requests within time window
4. **Email Failures**: Test fallback email providers
5. **Invalid Data**: Test form validation and error handling

### Test Email Configuration

For development, set up test email addresses and use Resend's test mode to avoid sending real emails during testing.

## Troubleshooting

### Common Issues

1. **Emails Not Sending**
   - Check `RESEND_API_KEY` configuration
   - Verify `EMAIL_FROM_ADDRESS` and `EMAIL_FROM_NAME` are set
   - Verify domain configuration in Resend
   - Check email service logs

2. **Activation Links Not Working**
   - Verify `PUBLIC_SITE_URL` configuration
   - Check token expiration
   - Validate database connectivity

3. **Rate Limiting Issues**
   - Check database timestamps
   - Verify rate limiting logic
   - Consider adjusting limits for testing

### Debug Commands

```bash
# Check email service configuration
npm run check-email-config

# Test email sending
npm run test-email

# Validate activation token
npm run validate-token <token>
```

## Future Enhancements

### Planned Features

1. **Multi-language Support**: Localized email templates
2. **Custom Branding**: Company-specific email templates
3. **Advanced Analytics**: Detailed activation metrics
4. **Webhook Integration**: Real-time activation notifications
5. **Mobile App Support**: Deep linking for mobile activation

### Integration Opportunities

1. **CRM Integration**: Sync activation status with CRM systems
2. **Analytics Platforms**: Send activation events to analytics
3. **Slack/Teams**: Real-time notifications for admin team
4. **Audit Logging**: Comprehensive security audit trails

---

This implementation provides a secure, user-friendly, and maintainable business registration and activation flow that can scale with your business needs while maintaining high security standards. 