#!/usr/bin/env node

// Test script to debug approval flow
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhmemh2cmtuamd3dHJrZ3lpbmpmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA2ODI4MiwiZXhwIjoyMDY1NjQ0MjgyfQ._scXNK1IgQuVok-iIBVxQYmcWntrI2VLInx1_Cz8knI';

console.log('🧪 Testing Approval Flow Debug');
console.log('📡 Supabase URL:', supabaseUrl);
console.log('🔑 Service Role Key Length:', serviceRoleKey?.length || 0);

async function testApprovalFlow() {
  const supabase = createClient(supabaseUrl, serviceRoleKey, {
    auth: { autoRefreshToken: false, persistSession: false }
  });

  try {
    console.log('\n1️⃣ Testing Database Connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('business_registrations')
      .select('count')
      .limit(1);
    
    if (connectionError) {
      console.error('❌ Database connection failed:', connectionError);
      return;
    }
    console.log('✅ Database connection successful');

    console.log('\n2️⃣ Finding Pending Registrations...');
    const { data: pendingRegistrations, error: fetchError } = await supabase
      .from('business_registrations')
      .select('*')
      .eq('status', 'pending')
      .limit(5);

    if (fetchError) {
      console.error('❌ Failed to fetch pending registrations:', fetchError);
      return;
    }

    console.log(`✅ Found ${pendingRegistrations?.length || 0} pending registrations`);
    
    if (!pendingRegistrations || pendingRegistrations.length === 0) {
      console.log('ℹ️ No pending registrations to test with');
      return;
    }

    const testRegistration = pendingRegistrations[0];
    console.log('\n3️⃣ Test Registration Details:');
    console.log(`   📋 ID: ${testRegistration.id}`);
    console.log(`   🏢 Company: ${testRegistration.company_name}`);
    console.log(`   📧 Email: ${testRegistration.contact_person_email}`);
    console.log(`   🌐 Domain: ${testRegistration.company_domain}`);

    console.log('\n4️⃣ Testing Company Creation (Dry Run)...');
    
    // Test if we can create a company (dry run)
    const companyData = {
      name: testRegistration.company_name,
      domain: testRegistration.company_domain,
      registration_status: 'test_mode',
      business_type: 'staffing_agency',
      recruitment_enabled: true,
      bench_sales_enabled: true,
      primary_contact: {
        name: testRegistration.contact_person_name,
        email: testRegistration.contact_person_email,
        phone: testRegistration.contact_person_phone
      }
    };

    console.log('   Company data to create:', JSON.stringify(companyData, null, 2));

    // Check if company already exists with this domain
    const { data: existingCompany, error: existingError } = await supabase
      .from('companies')
      .select('*')
      .eq('domain', testRegistration.company_domain)
      .limit(1);

    if (existingError) {
      console.error('❌ Error checking existing companies:', existingError);
      return;
    }

    if (existingCompany && existingCompany.length > 0) {
      console.log('ℹ️ Company with this domain already exists:', existingCompany[0].id);
    } else {
      console.log('✅ Domain is available for new company creation');
    }

    console.log('\n5️⃣ Testing Email Configuration...');
    
    // Check Resend API key
    const resendKey = process.env.RESEND_API_KEY;
    console.log(`   🔑 Resend API Key: ${resendKey ? `${resendKey.substring(0, 10)}...` : 'NOT SET'}`);
    
    // Check email environment variables
    console.log(`   📧 From Address: ${process.env.EMAIL_FROM_ADDRESS || 'NOT SET'}`);
    console.log(`   👤 From Name: ${process.env.EMAIL_FROM_NAME || 'NOT SET'}`);
    console.log(`   📬 Reply To: ${process.env.EMAIL_REPLY_TO || 'NOT SET'}`);

    console.log('\n6️⃣ Testing Email Service (Direct)...');
    
    if (resendKey) {
      try {
        const { Resend } = await import('resend');
        const resend = new Resend(resendKey);
        
        console.log('   ✅ Resend SDK loaded successfully');
        console.log('   📤 Testing email send...');
        
        // Test email send
        const { data: emailData, error: emailError } = await resend.emails.send({
          from: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>',
          to: [testRegistration.contact_person_email],
          subject: 'Test Email - Approval Flow Debug',
          html: `
            <h1>Test Email</h1>
            <p>This is a test email to verify the approval flow is working.</p>
            <p><strong>Company:</strong> ${testRegistration.company_name}</p>
            <p><strong>Registration ID:</strong> ${testRegistration.id}</p>
            <p>This email was sent as part of debugging the approval process.</p>
          `
        });

        if (emailError) {
          console.error('❌ Direct email send failed:', emailError);
        } else {
          console.log('✅ Direct email send successful:', emailData?.id || 'no ID returned');
        }
      } catch (emailSetupError) {
        console.error('❌ Email service setup error:', emailSetupError.message);
      }
    } else {
      console.log('⚠️ Resend API key not configured, skipping direct email test');
    }

    console.log('\n✅ Approval Flow Debug Complete');
    console.log('\n📋 Summary:');
    console.log(`   - Database connection: ✅`);
    console.log(`   - Pending registrations: ${pendingRegistrations.length} found`);
    console.log(`   - Email configuration: ${resendKey ? '✅' : '❌'}`);
    console.log(`   - Test registration ID: ${testRegistration.id}`);

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

testApprovalFlow().catch(console.error);
