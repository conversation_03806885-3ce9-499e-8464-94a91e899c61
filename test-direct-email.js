// Direct Email Test for Phase 3
// Tests the email workflow directly using Node.js

import { config } from 'dotenv';
import { Resend } from 'resend';

// Load environment variables
config({ path: '.env.local' });

console.log('🧪 Direct Email Test - Phase 3');
console.log('==============================\n');

console.log('Environment Check:');
console.log('- RESEND_API_KEY:', process.env.RESEND_API_KEY ? '✅ Set' : '❌ Missing');
console.log('- EMAIL_FROM_ADDRESS:', process.env.EMAIL_FROM_ADDRESS || 'Not set');
console.log('- EMAIL_FROM_NAME:', process.env.EMAIL_FROM_NAME || 'Not set');
console.log('');

// Test Resend API connection
async function testResend() {
  try {
    const resend = new Resend(process.env.RESEND_API_KEY);
    
    const testEmail = {
      from: `${process.env.EMAIL_FROM_NAME || 'ProcureServe'} <${process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'}>`,
      to: ['<EMAIL>'],
      subject: 'PSII Phase 3 Email Test - Console Admin Notification',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2563eb;">ProcureServe Console</h1>
          <h2 style="color: #92400e;">🧪 Email System Test</h2>
          <p>This is a test email from the PSII Phase 3 email notification system.</p>
          
          <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>Test Registration Details:</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr><td style="font-weight: 600;">Company:</td><td>Email Test Company</td></tr>
              <tr><td style="font-weight: 600;">Contact:</td><td>Email Test User</td></tr>
              <tr><td style="font-weight: 600;">Email:</td><td><EMAIL></td></tr>
              <tr><td style="font-weight: 600;">Domain:</td><td>newdomain.com</td></tr>
              <tr><td style="font-weight: 600;">Type:</td><td><span style="background: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 4px;">NEW COMPANY</span></td></tr>
            </table>
          </div>
          
          <p>✅ <strong>Email workflow is functioning correctly!</strong></p>
          <p style="color: #64748b; font-size: 14px;">
            Generated at: ${new Date().toLocaleString()}<br>
            System: PSII Phase 3 Email Notification System
          </p>
        </div>
      `,
      text: `
        ProcureServe Console - Email System Test
        
        This is a test email from the PSII Phase 3 email notification system.
        
        Test Registration Details:
        Company: Email Test Company
        Contact: Email Test User
        Email: <EMAIL>
        Domain: newdomain.com
        Type: NEW COMPANY
        
        ✅ Email workflow is functioning correctly!
        
        Generated at: ${new Date().toLocaleString()}
        System: PSII Phase 3 Email Notification System
      `
    };

    console.log('📧 Sending test <NAME_EMAIL>...');
    
    const { data, error } = await resend.emails.send(testEmail);
    
    if (error) {
      console.error('❌ Email send failed:', error);
      return false;
    }
    
    console.log('✅ Email sent successfully!');
    console.log('Message ID:', data?.id);
    console.log('');
    return true;
    
  } catch (error) {
    console.error('❌ Email test failed:', error);
    return false;
  }
}

// Run the test
testResend().then(success => {
  if (success) {
    console.log('🎯 Phase 3 Email System is working correctly!');
    console.log('✅ Ready for production testing');
  } else {
    console.log('❌ Email system needs attention');
    console.log('Check Resend API key and configuration');
  }
}).catch(console.error);
