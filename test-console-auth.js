// Test script to check console authentication status
import { createClient } from '@supabase/supabase-js'

const SUPABASE_URL = 'https://hfzhvrknjgwtrkgyinjf.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhmemh2cmtuamd3dHJrZ3lpbmpmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjgyODIsImV4cCI6MjA2NTY0NDI4Mn0.TwvhR8RrtEZuV6oCXIT2fho5_U5mnH21Hvt4bbvhThY'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

async function testAuth() {
  console.log('🔍 Testing Console Authentication...\n')

  // Check current session
  console.log('1. Checking current session...')
  const { data: { session }, error: sessionError } = await supabase.auth.getSession()
  
  if (sessionError) {
    console.error('❌ Session error:', sessionError)
    return
  }

  if (session) {
    console.log('✅ User session exists:')
    console.log(`   User ID: ${session.user.id}`)
    console.log(`   Email: ${session.user.email}`)
    console.log(`   Expires: ${new Date(session.expires_at * 1000)}`)
  } else {
    console.log('❌ No active session found')
  }

  // Check console user data if session exists
  if (session) {
    console.log('\n2. Checking console user data...')
    const { data: consoleUser, error: userError } = await supabase
      .from('console_users')
      .select('*')
      .eq('id', session.user.id)
      .single()

    if (userError) {
      console.error('❌ Console user error:', userError)
    } else if (consoleUser) {
      console.log('✅ Console user found:')
      console.log(`   ID: ${consoleUser.id}`)
      console.log(`   Email: ${consoleUser.email}`)
      console.log(`   Role: ${consoleUser.role}`)
      console.log(`   Active: ${consoleUser.is_active}`)
      console.log(`   Last Login: ${consoleUser.last_login}`)
    } else {
      console.log('❌ Console user not found')
    }
  }

  // Test login with admin credentials
  console.log('\n3. Testing login with admin credentials...')
  
  // First, sign out any existing session
  await supabase.auth.signOut()
  console.log('   Signed out existing session')

  // Try to sign in
  const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'admin123'
  })

  if (loginError) {
    console.error('❌ Login failed:', loginError)
  } else {
    console.log('✅ Login successful:')
    console.log(`   User ID: ${loginData.user.id}`)
    console.log(`   Email: ${loginData.user.email}`)
    console.log(`   Session expires: ${new Date(loginData.session.expires_at * 1000)}`)
  }

  // Clean up
  await supabase.auth.signOut()
  console.log('\n🧹 Cleaned up test session')
}

testAuth().catch(console.error)
