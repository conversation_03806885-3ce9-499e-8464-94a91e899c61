// Test login form submission
async function testLoginFormSubmission() {
  console.log('🔐 Testing login form submission...')

  try {
    // Test login form submission
    const formData = new FormData()
    formData.append('email', '<EMAIL>')
    formData.append('password', 'admin123')
    formData.append('remember', 'on')

    console.log('1. Submitting login form...')
    const response = await fetch('http://localhost:3008/login?/login', {
      method: 'POST',
      body: formData,
      redirect: 'manual' // Don't follow redirects automatically
    })

    console.log(`   Login form response: ${response.status}`)
    console.log(`   Response headers:`, Object.fromEntries(response.headers))

    if (response.status === 303) {
      const location = response.headers.get('location')
      console.log(`   ✅ Redirect to: ${location}`)
      
      if (location === '/dashboard') {
        console.log('   ✅ Login successful - redirected to dashboard')
      } else {
        console.log(`   ❌ Unexpected redirect location: ${location}`)
      }
    } else if (response.status === 200) {
      const responseText = await response.text()
      if (responseText.includes('error')) {
        console.log('   ❌ Login failed with error in response')
      } else {
        console.log('   ⚠️  Login form returned 200 (unexpected)')
      }
    } else {
      console.log(`   ❌ Unexpected response status: ${response.status}`)
    }

  } catch (error) {
    console.error('❌ Error during login test:', error)
  }
}

testLoginFormSubmission()
