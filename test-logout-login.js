// Test logout and login flow
async function testLogoutLogin() {
  console.log('🔄 Testing logout and login flow...')

  try {
    // 1. Call logout endpoint
    console.log('1. Logging out...')
    const logoutResponse = await fetch('http://localhost:3008/api/auth/signout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    console.log(`   Logout response: ${logoutResponse.status}`)
    if (logoutResponse.status === 303) {
      console.log('   ✅ Logout redirect received')
    }

    // 2. Try to access dashboard (should redirect to login)
    console.log('\n2. Testing access to dashboard after logout...')
    const dashboardResponse = await fetch('http://localhost:3008/dashboard')
    console.log(`   Dashboard response: ${dashboardResponse.status}`)
    
    // 3. Try login page
    console.log('\n3. Accessing login page...')
    const loginPageResponse = await fetch('http://localhost:3008/login')
    console.log(`   Login page response: ${loginPageResponse.status}`)
    
    if (loginPageResponse.ok) {
      const content = await loginPageResponse.text()
      if (content.includes('Email')) {
        console.log('   ✅ Login form is now accessible')
      } else {
        console.log('   ❌ Still showing dashboard content')
      }
    }

  } catch (error) {
    console.error('❌ Error during test:', error)
  }
}

testLogoutLogin()
