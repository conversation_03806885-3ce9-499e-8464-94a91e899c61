// Simple Email Template Test - Phase 3
// Tests the core email functionality with clean syntax

import { config } from 'dotenv';
import { Resend } from 'resend';

config({ path: '.env.local' });

console.log('🧪 Phase 3 Email Template Test');
console.log('==============================\n');

const resend = new Resend(process.env.RESEND_API_KEY);

async function testConsoleAdminEmail() {
  console.log('📧 Testing Console Admin Notification...');
  
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin: 0;">ProcureServe Console</h1>
        <p style="color: #64748b; margin: 5px 0;">Business Registration Review Required</p>
      </div>
      
      <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #f59e0b;">
        <h2 style="color: #92400e; margin-top: 0;">⚡ New Registration Pending Review</h2>
        <p style="color: #92400e;">A new business registration requires your immediate attention.</p>
      </div>
      
      <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h3 style="color: #1e293b; margin-top: 0;">Registration Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
          <tr><td style="font-weight: 600; padding: 8px 0;">Company:</td><td style="padding: 8px 0;">Email Test Company</td></tr>
          <tr><td style="font-weight: 600; padding: 8px 0;">Contact:</td><td style="padding: 8px 0;">Email Test User</td></tr>
          <tr><td style="font-weight: 600; padding: 8px 0;">Email:</td><td style="padding: 8px 0;"><EMAIL></td></tr>
          <tr><td style="font-weight: 600; padding: 8px 0;">Domain:</td><td style="padding: 8px 0;">newdomain.com</td></tr>
        </table>
      </div>
      
      <div style="text-align: center; margin: 25px 0;">
        <a href="http://localhost:3008/companies/pending/18e07d34-bd84-45d4-9eee-4550d5b8b468" 
           style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;">
          Review Application
        </a>
      </div>
    </div>
  `;

  const textContent = `
    ProcureServe Console - New Registration Pending Review
    
    A new business registration requires your immediate attention.
    
    Registration Details:
    Company: Email Test Company
    Contact: Email Test User
    Email: <EMAIL>
    Domain: newdomain.com
    
    Review Application: http://localhost:3008/companies/pending/18e07d34-bd84-45d4-9eee-4550d5b8b468
  `;

  try {
    const { data, error } = await resend.emails.send({
      from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_FROM_ADDRESS}>`,
      to: ['<EMAIL>'],
      subject: 'New Business Registration: Email Test Company',
      html: htmlContent,
      text: textContent,
      replyTo: process.env.EMAIL_REPLY_TO || '<EMAIL>'
    });

    if (error) {
      console.error('❌ Failed:', error.message);
      return false;
    } else {
      console.log('✅ Console admin email sent! ID:', data.id);
      return true;
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    return false;
  }
}

async function testApprovalEmail() {
  console.log('📧 Testing Registration Approval Email...');
  
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin: 0;">ProcureServe</h1>
        <p style="color: #64748b; margin: 5px 0;">Business Registration Platform</p>
      </div>
      
      <div style="background: #dcfce7; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #16a34a;">
        <h2 style="color: #15803d; margin-top: 0;">🎉 Registration Approved!</h2>
        <p style="color: #166534;">Congratulations! <strong>Email Test Company</strong> has been approved to join ProcureServe.</p>
      </div>
      
      <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h3 style="color: #1e293b; margin-top: 0;">Activate Your Account</h3>
        <p style="color: #475569;">To complete your registration and access your ProcureServe dashboard, please activate your account by clicking the button below:</p>
        
        <div style="text-align: center; margin: 25px 0;">
          <a href="http://localhost:3004/auth/activate?token=test-activation-token" 
             style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;">
            Activate Account & Set Password
          </a>
        </div>
        
        <p style="color: #64748b; font-size: 14px;">This activation link will expire in 7 days.</p>
      </div>
    </div>
  `;

  try {
    const { data, error } = await resend.emails.send({
      from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_FROM_ADDRESS}>`,
      to: ['<EMAIL>'],
      subject: 'Welcome to ProcureServe - Account Activation Required',
      html: htmlContent,
      replyTo: process.env.EMAIL_REPLY_TO
    });

    if (error) {
      console.error('❌ Failed:', error.message);
      return false;
    } else {
      console.log('✅ Approval email sent! ID:', data.id);
      return true;
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    return false;
  }
}

async function runTests() {
  const results = [];
  
  results.push(await testConsoleAdminEmail());
  await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
  
  results.push(await testApprovalEmail());
  
  const successCount = results.filter(r => r).length;
  
  console.log('\n🎯 Email Template Testing Complete!');
  console.log('====================================');
  console.log(`✅ ${successCount}/${results.length} emails sent successfully`);
  console.log('📧 Check your email inbox for test messages');
  
  if (successCount === results.length) {
    console.log('🚀 Phase 3 Email System is fully operational!');
  }
}

runTests().catch(console.error);
  console.log('🔍 Domain detection working correctly');
  console.log('🚀 System ready for production use');
  
  if (newCompanyId && existingCustomerId) {
    console.log('\n📋 Test Registration IDs:');
    console.log(`- New Company: ${newCompanyId}`);
    console.log(`- Existing Customer: ${existingCustomerId}`);
  }
}

runFullTest().catch(console.error);
