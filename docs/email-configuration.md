# Email Service Configuration

## Overview
PSII uses Resend API for reliable transactional email delivery. This configuration ensures all registration emails, password resets, and notifications are properly delivered.

## Configuration Details

### Environment Variables
The following environment variables must be set in your `.env` file:

```bash
# Resend API Configuration
RESEND_API_KEY=your_resend_api_key_here

# Email sender configuration
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=ProcureServe Team
EMAIL_REPLY_TO=<EMAIL>
```

### Supabase Configuration
For development, Supabase can use its built-in email service, but for production we use Resend API directly through our email service package.

## Testing Email Configuration

### 1. Command Line Test
Run the standalone email test script:

```bash
cd /Users/<USER>/Desktop/PSII
node scripts/test-email.js
```

### 2. Application Integration Test
Use the email service utilities in your application code:

```typescript
import { createEmailServiceUtils } from '@psii/email-service'

const emailUtils = createEmailServiceUtils()

// Test email configuration
const success = await emailUtils.testEmailConfiguration(
  '<EMAIL>', 
  'company-id-here'
)
```

## Email Types Supported

### 1. User Registration/Welcome Emails
- Sent when users complete registration
- Contains welcome message and account confirmation
- Template type: `welcome`

### 2. Password Reset Emails
- Sent when users request password reset
- Contains secure reset link
- Template type: `password_reset`

### 3. User Invitation Emails
- Sent when admins invite new users
- Contains invitation acceptance link
- Template type: `user_invitation`

### 4. Notification Emails
- General purpose notifications
- Customizable subject and content
- Template type: `notification`

## Email Service Architecture

### 1. Multi-Provider Support
- **ZeptoMail**: Primary SMTP provider (cost-effective)
- **Supabase**: Fallback provider for development
- **SMTP**: Generic SMTP support for other providers

### 2. Company-Scoped Configuration
- Each company can have its own email configuration
- Stored in `email_service_config` table
- Row-level security ensures data isolation

### 3. Usage Tracking
- Daily email count tracking
- Monthly limits (configurable)
- Usage analytics and monitoring

## Security Features

### 1. Credential Protection
- API keys stored as environment variables
- Database credentials encrypted at application layer
- No hardcoded sensitive data

### 2. Row-Level Security
- Email configurations scoped to companies
- Only admins/managers can modify settings
- Audit trail for all email activities

### 3. Rate Limiting
- Supabase built-in rate limiting
- Custom daily/monthly limits per company
- Protection against abuse

## Troubleshooting

### Common Issues

1. **Emails not being sent**
   - Check SMTP credentials in `.env` file
   - Verify ZeptoMail API key is active
   - Ensure sender domain is verified in ZeptoMail

2. **SMTP Authentication Failed**
   - Confirm `SMTP_PASS` environment variable is set
   - Verify the API key has proper permissions
   - Check firewall allows SMTP traffic on port 587

3. **Emails going to spam**
   - Verify SPF/DKIM records for sender domain
   - Ensure sender reputation is good
   - Use proper email templates with unsubscribe links

### Debugging Steps

1. Run the test script: `node scripts/test-email.js`
2. Check Supabase logs for SMTP errors
3. Verify environment variables are loaded correctly
4. Test with a different email provider if needed

## Production Considerations

### 1. Domain Verification
- Verify sender domain in ZeptoMail console
- Set up SPF, DKIM, and DMARC records
- Monitor sender reputation

### 2. Monitoring
- Set up email delivery monitoring
- Track bounce rates and spam complaints
- Monitor daily/monthly usage limits

### 3. Backup Configuration
- Configure fallback email provider
- Test failover scenarios
- Document recovery procedures

## Cost Optimization

### ZeptoMail Pricing
- Free tier: 10,000 emails/month
- Paid plans: $2.50/10,000 emails
- No setup fees or monthly minimums

### Usage Monitoring
- Track daily email counts per company
- Set alerts for high usage
- Implement email queuing for bulk operations

## Migration Notes

### From Previous Implementation
- Removed circular authentication dependencies
- Simplified SMTP configuration
- Added multi-tenant email management
- Improved error handling and fallbacks

### Future Enhancements
- Email template customization per company
- A/B testing for email templates
- Advanced analytics and reporting
- Integration with marketing automation tools
