# PSII Vercel Deployment Configuration Guide

## Overview
ProcureServe II uses Vercel for hosting all three SvelteKit applications with automatic deployments from Git. Each app is configured with the Vercel adapter and optimized for Node.js 18.x runtime.

## Application Deployment Mapping

### 1. Customer App (app.procureserve.com)
- **Repository Path**: `/apps/customer-app/`
- **Build Command**: `npm run build`
- **Output Directory**: `.svelte-kit/output`
- **Runtime**: Node.js 18.x
- **Framework**: SvelteKit

### 2. Console App (console.procureserve.com)  
- **Repository Path**: `/apps/console-app/`
- **Build Command**: `npm run build`
- **Output Directory**: `.svelte-kit/output`
- **Runtime**: Node.js 18.x
- **Framework**: SvelteKit

### 3. Candidate App (candidate.procureserve.com)
- **Repository Path**: `/apps/candidate-app/`
- **Build Command**: `npm run build`
- **Output Directory**: `.svelte-kit/output`
- **Runtime**: Node.js 18.x
- **Framework**: SvelteKit

## Vercel Configuration Files

### SvelteKit Adapter Configuration
```javascript
// svelte.config.js (all apps)
import adapter from '@sveltejs/adapter-vercel';

const config = {
  kit: {
    adapter: adapter({
      runtime: 'nodejs18.x',
      memory: 1024,
      maxDuration: 30
    }),
    alias: {
      '$shared-types': '../../packages/shared-types',
      '$shared-utils': '../../packages/shared-utils',
      '$database-types': '../../packages/database-types'
    }
  }
};
```

### Project Settings per App
```json
{
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "outputDirectory": ".svelte-kit/output",
  "framework": "sveltejs",
  "nodeVersion": "18.x"
}
```

## Environment Variables Configuration

### Required Environment Variables (All Apps)
```bash
# Supabase Configuration
PUBLIC_SUPABASE_URL=https://hfzhvrknjgwtrkgyinjf.supabase.co
PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Email Service (Resend)
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxx
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=ProcureServe
EMAIL_REPLY_TO=<EMAIL>

# Application URLs
PUBLIC_SITE_URL=https://app.procureserve.com
CONSOLE_URL=https://console.procureserve.com
CANDIDATE_URL=https://candidate.procureserve.com

# Optional: OpenAI Integration
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxx
```

### App-Specific Environment Variables

#### Customer App
```bash
PUBLIC_APP_NAME=ProcureServe Customer Portal
PUBLIC_APP_TYPE=customer
```

#### Console App  
```bash
PUBLIC_APP_NAME=ProcureServe Console
PUBLIC_APP_TYPE=console
```

#### Candidate App
```bash
PUBLIC_APP_NAME=ProcureServe Candidate Portal
PUBLIC_APP_TYPE=candidate
```

## Domain Configuration

### Custom Domains Setup
1. **Customer App**: app.procureserve.com
2. **Console App**: console.procureserve.com  
3. **Candidate App**: candidate.procureserve.com

### DNS Configuration
```dns
# CNAME Records
app.procureserve.com     → cname.vercel-dns.com
console.procureserve.com → cname.vercel-dns.com
candidate.procureserve.com → cname.vercel-dns.com

# SSL Certificates: Automatic via Vercel
```

## Build Optimization

### Package.json Scripts
```json
{
  "scripts": {
    "build": "vite build",
    "dev": "vite dev --port 3004",
    "preview": "vite preview",
    "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json",
    "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"
  }
}
```

### Vite Configuration
```javascript
// vite.config.js
import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';

export default defineConfig({
  plugins: [sveltekit()],
  build: {
    target: 'node18',
    rollupOptions: {
      external: ['@supabase/supabase-js']
    }
  },
  optimizeDeps: {
    include: ['@supabase/supabase-js']
  }
});
```

## Deployment Workflow

### Automatic Deployment Triggers
```yaml
# Git Branch → Vercel Environment Mapping
main branch → Production deployment
develop branch → Preview deployment
feature/* branches → Branch preview deployment
```

### Build Process
1. **Install Dependencies**: `npm install` (with workspace support)
2. **Type Checking**: `svelte-check --tsconfig ./tsconfig.json`
3. **Build**: `npm run build` 
4. **Deploy**: Automatic deployment to Vercel edge network

### Deployment Status Checks
```bash
# Check deployment status
vercel --prod list

# View deployment logs  
vercel logs [deployment-url]

# Promote preview to production
vercel --prod promote [preview-url]
```

## Performance Optimization

### Edge Functions
```typescript
// src/routes/api/health/+server.ts
export async function GET() {
  return new Response(JSON.stringify({ status: 'healthy' }), {
    headers: { 'content-type': 'application/json' }
  });
}
```

### Static Assets Caching
```javascript
// app.html - Cache headers
<meta http-equiv="cache-control" content="public, max-age=31536000, immutable">
```

### Bundle Analysis
```bash
# Analyze bundle size
npm run build -- --analyze

# Check bundle composition
npx vite-bundle-analyzer .svelte-kit/output
```

## Monitoring & Analytics

### Vercel Analytics Integration
```typescript
// app.html
import { inject } from '@vercel/analytics';
inject();
```

### Performance Monitoring
- **Core Web Vitals**: Automatic tracking
- **Real User Monitoring**: Built-in Vercel analytics
- **Error Tracking**: Sentry integration (optional)

### Health Checks
```typescript
// src/routes/health/+server.ts
export async function GET() {
  try {
    // Test Supabase connection
    const { data, error } = await supabase.from('companies').select('count').limit(1);
    
    return new Response(JSON.stringify({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: error ? 'error' : 'connected'
    }), {
      headers: { 'content-type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      status: 'error',
      error: error.message
    }), {
      status: 500,
      headers: { 'content-type': 'application/json' }
    });
  }
}
```

## Security Configuration

### Headers Configuration
```javascript
// svelte.config.js
export default {
  kit: {
    adapter: adapter(),
    csp: {
      mode: 'auto',
      directives: {
        'script-src': ['self', 'https://hfzhvrknjgwtrkgyinjf.supabase.co'],
        'connect-src': ['self', 'https://hfzhvrknjgwtrkgyinjf.supabase.co']
      }
    }
  }
};
```

### Environment Security
- ✅ **No secrets in client bundle**: Only PUBLIC_ variables exposed
- ✅ **Server-side API keys**: SUPABASE_SERVICE_ROLE_KEY protected
- ✅ **HTTPS enforcement**: Automatic via Vercel
- ✅ **Content Security Policy**: Configured per app

## Troubleshooting

### Common Deployment Issues

#### 1. Build Failures
```bash
# Check build logs
vercel logs --follow

# Common fixes
npm run check  # Type checking
npm run build  # Local build test
```

#### 2. Environment Variables
```bash
# Verify environment variables
vercel env ls

# Add missing variables
vercel env add [VARIABLE_NAME]
```

#### 3. Dependency Issues
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Update dependencies
npm update
```

### Performance Issues
```bash
# Check bundle size
npm run build:analyze

# Optimize images
npx @vercel/nft trace

# Check lighthouse scores
npx lighthouse https://app.procureserve.com
```

## Cost Management

### Vercel Usage Tracking
- **Bandwidth**: 100GB/month (Pro plan)
- **Builds**: 6,000 build minutes/month
- **Serverless Functions**: 1M executions/month
- **Edge Functions**: 500,000 executions/month

### Optimization Strategies
1. **Static Asset Optimization**: Use Vercel Image Optimization
2. **Bundle Splitting**: Lazy load non-critical components
3. **Edge Caching**: Cache static content at edge locations
4. **Function Optimization**: Minimize cold start times

## Backup & Recovery

### Deployment Rollback
```bash
# List recent deployments
vercel list

# Rollback to previous deployment
vercel rollback [deployment-url]
```

### Configuration Backup
```bash
# Export Vercel settings
vercel env pull .env.vercel
vercel project ls > projects-backup.txt
```

---

**Last Updated**: June 20, 2025
**Vercel Plan**: Pro ($20/month)
**Status**: Production Ready
