## 📧 Secure Email Service Implementation (Resend API)

### 1. **Email Security Configuration**
```typescript
// packages/email-service/secure-email.ts
import { Resend } from 'resend';
import { z } from 'zod';

interface SecureEmailConfig {
  api_key: string;
  from_domain: string;
  dkim_enabled: boolean;
  spf_enabled: boolean;
  dmarc_policy: 'none' | 'quarantine' | 'reject';
  encryption_required: boolean;
  audit_logging: boolean;
}

const emailValidationSchema = z.object({
  to: z.string().email().max(255),
  subject: z.string().min(1).max(255),
  template_type: z.enum([
    'user_invitation',
    'password_reset', 
    'registration_approved',
    'security_alert',
    'data_export_ready'
  ]),
  variables: z.record(z.string()).refine(
    (data) => !Object.values(data).some(val => 
      typeof val === 'string' && /<script|javascript:/i.test(val)
    ),
    'Potentially malicious content in variables'
  )
});

export class SecureEmailService {
  private resend: Resend;
  private config: SecureEmailConfig;

  constructor(config: SecureEmailConfig) {
    this.config = config;
    this.resend = new Resend(config.api_key);
  }

  async sendSecureEmail(request: EmailSendRequest): Promise<EmailSendResult> {
    // Validate input
    const validation = emailValidationSchema.safeParse(request);
    if (!validation.success) {
      throw new Error(`Invalid email request: ${validation.error.message}`);
    }

    // Audit log the email request
    await this.auditEmailRequest(request);

    try {
      const result = await this.resend.emails.send({
        from: `${this.config.from_name} <noreply@${this.config.from_domain}>`,
        to: [request.to],
        subject: this.getSecureSubject(request.template_type, request.variables),
        html: this.getSecureHtmlTemplate(request.template_type, request.variables),
        text: this.getSecureTextTemplate(request.template_type, request.variables),
        headers: {
          'X-Priority': '1',
          'X-MSMail-Priority': 'High',
          'X-Mailer': 'ProcureServe-Secure-v2',
          'X-Security-Level': 'Enterprise'
        }
      });

      // Audit successful send
      await this.auditEmailResult(request, result);
      
      return {
        success: true,
        message_id: result.data?.id || `secure-${Date.now()}`,
        provider_used: 'resend',
        audit_trail_id: await this.createAuditTrail(request, result)
      };

    } catch (error) {
      // Audit failed send
      await this.auditEmailError(request, error);
      throw error;
    }
  }

  private async auditEmailRequest(request: EmailSendRequest): Promise<void> {
    // Log email request for compliance
    await logSecurityEvent({
      event_type: 'email_request',
      details: {
        recipient_domain: request.to.split('@')[1],
        template_type: request.template_type,
        timestamp: new Date().toISOString()
      },
      security_level: 'audit'
    });
  }

  private getSecureHtmlTemplate(type: string, variables: Record<string, any>): string {
    // Sanitize all variables
    const sanitizedVars = this.sanitizeTemplateVariables(variables);
    
    // Use pre-approved templates only
    const templates = {
      user_invitation: this.renderUserInvitationTemplate(sanitizedVars),
      password_reset: this.renderPasswordResetTemplate(sanitizedVars),
      security_alert: this.renderSecurityAlertTemplate(sanitizedVars)
    };

    const template = templates[type];
    if (!template) {
      throw new Error(`Unauthorized email template: ${type}`);
    }

    return this.addSecurityHeaders(template);
  }

  private sanitizeTemplateVariables(variables: Record<string, any>): Record<string, string> {
    const sanitized: Record<string, string> = {};
    
    for (const [key, value] of Object.entries(variables)) {
      if (typeof value === 'string') {
        // Remove potentially dangerous content
        sanitized[key] = value
          .replace(/<script[^>]*>.*?<\/script>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/data:text\/html/gi, '')
          .replace(/vbscript:/gi, '')
          .trim();
      } else {
        sanitized[key] = String(value);
      }
    }
    
    return sanitized;
  }

  private addSecurityHeaders(html: string): string {
    // Add CSP and other security measures to email HTML
    return `
      <!-- Security: This email is from ProcureServe -->
      <div style="display: none;">Security Token: ${this.generateSecurityToken()}</div>
      ${html}
      <!-- End Security -->
    `;
  }
}
```

---

## 🔐 Database Security Implementation

### 1. **Secure Migration Patterns**
```sql
-- Example: Secure user invitation system
-- Migration: 20250620000003_secure_user_invitations.sql

-- Create secure invitation tokens table
CREATE TABLE user_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('admin', 'recruiter', 'manager', 'viewer')),
  process_permissions TEXT[] NOT NULL DEFAULT '{}',
  
  -- Security fields
  token_hash TEXT NOT NULL UNIQUE, -- Store hashed token, never plaintext
  expires_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '7 days'),
  max_uses INTEGER NOT NULL DEFAULT 1,
  uses_count INTEGER NOT NULL DEFAULT 0,
  
  -- Audit fields
  invited_by UUID NOT NULL REFERENCES auth.users(id),
  invited_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  accepted_at TIMESTAMPTZ,
  revoked_at TIMESTAMPTZ,
  revoked_by UUID REFERENCES auth.users(id),
  
  -- Security constraints
  CONSTRAINT valid_expiry CHECK (expires_at > invited_at),
  CONSTRAINT valid_uses CHECK (uses_count <= max_uses),
  CONSTRAINT email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Row-level security
ALTER TABLE user_invitations ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see invitations for their company
CREATE POLICY "company_invitations_policy" ON user_invitations FOR ALL USING (
  company_id = get_user_company_id()
  AND has_permission('manage:users')
);

-- Policy: Console admins can see all invitations
CREATE POLICY "console_admin_invitations_policy" ON user_invitations FOR ALL USING (
  is_console_admin()
);

-- Audit trigger
CREATE TRIGGER audit_user_invitations_trigger
  AFTER INSERT OR UPDATE OR DELETE ON user_invitations
  FOR EACH ROW EXECUTE FUNCTION create_audit_log();

-- Security function: Hash invitation tokens
CREATE OR REPLACE FUNCTION hash_invitation_token(token TEXT)
RETURNS TEXT AS $$
BEGIN
  -- Use a secure hash function (SHA-256 minimum)
  RETURN encode(digest(token || current_setting('app.jwt_secret'), 'sha256'), 'hex');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 2. **Data Classification and Protection**
```sql
-- Create data classification system
CREATE TABLE data_classification (
  table_name TEXT PRIMARY KEY,
  classification TEXT NOT NULL CHECK (classification IN ('public', 'internal', 'confidential', 'restricted')),
  retention_period INTERVAL,
  encryption_required BOOLEAN NOT NULL DEFAULT false,
  pii_fields TEXT[] DEFAULT '{}',
  access_log_required BOOLEAN NOT NULL DEFAULT true,
  gdpr_applicable BOOLEAN NOT NULL DEFAULT true
);

-- Insert classifications for all tables
INSERT INTO data_classification VALUES
  ('companies', 'confidential', '7 years', true, ARRAY['tax_id', 'business_address'], true, true),
  ('users', 'confidential', '7 years', true, ARRAY['email', 'profile'], true, true),
  ('candidates', 'restricted', '7 years', true, ARRAY['email', 'name', 'phone', 'resume_text'], true, true),
  ('applications', 'confidential', '7 years', true, ARRAY['notes'], true, true),
  ('jobs', 'internal', '3 years', false, ARRAY[]::TEXT[], true, false);

-- Function to check data access permissions
CREATE OR REPLACE FUNCTION check_data_access(table_name TEXT, operation TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  classification TEXT;
  user_clearance TEXT;
BEGIN
  -- Get data classification
  SELECT dc.classification INTO classification
  FROM data_classification dc
  WHERE dc.table_name = check_data_access.table_name;

  -- Get user security clearance
  SELECT profile->>'security_clearance' INTO user_clearance
  FROM auth.users
  WHERE id = auth.uid();

  -- Implement access control logic
  RETURN CASE 
    WHEN classification = 'public' THEN true
    WHEN classification = 'internal' AND user_clearance IN ('internal', 'confidential', 'restricted') THEN true
    WHEN classification = 'confidential' AND user_clearance IN ('confidential', 'restricted') THEN true
    WHEN classification = 'restricted' AND user_clearance = 'restricted' THEN true
    ELSE false
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

---

## 🛡️ Security Testing Framework

### 1. **Automated Security Tests**
```typescript
// tests/security/auth-security.test.ts
import { test, expect } from '@playwright/test';
import { SecurityTestSuite } from '../utils/security-utils';

const securitySuite = new SecurityTestSuite();

test.describe('Authentication Security Tests', () => {
  
  test('should prevent SQL injection in login form', async ({ page }) => {
    await page.goto('/login');
    
    const maliciousInputs = [
      "'; DROP TABLE users; --",
      "admin' OR '1'='1",
      "' UNION SELECT * FROM users --"
    ];

    for (const input of maliciousInputs) {
      await page.fill('[data-testid="email"]', input);
      await page.fill('[data-testid="password"]', 'password');
      await page.click('[data-testid="login-button"]');
      
      // Should show error, not succeed or crash
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
      await expect(page.url()).not.toContain('/dashboard');
    }
  });

  test('should enforce rate limiting on login attempts', async ({ page }) => {
    await page.goto('/login');
    
    // Attempt multiple failed logins
    for (let i = 0; i < 6; i++) {
      await page.fill('[data-testid="email"]', '<EMAIL>');
      await page.fill('[data-testid="password"]', 'wrongpassword');
      await page.click('[data-testid="login-button"]');
      await page.waitForTimeout(100);
    }
    
    // Should be rate limited
    await expect(page.locator('[data-testid="rate-limit-message"]')).toBeVisible();
  });

  test('should prevent session fixation attacks', async ({ page, context }) => {
    // Get initial session
    await page.goto('/login');
    const initialCookies = await context.cookies();
    
    // Login successfully
    await securitySuite.loginAsTestUser(page);
    
    // Session should change after login
    const postLoginCookies = await context.cookies();
    const sessionCookie = postLoginCookies.find(c => c.name.includes('session'));
    const initialSessionCookie = initialCookies.find(c => c.name.includes('session'));
    
    expect(sessionCookie?.value).not.toBe(initialSessionCookie?.value);
  });

  test('should validate CSRF tokens on forms', async ({ page }) => {
    await securitySuite.loginAsTestUser(page);
    await page.goto('/profile');
    
    // Remove CSRF token and submit form
    await page.evaluate(() => {
      const csrfInput = document.querySelector('[name="csrfToken"]') as HTMLInputElement;
      if (csrfInput) csrfInput.value = 'invalid-token';
    });
    
    await page.fill('[data-testid="name"]', 'Updated Name');
    await page.click('[data-testid="save-button"]');
    
    // Should reject the request
    await expect(page.locator('[data-testid="csrf-error"]')).toBeVisible();
  });
});
```

### 2. **Data Protection Tests**
```typescript
// tests/security/data-protection.test.ts
test.describe('Data Protection Tests', () => {
  
  test('should encrypt sensitive data at rest', async ({ page }) => {
    await securitySuite.loginAsAdmin(page);
    
    // Create test data with sensitive information
    await page.goto('/candidates/new');
    await page.fill('[data-testid="ssn"]', '***********');
    await page.fill('[data-testid="salary"]', '75000');
    await page.click('[data-testid="save-button"]');
    
    // Verify data is encrypted in database
    const dbResult = await securitySuite.queryDatabase(
      'SELECT ssn, salary FROM candidates WHERE email = $1',
      ['<EMAIL>']
    );
    
    // Sensitive data should be encrypted, not plaintext
    expect(dbResult.ssn).not.toBe('***********');
    expect(dbResult.salary).not.toBe('75000');
    expect(dbResult.ssn).toMatch(/^[a-f0-9]{64}$/); // Encrypted format
  });

  test('should enforce company data isolation', async ({ page, context }) => {
    // Login as Company A user
    await securitySuite.loginAsUser(page, '<EMAIL>');
    
    // Try to access Company B data via direct URL manipulation
    await page.goto('/candidates');
    const companyAData = await page.locator('[data-testid="candidate-row"]').count();
    
    // Try to access Company B candidate ID directly
    await page.goto('/candidates/company-b-candidate-id');
    await expect(page.locator('[data-testid="unauthorized"]')).toBeVisible();
    
    // Verify no data leakage in API responses
    const response = await page.request.get('/api/candidates?company_id=company-b-id');
    expect(response.status()).toBe(403);
  });

  test('should implement proper session management', async ({ page, context }) => {
    await securitySuite.loginAsTestUser(page);
    
    // Session should expire after inactivity
    await page.waitForTimeout(31 * 60 * 1000); // 31 minutes
    await page.reload();
    
    // Should be redirected to login
    await expect(page.url()).toContain('/login');
    
    // Session should be invalidated on logout
    await securitySuite.loginAsTestUser(page);
    await page.click('[data-testid="logout-button"]');
    
    // Try to use back button
    await page.goBack();
    await expect(page.url()).toContain('/login');
  });
});
```

---

## 📋 Security Compliance Checklist

### Pre-Development Security Review
```typescript
interface SecurityReviewChecklist {
  threat_modeling: {
    completed: boolean;
    threats_identified: string[];
    mitigations_planned: string[];
    residual_risk_accepted: boolean;
  };
  
  data_classification: {
    pii_identified: boolean;
    classification_level: 'public' | 'internal' | 'confidential' | 'restricted';
    retention_policy_defined: boolean;
    encryption_requirements: string[];
  };
  
  authentication_design: {
    auth_method: 'password' | 'sso' | 'mfa';
    session_management: boolean;
    password_policy: boolean;
    account_lockout: boolean;
  };
  
  authorization_design: {
    rbac_implemented: boolean;
    principle_of_least_privilege: boolean;
    company_isolation: boolean;
    api_authorization: boolean;
  };
  
  input_validation: {
    client_side_validation: boolean;
    server_side_validation: boolean;
    sql_injection_prevention: boolean;
    xss_prevention: boolean;
  };
  
  audit_logging: {
    security_events_logged: boolean;
    user_actions_logged: boolean;
    admin_actions_logged: boolean;
    log_integrity_protected: boolean;
  };
  
  compliance_requirements: {
    gdpr_applicable: boolean;
    ccpa_applicable: boolean;
    soc2_controls: string[];
    industry_specific: string[];
  };
}
```

### Code Review Security Checklist
```typescript
// Security code review template
interface CodeSecurityReview {
  // Input validation
  input_validation_present: boolean;
  sql_injection_protected: boolean;
  xss_protection_implemented: boolean;
  
  // Authentication & Authorization
  authentication_required: boolean;
  authorization_checked: boolean;
  session_management_secure: boolean;
  
  // Data protection
  sensitive_data_encrypted: boolean;
  pii_handling_compliant: boolean;
  data_retention_enforced: boolean;
  
  // Error handling
  error_messages_safe: boolean;
  stack_traces_hidden: boolean;
  sensitive_data_not_logged: boolean;
  
  // Security headers
  csp_headers_present: boolean;
  security_headers_configured: boolean;
  https_enforced: boolean;
  
  // Dependencies
  dependencies_up_to_date: boolean;
  vulnerability_scan_passed: boolean;
  
  // Testing
  security_tests_written: boolean;
  penetration_test_passed: boolean;
}
```

---

## 🚨 Incident Response Plan

### 1. **Security Incident Classification**
```typescript
enum SecurityIncidentSeverity {
  LOW = 'low',           // Minor security finding
  MEDIUM = 'medium',     // Potential security issue
  HIGH = 'high',         // Active security threat
  CRITICAL = 'critical'  // Data breach or system compromise
}

interface SecurityIncident {
  id: string;
  severity: SecurityIncidentSeverity;
  type: 'data_breach' | 'unauthorized_access' | 'system_compromise' | 'malware' | 'ddos';
  description: string;
  affected_systems: string[];
  affected_data_types: string[];
  estimated_records_affected: number;
  discovery_time: Date;
  containment_time?: Date;
  resolution_time?: Date;
  root_cause?: string;
  lessons_learned?: string;
  
  // Legal requirements
  notification_required: boolean;
  regulatory_bodies: string[];
  customer_notification_required: boolean;
  law_enforcement_contacted: boolean;
}
```

### 2. **Automated Security Monitoring**
```typescript
// Real-time security monitoring
class SecurityMonitoringService {
  async monitorSecurityEvents() {
    // Monitor failed login attempts
    await this.checkFailedLogins();
    
    // Monitor unusual data access patterns
    await this.checkDataAccessPatterns();
    
    // Monitor for potential data exfiltration
    await this.checkDataExfiltration();
    
    // Monitor system performance for attacks
    await this.checkSystemHealth();
  }

  private async checkFailedLogins() {
    const recentFailures = await this.query(`
      SELECT user_email, COUNT(*) as attempts, 
             array_agg(DISTINCT ip_address) as ip_addresses
      FROM auth_logs 
      WHERE event_type = 'login_failed' 
        AND created_at > NOW() - INTERVAL '15 minutes'
      GROUP BY user_email
      HAVING COUNT(*) >= 5
    `);
    
    for (const failure of recentFailures) {
      await this.triggerSecurityAlert({
        type: 'brute_force_attempt',
        severity: 'high',
        details: failure
      });
    }
  }

  private async triggerSecurityAlert(alert: SecurityAlert) {
    // Send immediate notification to security team
    await this.sendSecurityNotification(alert);
    
    // Log to security information and event management (SIEM)
    await this.logToSIEM(alert);
    
    // Trigger automated response if configured
    await this.triggerAutomatedResponse(alert);
  }
}
```

---

**SECURITY REMINDER**: Every line of code in ProcureServe II must be written with security as the primary concern. When in doubt, choose the most secure option and document your security rationale. The cost of a security breach far exceeds the cost of implementing proper security controls.

**Emergency Security Contact**: <EMAIL>  
**Security Review Required**: Before any production deployment  
**Compliance Status**: GDPR/CCPA ready, SOC 2 preparation in progress
