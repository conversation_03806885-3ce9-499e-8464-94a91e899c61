# Supabase Cloud SMTP Configuration Guide

## Important: Manual Configuration Required

Since you're using Supabase Cloud, the SMTP configuration needs to be set up through the Supabase Dashboard, not through local config files.

## Step-by-Step Configuration

### 1. Access Supabase Dashboard
1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Sign in to your account
3. Select your project: **hfzhvrknjgwtrkgyinjf**

### 2. Configure SMTP Settings
1. Navigate to **Authentication** → **Settings**
2. Scroll down to **SMTP Settings**
3. Enable **Custom SMTP**
4. Enter the following configuration:

```
SMTP Host: smtp.zeptomail.in
SMTP Port: 587
SMTP User: emailapikey
SMTP Password: PHtE6r0EQLy/izMq8hhVtKS/QsCjZ9l69OtgJFJP49lFCqMATk0Hqd4jlGTjrBsoUKYTFPLIwY9h4unP5rjWJj3tZDkZCWqyqK3sx/VYSPOZsbq6x00ZtVwTdUHaV4PvctVu1iPTvt+X

Sender Name: ProcureServe Team
Sender Email: <EMAIL>
```

### 3. Enable Email Confirmations
1. In the same **Authentication Settings** page
2. Find **Email Confirmations**
3. Enable **Enable email confirmations**
4. Set **Confirm email change** as needed

### 4. Configure Email Templates (Optional)
1. Navigate to **Authentication** → **Email Templates**
2. Customize the templates for:
   - Confirm signup
   - Magic Link
   - Change Email Address
   - Reset Password

### 5. Test Configuration
After saving the SMTP settings:

1. Run the test script:
```bash
node scripts/test-auth-emails.js
```

2. Or test directly in the Supabase Dashboard:
   - Go to **Authentication** → **Users**
   - Try to send a password reset email to a test user

## Verification Steps

### 1. Check Email Delivery
- Register a new user through any of your apps
- Verify the confirmation email is received
- Check that the email comes from `<EMAIL>`

### 2. Test Password Reset
- Use the forgot password feature
- Verify the reset email is delivered promptly
- Confirm the reset link works correctly

### 3. Monitor Email Logs
- Check Supabase Dashboard logs for email delivery status
- Monitor ZeptoMail dashboard for delivery statistics
- Look for any bounce or spam reports

## Troubleshooting

### Common Issues

1. **"Error sending recovery email"**
   - SMTP credentials not saved in Supabase Dashboard
   - ZeptoMail API key is incorrect or expired
   - Sender domain not verified in ZeptoMail

2. **Emails not being delivered**
   - Check spam folder
   - Verify recipient email is valid
   - Check ZeptoMail delivery logs

3. **Authentication errors**
   - Verify the SMTP password is exactly the ZeptoMail API key
   - Ensure no extra spaces or characters in credentials
   - Check that the sender email domain is verified

### Debugging Steps

1. **Check Supabase Logs**:
   - Dashboard → Settings → Logs
   - Look for SMTP connection errors

2. **Verify ZeptoMail Configuration**:
   - Login to ZeptoMail console
   - Check domain verification status
   - Review delivery statistics

3. **Test SMTP Directly**:
   ```bash
   node scripts/test-email.js
   ```

## Security Notes

- The ZeptoMail API key is stored securely in Supabase Cloud
- Local environment variables are used for development only
- Production credentials are managed through Supabase Dashboard
- All email traffic is encrypted in transit

## Cost Monitoring

- ZeptoMail Free Tier: 10,000 emails/month
- Monitor usage in ZeptoMail dashboard
- Set up alerts for high usage
- Supabase includes email sending in base plan

## Next Steps

After successful configuration:

1. **Test User Registration**:
   - Create test accounts in all three apps
   - Verify confirmation emails are received

2. **Test Password Reset**:
   - Use forgot password functionality
   - Confirm reset emails work correctly

3. **Monitor Performance**:
   - Track email delivery rates
   - Monitor user feedback on email receipt
   - Set up alerting for failed deliveries

## Important Notes

⚠️ **Manual Configuration Required**: Unlike local Supabase, Cloud requires manual SMTP setup through the dashboard.

🔐 **Security**: Never commit SMTP credentials to version control. Use environment variables and Supabase Dashboard settings.

📧 **Domain Verification**: Ensure `procureserve.com` is verified in your ZeptoMail account for best deliverability.

🚀 **Production Ready**: This configuration is production-ready and handles all transactional emails for your multi-tenant application.
