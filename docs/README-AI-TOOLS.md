# PSII Project Overview for Claude & <PERSON> Tools - UPDATED

## ⚠️ CRITICAL SECURITY NOTICE
**This platform handles sensitive employment data. Any security breach could result in legal action against ProcureServe. All code must implement enterprise-grade security from day one.**

## Quick Reference Guide

### 🏗️ **Architecture Summary**
- **Backend**: Supabase Cloud ONLY (PostgreSQL 17.4 + Auth + Storage + Edge Functions)
- **Frontend**: 3 SvelteKit apps (Customer, Console, Talent)
- **Email**: Resend API ONLY (not Supabase email)
- **Hosting**: Vercel (3 domains)
- **AI**: pgvector + OpenAI integration
- **Region**: ap-south-1 (Asia Pacific - Mumbai)
- **Security**: Enterprise-grade with GDPR/CCPA compliance

### 📱 **Applications (3 Apps)**
1. **Customer App** (`app.procureserve.com:3004`) - External business customers
2. **Console App** (`console.procureserve.com:3008`) - Internal ProcureServe team
3. **Talent App** (`talent.procureserve.com:3006`) - Job seekers & consultants

### 🗄️ **Database Status**
- **Project ID**: `hfzhvrknjgwtrkgyinjf`
- **Status**: ACTIVE_HEALTHY
- **Environment**: Supabase Cloud ONLY (no local Supabase)
- **Migrations Applied**: 27 (latest: email_verification_system)
- **Key Tables**: companies (28 cols), users (14 cols), jobs (19 cols), candidates (22 cols)
- **Security**: Enterprise RLS on all tables, audit logging, encryption

### 🔧 **Development Workflow (Cloud Only)**
```bash
# NO LOCAL SUPABASE - Cloud only
npm run dev:customer      # Customer app (port 3004)
npm run dev:console       # Console app (port 3008)  
npm run dev:talent        # Talent app (port 3006)
```

### 💰 **Current Costs (Security-Enhanced)**
- **Supabase Pro**: $25/month (SOC 2 compliant)
- **Vercel Pro**: $20/month (Enterprise security)
- **Resend Pro**: $20/month (Enhanced deliverability)
- **Security Monitoring**: $10/month
- **Total**: ~$75/month (Security-enhanced infrastructure)

---

## 🎯 **Key Innovations**

### 1. **Configurable Enums System**
```sql
-- All dropdowns managed via console, no hardcoded values
INSERT INTO configurable_enums (company_id, category, values) VALUES 
('company-id', 'work_authorization_types', '[
  {"key": "us_citizen", "label": "US Citizen", "color": "#22c55e", "active": true},
  {"key": "h1b", "label": "H1B Visa", "color": "#f59e0b", "active": true}
]');
```

### 2. **Process-Based Access Control**
- Users can access `recruitment`, `bench_sales`, or both
- Dynamic UI based on permissions
- Company-level process enablement

### 3. **Multi-Provider Email System**
```typescript
// Automatic failover: Resend → Supabase → SMTP → Development
export class EmailManager {
  async send(request: EmailSendRequest) {
    for (const provider of this.providers) {
      const result = await provider.send(request, this.config);
      if (result.success) return result;
    }
  }
}
```

### 4. **AI-Powered Matching**
```sql
-- Semantic job/candidate matching with pgvector
SELECT jobs.*, 
       1 - (jobs.vector_embedding <=> $1::vector) as similarity
FROM jobs
WHERE company_id = $2
ORDER BY similarity DESC
LIMIT 10;
```

---

## 📁 **Project Structure**
```
PSII/
├── apps/                    # SvelteKit Applications
│   ├── customer-app/        # External customers
│   ├── console-app/         # Internal team
│   └── candidate-app/       # Job seekers
├── packages/                # Shared Libraries  
│   ├── shared-types/        # TypeScript interfaces
│   ├── shared-utils/        # Common utilities
│   ├── database-types/      # Supabase generated types
│   ├── email-service/       # Multi-provider email
│   └── notification-service/# Real-time notifications
├── supabase/               # Database & Backend
│   ├── migrations/         # Schema versions (27 total)
│   └── config.toml        # Local dev config
├── docs/                   # Documentation
│   ├── PSII-COMPLETE-KNOWLEDGE-BASE.md
│   ├── PSII-VERCEL-DEPLOYMENT-GUIDE.md
│   └── BUSINESS_REGISTRATION_FLOW.md
└── scripts/               # Utility scripts
```

---

## 🔐 **Security Model**

### Multi-Tenant Isolation
```sql
-- Every table scoped by company_id
CREATE POLICY "Company isolation" ON jobs FOR ALL USING (
  company_id = (SELECT company_id FROM users WHERE id = auth.uid())
);
```

### Role-Based Access
- **admin**: Full company access
- **recruiter**: Recruitment process only
- **manager**: Team oversight + recruitment/bench_sales
- **viewer**: Read-only access

### App Isolation
- Separate authentication contexts
- No cross-app session bleeding
- Audit logging for all actions

---

## 📧 **Email Configuration**

### Provider Priority
1. **Resend** (Primary) - 99%+ deliverability, 3K free emails/month
2. **Supabase Auth** (Fallback) - For auth-related emails
3. **SMTP** (Custom) - Gmail, custom servers
4. **Development** (Local) - Console logging

### Built-in Templates
```typescript
Templates: {
  user_invitation: "Account activation for approved businesses",
  password_reset: "Secure password reset links", 
  registration_submitted: "Business registration confirmation",
  registration_approved: "Welcome with activation",
  registration_rejected: "Feedback for improvements",
  welcome: "Onboarding sequence",
  notification: "General system notifications"
}
```

---

## 🚀 **Deployment Strategy**

### Vercel Configuration
```javascript
// All apps use Vercel adapter
adapter: adapter({
  runtime: 'nodejs18.x',
  memory: 1024,
  maxDuration: 30
})
```

### Domain Mapping
- `app.procureserve.com` → Customer App
- `console.procureserve.com` → Console App  
- `candidate.procureserve.com` → Candidate App

### Auto-Deployment
- **main branch** → Production
- **develop branch** → Preview
- **feature/* branches** → Branch previews

---

## 🎯 **Business Processes**

### 1. Business Registration Flow
```mermaid
graph LR
    A[Lead Capture] --> B[Domain Detection]
    B --> C{New vs Existing}
    C -->|New| D[Console Review]
    C -->|Existing| E[Company Admin Approval]
    D --> F[Company Creation]
    E --> F
    F --> G[User Activation]
```

### 2. Recruitment Process
1. **Job Creation**: Multi-location, compensation tiers
2. **Candidate Sourcing**: AI-powered matching
3. **Application Management**: Status tracking  
4. **Interview Scheduling**: Calendar integration
5. **Placement**: Offer management

### 3. Bench Sales Process
1. **Consultant Onboarding**: Profile creation
2. **Availability Management**: Bench status
3. **Client Matching**: Skill-based pairing
4. **Placement Tracking**: Revenue management

---

## 📊 **Current Implementation Status**

### ✅ **Completed (90%)**
- [x] Database schema (27 migrations)
- [x] Multi-tenant security (RLS)
- [x] Email service (Resend integration)
- [x] Business registration flow
- [x] User invitation system
- [x] Process permissions
- [x] Configurable enums
- [x] Multi-location jobs
- [x] Audit logging

### 🔄 **In Progress (10%)**
- [ ] Authentication hooks (hooks.server.ts)
- [ ] Core UI components
- [ ] Dashboard development

### 📋 **Pending (Phase 2)**
- [ ] Jobs management interface
- [ ] Candidate management
- [ ] Application workflow
- [ ] Interview scheduling
- [ ] AI-powered matching
- [ ] Resume parsing
- [ ] Analytics dashboard

---

## 🛠️ **Development Best Practices**

### Component Architecture
```typescript
// Maximum 50 lines per component for token efficiency
<script lang="ts">
  import type { Job } from '@psii/shared-types';
  export let job: Job;
  export let onApply: (jobId: string) => void;
</script>

<div class="job-card">
  <h3>{job.title}</h3>
  <p>{job.description}</p>
  <button on:click={() => onApply(job.id)}>Apply</button>
</div>
```

### Database Best Practices
- **Indexing**: Optimized for query patterns
- **Migrations**: Atomic, reversible changes
- **Policies**: Least privilege access
- **Performance**: Query optimization with EXPLAIN

---

## 🔍 **Troubleshooting Guide**

### Common Issues
| Issue | Check | Solution |
|-------|-------|----------|
| **Auth Errors** | JWT token expiry | Refresh session |
| **RLS Violations** | company_id context | Verify user context |
| **Migration Failures** | Dependency order | Review migration sequence |
| **Email Delivery** | Resend API key | Check environment variables |
| **Build Failures** | TypeScript errors | Run `npm run check` |

### Debug Tools
- **Supabase Studio**: Database inspection
- **Vercel Logs**: Application debugging  
- **Browser DevTools**: Client-side issues
- **Playwright Tests**: E2E testing

---

## 📚 **Documentation Index**

1. **[PSII-COMPLETE-KNOWLEDGE-BASE.md](./PSII-COMPLETE-KNOWLEDGE-BASE.md)** - Comprehensive project guide
2. **[PSII-VERCEL-DEPLOYMENT-GUIDE.md](./PSII-VERCEL-DEPLOYMENT-GUIDE.md)** - Deployment configuration
3. **[BUSINESS_REGISTRATION_FLOW.md](./BUSINESS_REGISTRATION_FLOW.md)** - Domain detection system
4. **[SECURITY_ISOLATION_IMPLEMENTATION.md](./SECURITY_ISOLATION_IMPLEMENTATION.md)** - Security details

---

## 🎯 **AI Tool Guidelines**

### When Working on PSII:
1. **Always reference** this documentation first
2. **Respect the 50-line component limit** for token efficiency
3. **Use Supabase cloud** (Project ID: hfzhvrknjgwtrkgyinjf)
4. **Test with all 3 apps** running simultaneously
5. **Prioritize open-source solutions** over paid APIs
6. **Follow the cost-conscious approach** (~$45/month target)
7. **Maintain security-first implementation**
8. **Use modular, well-documented code**

### Environment Setup for AI Tools:
```bash
# Required for development
PUBLIC_SUPABASE_URL=https://hfzhvrknjgwt### ⚠️ **Critical Compliance Requirements**

#### GDPR Compliance (Mandatory)
- **Data Minimization**: Collect only necessary data
- **Right to Erasure**: Implement data deletion workflows
- **Data Portability**: Export user data in standard formats
- **Consent Management**: Granular privacy controls
- **Breach Notification**: 72-hour reporting procedures

#### Enterprise Security Standards
- **Multi-Factor Authentication**: Required for admin users
- **Session Management**: Secure JWT with rotation
- **Rate Limiting**: Prevent brute force attacks
- **Audit Logging**: Immutable security event logs
- **Data Encryption**: AES-256 for PII at rest

### 🧪 **Testing Requirements**

#### Security Testing (Mandatory)
```typescript
// Every feature must include security tests
interface SecurityTestSuite {
  input_validation_tests: boolean;
  authentication_bypass_tests: boolean;
  authorization_escalation_tests: boolean;
  sql_injection_tests: boolean;
  xss_prevention_tests: boolean;
  csrf_protection_tests: boolean;
  session_management_tests: boolean;
}
```

#### End-to-End Testing
- **Customer App**: Business user workflows
- **Console App**: Admin operations and security
- **Talent App**: Candidate privacy and data protection

### 📁 **Project Organization Rules**

#### File Structure Requirements
```
PSII/
├── docs/                    # ALL documentation here
│   ├── security/           # Security-specific docs
│   ├── compliance/         # GDPR, CCPA, SOC 2
│   └── api/               # API documentation
├── scripts/                # ALL utility scripts here
│   ├── security/          # Security validation
│   ├── testing/           # Test automation
│   └── deployment/        # Deployment scripts
├── tests/                  # Comprehensive test suite
│   ├── security/          # Security tests
│   ├── unit/              # Unit tests
│   ├── integration/       # Integration tests
│   └── e2e/               # End-to-end tests
```

#### Code Quality Standards
- **Maximum 50 lines per component** (token efficiency)
- **Strict TypeScript** (no `any` types)
- **Comprehensive error handling** (no sensitive data exposure)
- **Security code review** required for all changes

### 🚨 **Security Incident Response**

#### Immediate Actions Required
1. **Isolate affected systems**
2. **Preserve evidence**
3. **Notify security team**: <EMAIL>
4. **Document incident timeline**
5. **Assess data impact**

#### Severity Levels
- **CRITICAL**: Data breach, system compromise
- **HIGH**: Active security threat
- **MEDIUM**: Potential security issue  
- **LOW**: Minor security finding

---

**FINAL REMINDER**: ProcureServe II handles sensitive employment and personal data. Every development decision must prioritize security, compliance, and data protection. Legal action could result from security failures.

**Security Contact**: <EMAIL>  
**Last Updated**: June 20, 2025  
**Security Review**: Required before production deployment  
**Compliance Status**: GDPR/CCPA ready, SOC 2 in progress
