# Business Registration Domain Detection System
## ProcureServe II Platform Overview

### Executive Summary
The Business Registration Domain Detection System automatically identifies whether new business registrations belong to existing customers or represent entirely new companies. This system enables intelligent approval workflows and ensures proper business relationship management across the ProcureServe platform.

---

## System Architecture Overview

### Platform Structure
- **Customer App** (Port 3004): Business user registration and management
- **Console App** (Port 3008): Internal ProcureServe staff administration
- **Candidate App** (Port 3006): Job seeker platform
- **Shared Database**: Cloud Supabase with cross-app security isolation

### Security Model
✅ **App Isolation**: Each application maintains separate authentication contexts
✅ **Session Security**: Cross-app session bleeding prevention implemented
✅ **Data Protection**: Role-based access control with audit logging

---

## Domain Detection Workflow

### 1. Registration Initiation Flow

```mermaid
graph TD
    A[Business User Visits Customer App] --> B[Starts Registration Process]
    B --> C[Enters Email Address]
    C --> D[System Extracts Domain]
    D --> E{Domain Detection Check}
    E -->|New Company| F[Flag as New Company]
    E -->|Existing Customer| G[Flag as Existing Customer]
    F --> H[Console Admin Approval Required]
    G --> I[Company Admin Approval Required]
    H --> J[Send Console Notification]
    I --> K[Send Company Admin Email]
    J --> L[Registration Pending]
    K --> L
```

### 2. Domain Detection Logic

```mermaid
graph LR
    A[Extract Email Domain] --> B[Normalize Domain]
    B --> C[Query Companies Table]
    C --> D{Approved Company<br/>with Domain Exists?}
    D -->|Yes| E[EXISTING_CUSTOMER]
    D -->|No| F[NEW_COMPANY]
    E --> G[Set Company Reference]
    F --> H[No Company Reference]
    G --> I[Route to Company Admin]
    H --> J[Route to Console Admin]
```

---

## Approval Authority Matrix

### Decision Tree for Approval Authority

```mermaid
graph TD
    A[New Business Registration] --> B{Domain Detection Result}
    B -->|NEW_COMPANY| C[Console Admin Authority]
    B -->|EXISTING_CUSTOMER| D[Company Admin Authority]
    
    C --> E[Internal Review Process]
    E --> F{Console Decision}
    F -->|Approve| G[Create New Company]
    F -->|Reject| H[Registration Denied]
    
    D --> I[Company Admin Notification]
    I --> J{Company Admin Response}
    J -->|Approve in 3 days| K[User Activated]
    J -->|Reject| L[Registration Denied]
    J -->|No Response| M[Escalate to Console]
    
    M --> N[Console Override Decision]
    N --> O{Console Override}
    O -->|Approve| K
    O -->|Reject| L
    O -->|No Action for 14 days| P[Auto-Deny Registration]
```

---

## Notification & Communication Flow

### Multi-Channel Notification System

```mermaid
graph TD
    A[Registration Submitted] --> B{Registration Type}
    
    B -->|New Company| C[Console Dashboard Flag]
    B -->|Existing Customer| D[Company Admin Email]
    
    C --> E[Internal Slack/Teams Alert]
    C --> F[Email to Console Admins]
    
    D --> G[Primary Admin Email]
    D --> H[Backup Admin Email]
    D --> I[Console Dashboard Backup Flag]
    
    G --> J{Email Delivered?}
    J -->|No| K[Retry Email Delivery]
    J -->|Yes| L[Track Response Time]
    
    K --> M{Max Retries Reached?}
    M -->|Yes| N[Fallback to Console Alert]
    M -->|No| G
```

### Escalation Timeline

```mermaid
gantt
    title Registration Approval Timeline
    dateFormat X
    axisFormat %d days
    
    section Company Admin
    Initial Email    :0, 1
    Reminder Email   :2, 3
    Final Notice     :3, 4
    
    section Console Escalation
    Console Alert    :3, 4
    Console Review   :4, 10
    Auto-Deny        :14, 15
```

---

## Business Process Workflows

### New Company Registration Process

```mermaid
sequenceDiagram
    participant BU as Business User
    participant CA as Customer App
    participant DB as Database
    participant CO as Console App
    participant Admin as Console Admin
    
    BU->>CA: Submit Registration
    CA->>DB: Extract & Check Domain
    DB-->>CA: Domain Not Found
    CA->>DB: Create Registration Record
    CA->>CO: Create Console Alert
    CO->>Admin: Dashboard Notification
    
    Admin->>CO: Review Registration
    alt Approve Registration
        Admin->>DB: Create New Company
        Admin->>CA: Approve Registration
        CA->>BU: Send Activation Email
    else Reject Registration
        Admin->>DB: Update Status
        CA->>BU: Send Rejection Email
    end
```

### Existing Customer Registration Process

```mermaid
sequenceDiagram
    participant BU as Business User
    participant CA as Customer App
    participant DB as Database
    participant EM as Email Service
    participant CompAdmin as Company Admin
    participant Console as Console App
    
    BU->>CA: Submit Registration
    CA->>DB: Extract & Check Domain
    DB-->>CA: Domain Found (Company ID: 123)
    CA->>DB: Create Registration Record
    CA->>EM: Send Company Admin Email
    EM->>CompAdmin: Approval Request Email
    
    alt Company Admin Approves (within 3 days)
        CompAdmin->>CA: Approve via Email Link
        CA->>DB: Update Registration Status
        CA->>BU: Send Activation Email
    else No Response (after 3 days)
        CA->>Console: Escalate to Console
        Console->>DB: Flag for Console Review
    else Company Admin Rejects
        CompAdmin->>CA: Reject via Email Link
        CA->>BU: Send Rejection Email
    end
```

---

## Data Flow Architecture

### Registration Data Processing

```mermaid
graph TB
    subgraph "Customer App"
        A[Registration Form] --> B[Domain Extraction]
        B --> C[Validation Engine]
    end
    
    subgraph "Database Layer"
        D[Business Leads Table]
        E[Companies Table]
        F[Domain Lookup Cache]
    end
    
    subgraph "Console App"
        G[Admin Dashboard]
        H[Approval Interface]
        I[Company Management]
    end
    
    subgraph "Email Service"
        J[Notification Engine]
        K[Template System]
        L[Delivery Tracking]
    end
    
    C --> D
    C --> E
    C --> F
    
    D --> G
    G --> H
    H --> I
    
    D --> J
    J --> K
    K --> L
```

---

## Security & Risk Management

### Security Controls Matrix

| Risk Category | Control Measure | Implementation |
|---------------|----------------|----------------|
| **Domain Spoofing** | Exact match only | No fuzzy domain matching |
| **Cross-App Access** | Session isolation | App-specific authentication |
| **Data Integrity** | Transaction safety | Database constraints & triggers |
| **Audit Trail** | Complete logging | All actions logged with timestamps |
| **Email Fraud** | Domain validation | Business email verification |

### Risk Mitigation Flow

```mermaid
graph TD
    A[Potential Risk Detected] --> B{Risk Category}
    
    B -->|Domain Spoofing| C[Block Registration]
    B -->|Cross-App Access| D[Terminate Session]
    B -->|Data Corruption| E[Rollback Transaction]
    B -->|Email Fraud| F[Flag for Manual Review]
    
    C --> G[Log Security Event]
    D --> G
    E --> G
    F --> G
    
    G --> H[Alert Security Team]
    H --> I[Investigate & Respond]
```

---

## Performance & Scalability

### System Performance Targets

| Metric | Target | Monitoring |
|--------|--------|------------|
| **Domain Detection** | < 100ms | Real-time alerts |
| **Email Delivery** | > 99% success | Retry mechanisms |
| **Approval Time** | < 5 days average | Escalation triggers |
| **Database Response** | < 50ms queries | Performance monitoring |

### Load Distribution

```mermaid
graph LR
    subgraph "Traffic Distribution"
        A[Customer Registrations] --> B[Domain Detection]
        B --> C{Load Balancer}
        C --> D[Primary Database]
        C --> E[Read Replica]
        C --> F[Cache Layer]
    end
    
    subgraph "Background Processing"
        G[Email Queue] --> H[Notification Service]
        H --> I[Delivery Tracking]
        I --> J[Retry Logic]
    end
```

---

## Monitoring & Analytics

### Key Performance Indicators

```mermaid
graph TD
    A[Business Registration KPIs] --> B[Volume Metrics]
    A --> C[Quality Metrics]
    A --> D[Performance Metrics]
    
    B --> E[Daily Registrations]
    B --> F[Domain Distribution]
    B --> G[Approval Rates]
    
    C --> H[Detection Accuracy]
    C --> I[Email Delivery Success]
    C --> J[False Positive Rate]
    
    D --> K[Response Times]
    D --> L[System Availability]
    D --> M[Processing Throughput]
```

### Alert Configuration

| Alert Level | Trigger | Response Time |
|-------------|---------|---------------|
| **Critical** | System down, Data corruption | Immediate |
| **High** | Email failures > 5% | 15 minutes |
| **Medium** | Approval delays > 7 days | 1 hour |
| **Low** | Performance degradation | 4 hours |

---

## Implementation Phases

### Development Roadmap

```mermaid
gantt
    title Implementation Timeline
    dateFormat YYYY-MM-DD
    section Phase 1: Core MVP
    Database Schema       :2024-01-01, 3d
    Domain Detection      :2024-01-04, 4d
    Basic Notifications   :2024-01-08, 3d
    Console Integration   :2024-01-11, 4d
    
    section Phase 2: Reliability
    Email Retry Logic     :2024-01-15, 3d
    Error Handling        :2024-01-18, 3d
    Performance Testing   :2024-01-21, 2d
    
    section Phase 3: Scale & Polish
    Caching Layer         :2024-01-23, 4d
    Advanced Analytics    :2024-01-27, 3d
    UI/UX Enhancements    :2024-01-30, 3d
```

---

## Success Metrics & Validation

### Business Success Criteria

| Objective | Metric | Target | Validation Method |
|-----------|--------|--------|-------------------|
| **Accuracy** | Domain detection precision | 100% | Manual audit sampling |
| **Efficiency** | Average approval time | < 5 days | Automated tracking |
| **Reliability** | Email delivery rate | > 99% | Service monitoring |
| **Security** | Cross-app breach attempts | 0 successful | Security logs |

### Operational Readiness Checklist

- [ ] Database schema verified and tested
- [ ] Domain detection algorithm validated
- [ ] Email notification system tested
- [ ] Console dashboard integration complete
- [ ] Security controls verified
- [ ] Performance benchmarks met
- [ ] Monitoring and alerting configured
- [ ] Documentation and training completed

---

## Conclusion

The Business Registration Domain Detection System provides intelligent, automated business relationship management while maintaining security and operational efficiency. The system's design prioritizes accuracy, scalability, and user experience while providing comprehensive administrative oversight and control.

**Key Benefits:**
- Automated business relationship detection
- Intelligent approval routing
- Comprehensive audit trail
- Scalable architecture
- Enhanced security controls
- Streamlined administrative workflows 