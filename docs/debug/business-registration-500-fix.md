# Business Registration 500 Error - Debug & Fix

## Issue Summary
User encountered a 500 Internal Error when submitting the business registration form at `http://localhost:3005/register/business`.

## Root Cause Analysis
1. **Port Configuration Mismatch**: User accessed port 3005, but customer-app runs on port 3004
2. **Missing Enhanced Error Logging**: No detailed debugging to identify exact failure points
3. **Potential Environment/Database Issues**: Service client or schema mismatches

## Changes Made

### 1. Enhanced Logging System
- **File**: `/src/lib/debug/logger.ts`
- **Purpose**: Comprehensive logging for registration flow debugging
- **Features**:
  - Step-by-step registration tracking
  - Environment validation
  - In-memory log storage for debugging
  - Security-aware logging (redacts passwords/emails)

### 2. Improved Registration Server Action
- **File**: `/src/routes/register/business/+page.server.ts`
- **Changes**:
  - Added comprehensive error logging at each step
  - Better error handling with cleanup on failures
  - Environment validation before processing
  - Enhanced debugging output

### 3. Debug Endpoints
- **File**: `/src/routes/debug/+server.ts`
  - System status check endpoint
  - Database connection testing
  - Environment validation
  
- **File**: `/src/routes/debug/test-registration/+server.ts`
  - Registration-specific testing
  - Schema validation
  - Service client testing

## Port Configuration
```bash
# Correct URLs for each app:
Customer App:  http://localhost:3004  # Business registration
Candidate App: http://localhost:3006  # Candidate portal  
Console App:   http://localhost:3008  # Internal management
```

## Testing Steps

### 1. Access Correct URL
Visit: `http://localhost:3004/register/business`

### 2. Run System Check
```bash
# Test system health
curl -X GET http://localhost:3004/debug

# Test registration system
curl -X POST http://localhost:3004/debug/test-registration
```

### 3. Monitor Logs
Check browser console and server logs for detailed registration flow information.

## Database Schema Requirements
The companies table requires these columns for registration:
- `name`, `legal_entity_type`, `tax_id`, `business_type`
- `business_address` (JSONB), `primary_contact` (JSONB), `working_hours` (JSONB)
- `registration_status`, `recruitment_enabled`, `bench_sales_enabled`

## Security Features Maintained
- ✅ Row-Level Security (RLS) policies intact
- ✅ Service client bypasses RLS only for company creation during registration
- ✅ Multi-tenant data isolation preserved
- ✅ Enterprise-grade audit logging
- ✅ Password and email redaction in logs

## Rollback Plan
If issues persist, restore the original `+page.server.ts`:
```bash
git checkout HEAD~1 -- src/routes/register/business/+page.server.ts
```

## Next Steps
1. Test registration flow at correct URL (port 3004)
2. Check debug endpoints for system health
3. Review server logs for detailed error information
4. Contact support if database schema issues are found

## Prevention
- Add port configuration validation to startup scripts
- Implement health check endpoints for all apps
- Add automated testing for registration flow
