# Console App Setup Guide

## Overview

The ProcureServe II Console App implements a secure, environment-variable based initial setup system to bootstrap the first super admin user. This approach eliminates hardcoded credentials while maintaining enterprise-grade security.

## Setup Architecture

### Core Components

1. **SetupManager** (`/src/lib/server/auth/setup-manager.ts`)
   - Manages initial setup logic
   - Validates setup configuration
   - Creates first super admin user

2. **Setup Route** (`/src/routes/setup/`)
   - Secure setup page with token validation
   - Form handling for admin creation
   - Auto-redirects when setup complete

3. **Environment Configuration**
   - `ENABLE_INITIAL_SETUP`: Enables/disables setup
   - `INITIAL_ADMIN_EMAIL`: Email for first admin
   - `INITIAL_ADMIN_SETUP_TOKEN`: Secret token for setup URL

4. **Security Integration**
   - Full audit logging in `console_security_events`
   - Rate limiting and input validation
   - Auto-disable after completion

## Setup Process

### Phase 1: Initial Configuration

1. **Configure Environment Variables**
   ```bash
   # In .env.local
   ENABLE_INITIAL_SETUP=true
   INITIAL_ADMIN_EMAIL=<EMAIL>
   INITIAL_ADMIN_SETUP_TOKEN=ps2-setup-super-secret-token-2025
   ```

2. **Check Setup Status**
   ```bash
   cd apps/console-app
   npm run setup:check
   ```

### Phase 2: Admin Creation

1. **Access Setup URL**
   - URL: `http://localhost:3008/setup?token=ps2-setup-super-secret-token-2025`
   - Token validation enforced
   - Email pre-filled from configuration

2. **Create Admin Account**
   - Email validated against `INITIAL_ADMIN_EMAIL`
   - Password strength requirements (8+ characters)
   - Automatic role assignment as `super_admin`

3. **Permission Assignment**
   - Full permissions across all resources
   - Access to all companies
   - Ability to invite other users

### Phase 3: Security Lockdown

1. **Disable Setup**
   ```bash
   # Update .env.local
   ENABLE_INITIAL_SETUP=false
   # Remove or comment out setup tokens
   ```

2. **Remove Sensitive Variables**
   ```bash
   # Optional: Remove these after setup
   # INITIAL_ADMIN_EMAIL=<EMAIL>
   # INITIAL_ADMIN_SETUP_TOKEN=ps2-setup-super-secret-token-2025
   ```

## Security Features

### Token-Based Access Control
- Setup URL requires exact token match
- Tokens should be cryptographically secure
- One-time use (setup self-destructs)

### Email Validation
- Admin email must match environment configuration
- Prevents unauthorized account creation
- Supports company-specific domains

### Audit Trail
- All setup attempts logged to `console_security_events`
- Failed attempts tracked with IP addresses
- Success events include metadata

### Auto-Protection
- Setup automatically disabled after first admin created
- Invalid token attempts blocked and logged
- Redirect protection for completed setup

## Environment Configurations

### Development Setup
```bash
ENABLE_INITIAL_SETUP=true
INITIAL_ADMIN_EMAIL=<EMAIL>
INITIAL_ADMIN_SETUP_TOKEN=dev-setup-token-123
```

### Staging Setup
```bash
ENABLE_INITIAL_SETUP=true
INITIAL_ADMIN_EMAIL=<EMAIL>
INITIAL_ADMIN_SETUP_TOKEN=staging-super-secret-token-456
```

### Production Setup
```bash
ENABLE_INITIAL_SETUP=true
INITIAL_ADMIN_EMAIL=<EMAIL>
INITIAL_ADMIN_SETUP_TOKEN=prod-ultra-secure-token-789
```

## Administrative Commands

### Check Setup Status
```bash
npm run setup:check
```
- Shows current setup state
- Lists existing super admins
- Provides setup URL if needed

### Database Queries
```sql
-- Check existing super admins
SELECT id, email, role, created_at, is_active 
FROM console_users 
WHERE role = 'super_admin' AND is_active = true;

-- View setup security events
SELECT event_type, user_email, success, error_message, timestamp
FROM console_security_events 
WHERE event_type IN ('initial_setup_completed', 'setup_failed')
ORDER BY timestamp DESC;
```

## Troubleshooting

### Common Issues

1. **Setup URL Not Working**
   - Verify `ENABLE_INITIAL_SETUP=true`
   - Check token in URL matches environment
   - Ensure no existing super admins

2. **Token Validation Failed**
   - Verify exact token match (case-sensitive)
   - Check for URL encoding issues
   - Ensure environment variables loaded

3. **Email Mismatch Error**
   - Verify `INITIAL_ADMIN_EMAIL` in environment
   - Check for typos or case sensitivity
   - Ensure email format is valid

4. **Database Connection Issues**
   - Verify Supabase configuration
   - Check service role key permissions
   - Ensure console_users table exists

### Reset Setup Process

If you need to restart the setup process:

1. **Delete Existing Admin** (Development Only)
   ```sql
   DELETE FROM console_users WHERE role = 'super_admin';
   DELETE FROM console_user_permissions WHERE user_id IN (
     SELECT id FROM auth.users WHERE email = '<EMAIL>'
   );
   ```

2. **Re-enable Setup**
   ```bash
   ENABLE_INITIAL_SETUP=true
   ```

3. **Generate New Token**
   ```bash
   INITIAL_ADMIN_SETUP_TOKEN=new-secure-token-$(date +%s)
   ```

## Security Best Practices

### Token Management
- Use cryptographically secure random tokens
- Change tokens between environments
- Never commit tokens to version control
- Rotate tokens periodically in production

### Environment Security
- Use separate `.env.local` files per environment
- Never expose setup tokens in logs
- Remove setup configuration after completion
- Monitor setup attempts in production

### Access Control
- Limit setup URL access to secure networks
- Use HTTPS in production environments
- Implement additional IP restrictions if needed
- Monitor for suspicious setup attempts

## Integration Points

### Authentication Flow
- Setup bypasses normal auth requirements
- Hooks.server.ts handles setup route protection
- Auto-redirect to login after completion

### Permission System
- Super admin gets all permissions automatically
- Permissions stored in `console_user_permissions`
- Role hierarchy enforced in auth manager

### Audit System
- All setup events logged to `console_security_events`
- Security event types include setup-specific events
- Audit trail preserved permanently

## Maintenance

### Regular Tasks
- Monitor setup attempt logs
- Verify setup is disabled in production
- Update tokens during security reviews
- Check for orphaned setup configurations

### Security Reviews
- Audit setup logs quarterly
- Review environment variable security
- Test setup process in staging
- Validate permission assignments