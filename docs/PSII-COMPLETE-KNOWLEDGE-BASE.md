# ProcureServe II (PSII) - Complete Knowledge Base

## Project Overview
ProcureServe II is a next-generation enterprise staffing platform completely rebuilt with a Supabase-first architecture. This document serves as the comprehensive guide for AI tools, developers, and stakeholders to understand the project structure, technologies, and current implementation status.

## Core Architecture Decisions

### Why Supabase-First?
The previous ProcureServe implementation faced several challenges:
- **Circular authentication issues** causing development complexity
- **Hardcoded enums** for work authorization, job statuses, etc.
- **Large codebase** causing context window problems for AI tools
- **Confusion between internal vs customer users**
- **High complexity** in maintaining custom FastAPI backend

### Solution: Complete Architectural Pivot
- ✅ **Eliminate FastAPI backend** → Use Supabase Edge Functions
- ✅ **Resolve circular auth** → Use Supabase Auth with JWT tokens
- ✅ **Separate applications** → Clear user separation (customer vs internal)
- ✅ **Configurable enums** → All dropdowns manageable via console interface
- ✅ **Modular components** → <50 lines each for token efficiency

## Project Structure

```
/Users/<USER>/Desktop/PSII/
├── apps/                          # Three SvelteKit Applications
│   ├── customer-app/              # app.procureserve.com (External customers)
│   ├── console-app/               # console.procureserve.com (Internal ProcureServe team)
│   └── candidate-app/             # candidate.procureserve.com (Job seekers)
├── packages/                      # Shared Libraries
│   ├── shared-types/              # TypeScript interfaces & types
│   ├── shared-utils/              # Common utility functions
│   ├── database-types/            # Supabase generated types
│   ├── email-service/             # Resend-based email service
│   └── notification-service/      # Real-time notifications
├── supabase/                      # Backend Infrastructure
│   ├── migrations/                # Database schema versions
│   ├── seed.sql                   # Initial data seeding
│   └── supabase/config.toml       # Local development config
├── docs/                          # Documentation
├── scripts/                       # Utility scripts
└── tests/                         # Playwright tests
```

## Technology Stack

### Backend (Supabase Cloud)
- **Database**: PostgreSQL 17.4 with pgvector for AI embeddings
- **Authentication**: Supabase Auth with JWT tokens
- **Storage**: Supabase Storage for files/resumes
- **Real-time**: Supabase Realtime for live updates
- **Edge Functions**: Deno-based serverless functions
- **Region**: Asia Pacific (Mumbai) - ap-south-1

### Frontend (SvelteKit)
- **Framework**: SvelteKit with TypeScript
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: Svelte stores
- **Routing**: SvelteKit's file-based routing
- **Build Tool**: Vite

### Hosting & Deployment
- **Frontend**: Vercel (automatic deployments)
- **Backend**: Supabase Cloud
- **Email**: Resend API (not Supabase email)
- **SSL**: Automatic HTTPS via Vercel + Supabase

### AI & Search
- **Vector Database**: pgvector for semantic search
- **AI Integration**: OpenAI API for resume parsing, job matching
- **Search**: Full-text search + semantic similarity

## Current Implementation Status

### ✅ Completed Components

#### Database Schema (Latest: 27 migrations)
1. **Core Tables**: companies, users, configurable_enums
2. **Jobs Module**: jobs, job_locations, job_compensation, job_custom_fields
3. **Candidates Module**: candidates, applications, interviews
4. **Business Registration**: business_registrations, business_documents
5. **Notifications**: notifications, notification_types, user_notification_preferences
6. **Audit Logging**: activity_logs, company_audit_logs
7. **Multi-tenant Security**: Row-Level Security policies

#### Key Innovations
1. **Configurable Enums System**:
   ```sql
   -- Example: Work authorization managed via console
   INSERT INTO configurable_enums (company_id, category, values) VALUES 
   ('company-id', 'work_authorization_types', '[
     {"key": "us_citizen", "label": "US Citizen", "color": "#22c55e", "active": true},
     {"key": "h1b", "label": "H1B Visa", "color": "#f59e0b", "active": true}
   ]');
   ```

2. **Process-Based Access Control**:
   - Users can access `recruitment`, `bench_sales`, or both
   - Dynamic UI based on user permissions
   - Company-level process enablement

3. **Multi-Location Jobs**:
   - Office, remote, hybrid support
   - Location-specific compensation
   - Visa sponsorship tracking
   - Travel requirements

#### Supabase Cloud Configuration
- **Project ID**: hfzhvrknjgwtrkgyinjf
- **Status**: ACTIVE_HEALTHY
- **Region**: ap-south-1 (Asia Pacific - Mumbai)
- **Database Version**: PostgreSQL 17.4
- **API URL**: https://hfzhvrknjgwtrkgyinjf.supabase.co

### 🔄 In Progress

1. **Authentication System**: hooks.server.ts implementation
2. **UI Components**: Basic layout and navigation
3. **Email Integration**: Resend API setup in progress
4. **Dashboard Development**: Company-specific metrics

### 📋 Pending Implementation

1. **Core Features**:
   - Jobs management with configurable statuses
   - Candidate management with AI matching
   - Application workflow tracking
   - Interview scheduling system

2. **Advanced Features**:
   - Resume parsing with AI
   - Real-time notifications
   - Analytics dashboard
   - Vendor management

## Database Architecture Details

### Core Tables Summary
1. **companies** (28 columns): Multi-tenant root with comprehensive settings
2. **configurable_enums** (6 columns): Dynamic dropdown management
3. **users** (14 columns): Role-based access with process permissions
4. **jobs** (19 columns): Job postings with vector embeddings
5. **candidates** (22 columns): Candidate profiles with AI embeddings
6. **applications** (9 columns): Application tracking
7. **business_registrations** (20 columns): New customer onboarding

### Multi-Tenant Security
All tables implement Row-Level Security (RLS):
```sql
CREATE POLICY "Company-scoped access" ON jobs FOR ALL USING (
  company_id = (SELECT company_id FROM users WHERE id = auth.uid())
);
```

### Vector Embeddings
- **Jobs**: `vector_embedding` for AI-powered job matching
- **Candidates**: `vector_embedding` for semantic candidate search
- **pgvector**: Cosine similarity for matching algorithms

## Applications Architecture (3 Apps - Enterprise Security)

### 1. Customer App (app.procureserve.com)
**Purpose**: External business customers (staffing agencies, direct employers)
**Port**: 3004 (development)
**Security Level**: High (customer data, financial information)
**Features**:
- Job posting and management
- Candidate sourcing and matching
- Application tracking
- Interview scheduling
- Recruitment/bench sales processes
- Multi-tenant data isolation
- Enterprise-grade audit logging

### 2. Console App (console.procureserve.com)
**Purpose**: Internal ProcureServe team operations and customer support
**Port**: 3008 (development)
**Security Level**: Critical (administrative access, customer management)
**Features**:
- Business registration approval workflow
- Configurable enum management
- Customer support tools with audit trails
- Analytics and reporting dashboards
- System administration with MFA
- Security monitoring and incident response
- Compliance management tools

### 3. Talent App (talent.procureserve.com)
**Purpose**: Job seekers and consultants (candidate portal)
**Port**: 3006 (development)
**Security Level**: High (personal data, employment history)
**Features**:
- GDPR-compliant profile creation
- Secure job applications
- Interview scheduling with privacy controls
- Encrypted resume management
- Employment status tracking
- Privacy preference management
- Data export/deletion tools

## Email Service Architecture

### Multi-Provider Email System
**Primary**: Resend API (recommended for production)
**Fallbacks**: Supabase Auth, SMTP, Local development

**Provider Selection Strategy**:
```typescript
// Automatic provider selection based on configuration
1. Resend (if API key provided) - Best deliverability
2. Supabase Auth (for auth-related emails) - Built-in integration  
3. SMTP (Gmail, custom mail servers) - Flexibility
4. Development (console logging) - Local testing
```

### Resend Integration (Primary Choice)
**Why Resend over Supabase Email?**:
- ✅ **Superior deliverability**: 99%+ delivery rates
- ✅ **Advanced analytics**: Open rates, click tracking, bounces
- ✅ **Template management**: Rich HTML templates with variables
- ✅ **Modern API**: Developer-friendly with excellent documentation
- ✅ **Cost-effective**: Generous free tier (3,000 emails/month)

**Configuration**:
```typescript
// packages/email-service/providers.ts
export class ResendProvider implements EmailProvider {
  name = 'resend'
  
  async send(request: EmailSendRequest, config: EmailServiceConfig) {
    const { Resend } = await import('resend')
    const resend = new Resend(config.api_key || process.env.RESEND_API_KEY)
    
    return await resend.emails.send({
      from: `${config.from_name} <${config.from_email}>`,
      to: [request.to],
      subject: this.getSubjectForTemplate(request.template_type, request.variables),
      html: this.getHtmlForTemplate(request.template_type, request.variables),
      replyTo: config.reply_to_email
    })
  }
}
```

### Email Templates System
**Built-in Templates**:
1. **user_invitation**: Account activation for approved businesses
2. **password_reset**: Secure password reset links
3. **registration_submitted**: Business registration confirmation
4. **registration_approved**: Welcome with activation
5. **registration_rejected**: Feedback for improvements
6. **welcome**: Onboarding sequence
7. **notification**: General system notifications

**Template Variables**:
```typescript
// Example: User invitation template
{
  company_name: "TechCorp Solutions",
  activation_link: "https://app.procureserve.com/activate?token=...",
  invitation_url: "https://app.procureserve.com/accept?token=...",
  expires_in: "7 days",
  support_email: "<EMAIL>"
}
```

**HTML Template Example**:
```html
<!-- Professional responsive design -->
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <div style="text-align: center; margin-bottom: 30px;">
    <h1 style="color: #2563eb;">ProcureServe</h1>
    <p style="color: #64748b;">Business Registration Platform</p>
  </div>
  
  <div style="background: #dcfce7; padding: 20px; border-radius: 8px;">
    <h2 style="color: #15803d;">🎉 Account Activation Required</h2>
    <p>Welcome to <strong>{{company_name}}</strong>!</p>
  </div>
  
  <div style="text-align: center; margin: 25px 0;">
    <a href="{{activation_link}}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
      Activate Account & Set Password
    </a>
  </div>
</div>
```

## Development Workflow

### Local Development Setup (Supabase Cloud Only)
1. **No Local Supabase**: Use cloud instance only (hfzhvrknjgwtrkgyinjf)
2. **Customer App**: `npm run dev:customer` (port 3004)
3. **Console App**: `npm run dev:console` (port 3008)
4. **Talent App**: `npm run dev:talent` (port 3006)

### Environment Configuration (Cloud Only)
```bash
# Supabase Cloud Configuration (NO LOCAL)
PUBLIC_SUPABASE_URL=https://hfzhvrknjgwtrkgyinjf.supabase.co
PUBLIC_SUPABASE_ANON_KEY=cloud_anon_key
SUPABASE_SERVICE_ROLE_KEY=cloud_service_role_key

# Resend Email Service (Primary)
RESEND_API_KEY=re_your_api_key_here
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=ProcureServe
EMAIL_REPLY_TO=<EMAIL>

# Application URLs
CUSTOMER_APP_URL=https://app.procureserve.com
CONSOLE_APP_URL=https://console.procureserve.com
TALENT_APP_URL=https://talent.procureserve.com
```

### Database Migrations
**Current**: 27 migrations applied
**Latest**: `20250622000001_email_verification_system`

**Migration Categories**:
- Initial schema setup
- Process permissions system
- Multi-location jobs
- Business registration flow
- Notification system
- Security improvements

## Security Implementation

### Row-Level Security (RLS)
- **Multi-tenant isolation**: Every table scoped by company_id
- **Role-based access**: admin, recruiter, manager, viewer
- **Process permissions**: recruitment, bench_sales access control

### Authentication Flow
```mermaid
graph LR
    A[User Login] --> B[Supabase Auth]
    B --> C[JWT Token]
    C --> D[Company Lookup]
    D --> E[Process Permissions]
    E --> F[Dashboard Redirect]
```

### Data Protection
- **PII Encryption**: Sensitive candidate data
- **Audit Logging**: All user actions tracked
- **Input Sanitization**: DOMPurify for XSS protection
- **HTTPS Everywhere**: Enforced on all endpoints

## Cost Structure & Scaling

### Current Costs (Development)
- **Supabase**: Pro Plan $25/month
- **Vercel**: Pro Plan $20/month
- **Resend**: Generous free tier
- **Total**: ~$45/month

### Production Scaling
- **Database**: Auto-scaling with connection pooling
- **Storage**: Per-GB pricing for resumes/documents
- **Bandwidth**: Included in Supabase/Vercel plans
- **Email**: Usage-based pricing with Resend

## Key Design Patterns

### Component Architecture
```typescript
// Example: JobCard.svelte (under 50 lines)
<script lang="ts">
  import type { Job } from '@psii/shared-types';
  export let job: Job;
  export let onApply: (jobId: string) => void;
</script>

<div class="job-card">
  <h3>{job.title}</h3>
  <p>{job.description}</p>
  <button on:click={() => onApply(job.id)}>Apply</button>
</div>
```

### State Management
```typescript
// stores/auth.ts
import { writable } from 'svelte/store';
import type { User } from '@psii/shared-types';

export const user = writable<User | null>(null);
export const company = writable<Company | null>(null);
export const permissions = writable<string[]>([]);
```

## Business Logic & Processes

### Recruitment Process Flow
1. **Job Creation**: Multi-location, compensation tiers
2. **Candidate Sourcing**: AI-powered matching
3. **Application Management**: Status tracking
4. **Interview Scheduling**: Calendar integration
5. **Placement**: Offer management

### Bench Sales Process Flow
1. **Consultant Onboarding**: Profile creation
2. **Availability Management**: Bench status
3. **Client Matching**: Skill-based pairing
4. **Placement Tracking**: Revenue management

### Business Registration Flow
1. **Lead Capture**: Public registration form
2. **Review Process**: Console team approval
3. **Company Activation**: Automated setup
4. **User Invitation**: Email-based onboarding

## Integration Points

### OpenAI API Integration
```typescript
// Resume parsing and skill extraction
const parseResume = async (resumeText: string) => {
  const response = await openai.chat.completions.create({
    model: "gpt-4",
    messages: [
      { role: "system", content: "Extract skills and experience..." },
      { role: "user", content: resumeText }
    ]
  });
  return JSON.parse(response.choices[0].message.content);
};
```

### Vector Search Implementation
```sql
-- Semantic job matching
SELECT jobs.*, 
       1 - (jobs.vector_embedding <=> $1::vector) as similarity
FROM jobs
WHERE company_id = $2
ORDER BY similarity DESC
LIMIT 10;
```

## Deployment Strategy

### Vercel Deployment
- **Customer App**: app.procureserve.com
- **Console App**: console.procureserve.com
- **Candidate App**: candidate.procureserve.com
- **Auto-deployment**: Git push triggers build
- **Environment Variables**: Managed via Vercel dashboard

### Supabase Cloud
- **Production Database**: Automatic backups
- **Edge Functions**: Global deployment
- **CDN**: Automatic asset optimization

## Monitoring & Analytics

### Application Monitoring
- **Vercel Analytics**: Performance metrics
- **Supabase Dashboard**: Database insights
- **Error Tracking**: Built-in Supabase logging

### Business Metrics
- **Activity Logs**: User action tracking
- **Application Funnel**: Conversion analytics
- **Performance KPIs**: Response times, uptime

## Development Best Practices

### Code Organization
1. **Component Size**: Maximum 50 lines per component
2. **Type Safety**: Strict TypeScript configuration
3. **Error Handling**: Comprehensive try-catch blocks
4. **Testing**: Playwright for E2E testing

### Database Best Practices
1. **Indexing**: Optimized for query patterns
2. **Migrations**: Atomic, reversible changes
3. **Policies**: Least privilege access
4. **Performance**: Query optimization with EXPLAIN

## Future Roadmap

### Phase 1: Foundation (90% Complete)
- ✅ Database schema and security
- ✅ Basic authentication
- 🔄 Core UI components
- 🔄 Email integration

### Phase 2: Core Features (Next)
- 📋 Jobs management
- 📋 Candidate management  
- 📋 Application workflow
- 📋 Interview scheduling

### Phase 3: Advanced Features
- 📋 AI-powered matching
- 📋 Resume parsing
- 📋 Analytics dashboard
- 📋 Mobile optimization

## Troubleshooting Guide

### Common Issues
1. **Authentication Errors**: Check JWT token expiry
2. **RLS Violations**: Verify company_id context
3. **Migration Failures**: Check dependency order
4. **Email Delivery**: Verify Resend API key

### Debug Tools
- **Supabase Studio**: Database inspection
- **Vercel Logs**: Application debugging
- **Browser DevTools**: Client-side issues

## API Documentation

### Supabase REST API
- **Base URL**: https://hfzhvrknjgwtrkgyinjf.supabase.co/rest/v1/
- **Authentication**: Bearer token in headers
- **Tables**: Auto-generated endpoints for all tables

### Custom Edge Functions
- **Business Registration**: Company onboarding
- **Email Verification**: Custom token handling
- **Resume Parsing**: AI-powered extraction

## Conclusion

PSII represents a complete architectural evolution from its predecessor, focusing on:
- **Simplicity**: Supabase-first eliminates backend complexity
- **Scalability**: Cloud-native architecture
- **Maintainability**: Modular, well-documented codebase
- **Cost-effectiveness**: Open-source first approach
- **Security**: Enterprise-grade data protection

This knowledge base should be regularly updated as the project evolves. All AI tools working on this project should reference this document for architectural decisions and implementation guidance.

---

**Last Updated**: June 20, 2025
**Version**: 1.0
**Status**: Active Development
