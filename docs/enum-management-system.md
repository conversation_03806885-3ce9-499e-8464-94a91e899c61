# Application-Wide Enum Management System

## Overview

The PSII application now features a comprehensive application-wide enum management system that replaces company-specific enums for core application functionality. This system provides consistent dropdown options across all three applications (customer-app, console-app, candidate-app) while maintaining high performance through intelligent caching.

## Key Features

### ✅ Application-Wide Scope
- **No Company Restrictions**: Core enums are available across all companies and applications
- **Consistent Experience**: Same enum values everywhere in the application
- **Centralized Management**: Single source of truth managed via console-app

### ✅ Hierarchical Support
- **EAD Subcategories**: Work authorization types support nested structures (H4 EAD, L2 EAD, etc.)
- **Flexible Structure**: Can handle both flat and hierarchical enum values
- **Intelligent Flattening**: Automatic flattening for dropdown components

### ✅ System Protection
- **Core Enum Protection**: System enums cannot be deleted accidentally
- **Controlled Editing**: Granular control over what can be modified
- **Version Tracking**: Complete audit trail of changes

### ✅ Performance Optimization
- **Server-Side Caching**: 5-minute cache for admin operations
- **Client-Side Caching**: 10-minute cache for application usage
- **Smart Invalidation**: Cache invalidation on updates
- **Preloading**: Common enums preloaded for better UX

## Database Schema

### Core Tables

#### `application_enums`
```sql
CREATE TABLE application_enums (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  category TEXT NOT NULL UNIQUE,              -- e.g., 'job_statuses'
  display_name TEXT NOT NULL,                 -- e.g., 'Job Statuses'
  description TEXT,                           -- Optional description
  values JSONB NOT NULL DEFAULT '[]',         -- Array of enum values
  
  -- System metadata
  is_system BOOLEAN DEFAULT false,            -- Protected from deletion
  is_editable BOOLEAN DEFAULT true,           -- Can be modified
  is_hierarchical BOOLEAN DEFAULT false,      -- Supports subcategories
  
  -- Versioning and audit
  version INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES console_users(id),
  updated_by UUID REFERENCES console_users(id)
);
```

#### `application_enum_operations`
```sql
CREATE TABLE application_enum_operations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  enum_id UUID REFERENCES application_enums(id) ON DELETE CASCADE,
  operation_type TEXT CHECK (operation_type IN ('created', 'updated', 'value_added', 'value_removed', 'value_modified')) NOT NULL,
  user_id UUID REFERENCES console_users(id),
  changes JSONB,                              -- What changed
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Enum Categories

### Core Application Enums

#### Work Authorization Types (Hierarchical)
```json
{
  "key": "ead",
  "label": "EAD (Employment Authorization Document)",
  "color": "#f97316",
  "active": true,
  "sort_order": 7,
  "subcategories": [
    {"key": "h4_ead", "label": "H4 EAD", "description": "H4 spouse employment authorization"},
    {"key": "l2_ead", "label": "L2 EAD", "description": "L2 spouse employment authorization"},
    {"key": "e2_ead", "label": "E2 EAD", "description": "E2 treaty investor spouse"},
    {"key": "asylum_ead", "label": "Asylum EAD", "description": "Asylum-based employment authorization"},
    {"key": "refugee_ead", "label": "Refugee EAD", "description": "Refugee employment authorization"},
    {"key": "tps_ead", "label": "TPS EAD", "description": "Temporary Protected Status"},
    {"key": "daca_ead", "label": "DACA EAD", "description": "Deferred Action for Childhood Arrivals"}
  ]
}
```

#### Other Core Enums
- **job_statuses**: Draft, Open, In Progress, On Hold, Closed, Cancelled
- **interview_statuses**: Scheduled, In Progress, Completed, Cancelled, Rescheduled, No Show
- **interview_types**: Pre-Screen, Phone, Video, Online Assessment, In-Person, Panel, Technical
- **offer_statuses**: Draft, Pending, Extended, Accepted, Declined, Withdrawn, Expired
- **submission_statuses**: Submitted, Under Review, Shortlisted, Interview Scheduled, Rejected, Withdrawn, Offer Extended, Hired
- **employment_types**: Full Time, Part Time, Contract, Contract to Hire, Freelance, Temporary, Internship, Need Basis, Project Based, Seasonal
- **skill_levels**: Beginner, Intermediate, Advanced, Expert, Master
- **candidate_statuses**: New, Active, Interviewing, Placed, Inactive
- **application_statuses**: Received, Reviewing, Screening, Interviewing, Decision Pending, Approved, Rejected, Withdrawn
- **priority_levels**: Critical, High, Medium, Low

## Cloud Deployment Instructions

### Step 1: Run Schema Migration
1. Open Supabase Dashboard → SQL Editor
2. Run `/supabase/cloud-enum-migration.sql`

### Step 2: Seed Initial Data
1. In Supabase Dashboard → SQL Editor
2. Run `/supabase/cloud-enum-seed.sql`

### Step 3: Verify Installation
```sql
-- Check if enums are created
SELECT category, display_name, array_length(values, 1) as value_count 
FROM application_enums 
ORDER BY category;

-- Verify hierarchical structure for work authorization
SELECT category, values->>0 as sample_value 
FROM application_enums 
WHERE category = 'work_authorization_types';
```

## API Usage

### Server-Side Usage (Console App)

```typescript
import { getApplicationEnums, getEnumByCategory, getEnumValues } from '$lib/server/enum-cache'

// Get all enums
const allEnums = await getApplicationEnums()

// Get specific enum
const jobStatuses = await getEnumByCategory('job_statuses')

// Get just the values
const statusValues = await getEnumValues('job_statuses')

// Get options for select dropdown
const options = await getEnumOptions('job_statuses')
```

### Client-Side Usage (Customer & Candidate Apps)

```typescript
import { getEnumOptions, getHierarchicalEnumOptions, getEnumValueLabel } from '@/packages/shared-utils/enum-utils'

// Get options for select component
const jobStatusOptions = await getEnumOptions('job_statuses')

// Get hierarchical options (work authorization with EAD subcategories)
const workAuthOptions = await getHierarchicalEnumOptions('work_authorization_types')

// Get display label for a specific value
const label = await getEnumValueLabel('job_statuses', 'open')
```

### API Endpoint (Required for Client Apps)
Create `/api/enums` endpoint in customer-app and candidate-app:

```typescript
// routes/api/enums/+server.ts
import { json } from '@sveltejs/kit'
import { getApplicationEnums } from '$lib/server/enum-cache'

export async function GET() {
  try {
    const enums = await getApplicationEnums()
    return json({ enums })
  } catch (error) {
    console.error('Error fetching enums:', error)
    return json({ error: 'Failed to fetch enums' }, { status: 500 })
  }
}
```

## Component Integration

### Select Component Example

```svelte
<script lang="ts">
  import { getEnumOptions } from '@/packages/shared-utils/enum-utils'
  import { onMount } from 'svelte'

  export let value = ''
  export let category = 'job_statuses'
  
  let options = []

  onMount(async () => {
    options = await getEnumOptions(category)
  })
</script>

<select bind:value class="form-select">
  <option value="">Select {category.replace('_', ' ')}</option>
  {#each options as option}
    <option value={option.value}>{option.label}</option>
  {/each}
</select>
```

### Hierarchical Select Example

```svelte
<script lang="ts">
  import { getHierarchicalEnumOptions } from '@/packages/shared-utils/enum-utils'
  
  let workAuthOptions = []
  
  onMount(async () => {
    workAuthOptions = await getHierarchicalEnumOptions('work_authorization_types')
  })
</script>

<select bind:value class="form-select">
  <option value="">Select Work Authorization</option>
  {#each workAuthOptions as option}
    <option 
      value={option.value} 
      class={option.isSubcategory ? 'pl-4 text-muted-foreground' : ''}
    >
      {option.label}
    </option>
  {/each}
</select>
```

### Display Value with Color Badge

```svelte
<script lang="ts">
  import { getEnumValueInfo } from '@/packages/shared-utils/enum-utils'
  
  export let category
  export let value
  
  let valueInfo = null
  
  $: if (category && value) {
    getEnumValueInfo(category, value).then(info => {
      valueInfo = info
    })
  }
</script>

{#if valueInfo}
  <span class="inline-flex items-center gap-2">
    <span 
      class="w-3 h-3 rounded-full" 
      style="background-color: {valueInfo.color}"
    ></span>
    {valueInfo.label}
  </span>
{:else}
  {value}
{/if}
```

## Console Management

### Enum Creation
1. Navigate to `/enums/create` in console-app
2. Fill out enum details:
   - **Category**: Lowercase with underscores (e.g., `custom_statuses`)
   - **Display Name**: Human readable (e.g., `Custom Statuses`)
   - **Description**: Optional explanation
   - **Configuration**: Set system protection, editability, hierarchical support
   - **Values**: Add enum values with keys, labels, colors

### Enum Editing
1. Navigate to `/enums` in console-app
2. Click on enum to view/edit
3. Add/remove values or modify existing ones
4. Changes are versioned and logged

### System Protection
- **System Enums**: Cannot be deleted, marked with shield icon
- **Editable Control**: Granular control over modification permissions
- **Audit Trail**: Complete history of changes in `application_enum_operations`

## Performance Considerations

### Caching Strategy
- **Server Cache**: 5 minutes for admin operations
- **Client Cache**: 10 minutes for application usage
- **Preloading**: Common enums loaded on app start
- **Smart Invalidation**: Cache cleared on enum updates

### Best Practices
1. **Preload Common Enums**: Call `preloadEnums()` on app initialization
2. **Use Appropriate Functions**: Use `getEnumOptions()` for dropdowns, `getEnumValueLabel()` for display
3. **Handle Hierarchical Correctly**: Use `getHierarchicalEnumOptions()` for work authorization
4. **Monitor Cache**: Use `getEnumCacheStats()` for performance monitoring

## Migration from Company-Specific Enums

### For Existing Implementations
```typescript
// Old way (company-specific)
const { data } = await supabase
  .from('configurable_enums')
  .select('values')
  .eq('company_id', companyId)
  .eq('category', 'job_statuses')

// New way (application-wide)
const options = await getEnumOptions('job_statuses')
```

### Data Migration Script
If you have existing company-specific enums that should become application-wide:

```sql
-- Identify common enum values across companies
SELECT category, values 
FROM configurable_enums 
WHERE category IN ('job_statuses', 'interview_statuses', 'offer_statuses')
GROUP BY category, values
HAVING COUNT(DISTINCT company_id) > 1;

-- Insert as application-wide enums (manual process recommended)
```

## Troubleshooting

### Common Issues

#### Cache Not Updating
```typescript
import { clearEnumCache } from '@/packages/shared-utils/enum-utils'
clearEnumCache() // Force cache refresh
```

#### Hierarchical Values Not Displaying
- Ensure enum `is_hierarchical` flag is set to `true`
- Use `getHierarchicalEnumOptions()` instead of `getEnumOptions()`
- Check subcategories structure in enum values

#### Performance Issues
```typescript
import { getEnumCacheStats } from '@/packages/shared-utils/enum-utils'
console.log(getEnumCacheStats()) // Monitor cache performance
```

#### Missing Enum Categories
1. Check if enum exists in `application_enums` table
2. Verify category name spelling
3. Ensure enum is marked as active

### Debug Commands

```sql
-- Check enum structure
SELECT category, display_name, is_system, is_editable, is_hierarchical 
FROM application_enums;

-- View enum operations history
SELECT ae.category, aeo.operation_type, aeo.changes, aeo.timestamp
FROM application_enum_operations aeo
JOIN application_enums ae ON ae.id = aeo.enum_id
ORDER BY aeo.timestamp DESC;

-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename IN ('application_enums', 'application_enum_operations');
```

## Future Enhancements

### Planned Features
1. **Bulk Import/Export**: CSV/JSON import for large enum sets
2. **Enum Dependencies**: Define relationships between enums
3. **Localization**: Multi-language support for enum labels
4. **API Versioning**: Version management for enum changes
5. **Usage Analytics**: Track which enums are used most frequently

### UDF Integration (Future)
When implementing User Defined Fields (UDFs) for companies:
- Keep UDFs separate from application-wide enums
- Use company-scoped `configurable_enums` table for UDFs
- Provide clear UI distinction between core enums and custom fields

## Security Considerations

### Row Level Security
- All enum tables have RLS enabled
- Console users can read all application enums
- Only super_admin and company_admin can modify enums
- Complete audit trail for all changes

### Data Validation
- Enum keys must be lowercase with underscores
- Labels are required for all values
- Color codes validated as hex colors
- JSON structure validated before storage

### Access Control
```sql
-- Example RLS policy
CREATE POLICY "Console users can read application enums" ON application_enums
  FOR SELECT USING (
    EXISTS (SELECT 1 FROM console_users WHERE id = auth.uid())
  );
```

## Support and Maintenance

### Monitoring
- Monitor cache hit rates using `getEnumCacheStats()`
- Track enum operation frequency in `application_enum_operations`
- Watch for performance impacts on application load times

### Backup Strategy
- Regular backups of `application_enums` table
- Export enum definitions before major changes
- Maintain rollback procedures for critical enums

### Documentation Updates
- Keep enum documentation in sync with actual values
- Update API documentation when new categories are added
- Maintain change log for enum modifications

---

**Note**: This system replaces company-specific enum management for core application functionality. Company-specific customizations will be handled through a separate User Defined Fields (UDF) system in future iterations.
