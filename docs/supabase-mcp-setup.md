# Supabase MCP Integration Guide for ProcureServe II

## 🤖 What is Supabase MCP?

The **Model Context Protocol (MCP)** allows AI assistants like <PERSON> to directly interact with your Supabase database using natural language commands. Instead of writing SQL manually, you can ask <PERSON> to manage your database, create tables, query data, and more.

## 🎯 Project Configuration

### Current Supabase Setup
- **Project ID:** `hfzhvrknjgwtrkgyinjf`
- **URL:** `https://hfzhvrknjgwtrkgyinjf.supabase.co`
- **Anon Key:** `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TwvhR8RrtEZuV6oCXIT2fho5_U5mnH21Hvt4bbvhThY`
- **Service Role Key:** `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************._scXNK1IgQuVok-iIBVxQYmcWntrI2VLInx1_Cz8knI`

## 🔧 Setup Instructions

### Step 1: Generate Personal Access Token

1. **Go to Supabase Dashboard**
   - Visit: https://supabase.com/dashboard/account/tokens
   - Sign in with your Supabase account

2. **Create New Token**
   - Click "Generate new token"
   - Name: `Claude MCP Server - PSII`
   - Description: `MCP access for ProcureServe II development`
   - Scope: Select appropriate permissions (typically read/write for development)
   - Click "Generate token"

3. **Copy Token**
   - Save the token securely - it won't be shown again
   - Format: `sbp_...` (starts with sbp_)

### Step 2: Configure Claude Desktop (Recommended)

If you have Claude Desktop installed:

1. **Open Claude Desktop Settings**
   - Navigate to Settings → MCP Servers

2. **Add Configuration**
   ```json
   {
     "mcpServers": {
       "supabase": {
         "command": "npx",
         "args": [
           "-y",
           "@supabase/mcp-server-supabase@latest",
           "--access-token",
           "your_personal_access_token_here",
           "--project-ref",
           "hfzhvrknjgwtrkgyinjf"
         ]
       }
     }
   }
   ```

3. **Replace Token**
   - Replace `your_personal_access_token_here` with your actual token
   - Save configuration and restart Claude Desktop

### Step 3: Configure Cursor (Alternative)

If you're using Cursor:

1. **Create MCP Configuration**
   - Create `.cursor/mcp.json` in your project root:
   ```json
   {
     "mcpServers": {
       "supabase": {
         "command": "npx",
         "args": [
           "-y",
           "@supabase/mcp-server-supabase@latest",
           "--access-token",
           "your_personal_access_token_here",
           "--project-ref",
           "hfzhvrknjgwtrkgyinjf"
         ]
       }
     }
   }
   ```

2. **Restart Cursor**
   - Restart Cursor to load the new configuration
   - Check for MCP tools in the command palette

### Step 4: Configure VS Code with Cline

If you're using Cline extension in VS Code:

1. **Install Cline Extension**
   - Install from VS Code Marketplace: `saoudrizwan.claude-dev`

2. **Configure MCP**
   - Open Cline settings
   - Navigate to MCP Servers configuration
   - Add the same JSON configuration as above

## 🛠️ Available MCP Tools

Once configured, Claude can use these tools with your ProcureServe II database:

### Database Management
- `execute_sql` - Run SQL queries directly
- `apply_migration` - Apply database migrations
- `list_tables` - View all tables in your schema
- `get_table_definition` - Get detailed table structure

### Project Management
- `get_project_url` - Get your project URL
- `get_anon_key` - Retrieve anonymous key
- `get_service_role_key` - Get service role key (admin access)
- `list_projects` - List all your Supabase projects

### Development Helpers
- `generate_typescript_types` - Generate TS types from schema
- `search_docs` - Search Supabase documentation
- `get_logs` - Retrieve project logs for debugging

### Advanced Features
- `create_branch` - Create database branches (Pro feature)
- `list_branches` - List available branches
- `merge_branch` - Merge branches

## 💡 Example Usage

Once MCP is configured, you can ask Claude:

### Database Queries
```
"Show me all users in the ProcureServe database"
"Create a new table for job applications with these fields..."
"Update all inactive users to have a status of 'archived'"
"Generate a report of companies with the most job postings this month"
```

### Schema Management
```
"What's the current database schema?"
"Add a new column 'last_login' to the users table"
"Create indexes to optimize the jobs search functionality"
"Generate TypeScript types for the current schema"
```

### Development Tasks
```
"Check the recent error logs"
"What's my project's anon key?"
"Create a migration to add the new candidate features"
"Show me which tables have Row Level Security enabled"
```

## 🔒 Security Considerations

### Read-Only Mode
For safer exploration, you can configure read-only access:
```json
{
  "args": [
    "-y",
    "@supabase/mcp-server-supabase@latest",
    "--access-token", "your_token",
    "--project-ref", "hfzhvrknjgwtrkgyinjf",
    "--read-only"
  ]
}
```

### Best Practices
1. **Use read-only mode** for production databases
2. **Create separate tokens** for different environments
3. **Regularly rotate tokens** for security
4. **Use database branching** for experimental changes
5. **Always backup** before major schema changes

## 🚀 Integration with ProcureServe II

### Development Workflow
1. **Use MCP for rapid prototyping** - Ask Claude to create test data
2. **Schema evolution** - Let Claude suggest and implement schema improvements
3. **Data analysis** - Quick insights about users, jobs, applications
4. **Debugging** - Ask Claude to help diagnose database issues

### Specific Use Cases
- **User Management:** "Show me all users who haven't logged in for 30 days"
- **Job Analytics:** "What are the most popular job types by company?"
- **Application Tracking:** "Show me the conversion rate from application to hire"
- **Configuration:** "Update the configurable enums for work authorization types"

## 🔧 Troubleshooting

### Common Issues

1. **MCP Not Available**
   - Ensure you're using a compatible Claude interface
   - Check that the configuration file is properly formatted
   - Restart your AI tool after configuration

2. **Authentication Failed**
   - Verify your Personal Access Token is correct
   - Check that the token has necessary permissions
   - Ensure the project reference matches your database

3. **Connection Issues**
   - Verify Supabase is reachable from your network
   - Check that your project is active and not paused
   - Test direct API access with curl

### Verification Commands
```bash
# Test Supabase connectivity
curl -H "apikey: your_anon_key" \
     "https://hfzhvrknjgwtrkgyinjf.supabase.co/rest/v1/"

# Verify MCP installation
npx @supabase/mcp-server-supabase@latest --help

# Check project status
node scripts/setup-supabase-cloud.js
```

## 📚 Additional Resources

- **Supabase MCP Documentation:** https://supabase.com/docs/guides/getting-started/mcp
- **MCP GitHub Repository:** https://github.com/supabase-community/supabase-mcp
- **Model Context Protocol:** https://spec.modelcontextprotocol.io/
- **Our Project Dashboard:** https://supabase.com/dashboard/project/hfzhvrknjgwtrkgyinjf

## 🎯 Next Steps

1. **Set up MCP** following the instructions above
2. **Test with simple queries** to verify connectivity
3. **Explore schema management** for ProcureServe tables
4. **Use for development** to accelerate database work
5. **Consider production setup** with read-only access

---

**Note:** MCP is optional but highly recommended for accelerated development. All ProcureServe II functionality works perfectly without it, but MCP enables powerful AI-assisted database management.
