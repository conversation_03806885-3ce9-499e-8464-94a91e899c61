#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

dotenv.config({ path: join(__dirname, '.env.local') })

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { persistSession: false, autoRefreshToken: false }
})

async function checkConsoleTables() {
  console.log('🔍 Checking Console App Database Setup...\n')
  
  // Check if console_users table exists
  try {
    const { data, error } = await supabase
      .from('console_users')
      .select('count(*)')
      .limit(1)
    
    if (error) {
      console.log('❌ console_users table does not exist')
      console.log('   Error:', error.message)
      console.log('   Need to run console app migrations first!')
      return false
    } else {
      console.log('✅ console_users table exists')
    }
  } catch (e) {
    console.log('❌ Error checking console_users:', e.message)
    return false
  }

  // Check console_user_permissions table
  try {
    const { data, error } = await supabase
      .from('console_user_permissions')
      .select('count(*)')
      .limit(1)
    
    if (error) {
      console.log('❌ console_user_permissions table does not exist')
      console.log('   Error:', error.message)
    } else {
      console.log('✅ console_user_permissions table exists')
    }
  } catch (e) {
    console.log('❌ Error checking console_user_permissions:', e.message)
  }

  // Check console_security_events table
  try {
    const { data, error } = await supabase
      .from('console_security_events')
      .select('count(*)')
      .limit(1)
    
    if (error) {
      console.log('❌ console_security_events table does not exist')
      console.log('   Error:', error.message)
    } else {
      console.log('✅ console_security_events table exists')
    }
  } catch (e) {
    console.log('❌ Error checking console_security_events:', e.message)
  }

  return true
}

checkConsoleTables()