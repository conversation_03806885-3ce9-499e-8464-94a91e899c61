#!/usr/bin/env node

// Test registration by directly calling the server action
import { config } from 'dotenv'
import { createSupabaseAdminClient } from './apps/candidate-app/src/lib/server/supabase-admin.js'
import { sendVerificationEmail } from './packages/email-service/verification.js'

config()

console.log('🧪 Testing Registration Components Directly...\n')

async function testSupabaseConnection() {
  console.log('🔗 Testing Supabase Admin Connection...')
  try {
    const supabase = createSupabaseAdminClient()
    const { data, error } = await supabase.from('users').select('count').limit(1)
    
    if (error) {
      console.error('❌ Supabase connection failed:', error.message)
      return false
    }
    
    console.log('✅ Supabase admin connection successful')
    return true
  } catch (error) {
    console.error('❌ Supabase test failed:', error.message)
    return false
  }
}

async function testEmailService() {
  console.log('\n📧 Testing Email Service...')
  try {
    const result = await sendVerificationEmail({
      to: '<EMAIL>',
      firstName: 'Test',
      token: 'test-token-123',
      baseUrl: 'http://localhost:3006/'
    })
    
    if (result.success) {
      console.log('✅ Email service configuration is working')
      console.log('Message:', result.message)
      return true
    } else {
      console.error('❌ Email service failed:', result.error)
      return false
    }
  } catch (error) {
    console.error('❌ Email service test failed:', error.message)
    return false
  }
}

async function testTokenCreation() {
  console.log('\n🔑 Testing Token Creation Function...')
  try {
    const supabase = createSupabaseAdminClient()
    
    // Test the create_verification_token RPC function
    const { data, error } = await supabase.rpc('create_verification_token', {
      p_user_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
      p_email: '<EMAIL>',
      p_first_name: 'Test'
    })
    
    if (error) {
      console.error('❌ Token creation failed:', error.message)
      console.error('Error details:', error)
      return false
    }
    
    if (data && typeof data === 'string' && data.length > 0) {
      console.log('✅ Token creation function is working')
      console.log('Generated token length:', data.length)
      return true
    } else {
      console.error('❌ Token creation returned invalid data:', data)
      return false
    }
  } catch (error) {
    console.error('❌ Token creation test failed:', error.message)
    return false
  }
}

async function testEmailVerificationTable() {
  console.log('\n📋 Testing Email Verification Table...')
  try {
    const supabase = createSupabaseAdminClient()
    
    // Check if the table exists by trying to select from it
    const { data, error } = await supabase
      .from('email_verification_tokens')
      .select('id')
      .limit(1)
    
    if (error) {
      console.error('❌ Email verification table test failed:', error.message)
      console.error('This might mean the table doesn\'t exist or there are permission issues')
      return false
    }
    
    console.log('✅ Email verification table is accessible')
    return true
  } catch (error) {
    console.error('❌ Email verification table test failed:', error.message)
    return false
  }
}

async function runComponentTests() {
  console.log('Testing individual authentication components:\n')
  
  const results = []
  results.push(await testSupabaseConnection())
  results.push(await testEmailVerificationTable())
  results.push(await testTokenCreation())
  results.push(await testEmailService())
  
  console.log('\n📊 Component Test Results:')
  console.log('Supabase Connection:', results[0] ? '✅ PASS' : '❌ FAIL')
  console.log('Email Verification Table:', results[1] ? '✅ PASS' : '❌ FAIL')
  console.log('Token Creation:', results[2] ? '✅ PASS' : '❌ FAIL')
  console.log('Email Service:', results[3] ? '✅ PASS' : '❌ FAIL')
  
  const passCount = results.filter(r => r).length
  console.log(`\n${passCount}/4 components working correctly`)
  
  if (passCount === 4) {
    console.log('🎉 All authentication components are working!')
    console.log('The registration flow should work properly.')
  } else {
    console.log('🔧 Some components need to be fixed before registration will work.')
  }
  
  return passCount === 4
}

runComponentTests().catch(console.error)