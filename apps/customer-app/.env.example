# Customer App Environment Configuration
# Copy this file to .env.local and update with your values

# Supabase Configuration (Local Development)
PUBLIC_SUPABASE_URL=http://127.0.0.1:54331
PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# Application Configuration
NODE_ENV=development
CUSTOMER_APP_PORT=3004

# Database URL (for server-side operations)
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54332/postgres

# For production, replace with your actual Supabase project values:
# PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
# PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Optional: Analytics and monitoring
# ANALYTICS_ID=
# SENTRY_DSN=

# Optional: File upload settings
# MAX_FILE_SIZE=50MB
# UPLOAD_BUCKET=uploads
