{"name": "customer-app", "version": "0.0.1", "private": true, "scripts": {"build": "vite build", "dev": "vite dev --port 3004", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@playwright/test": "^1.53.0", "@sveltejs/adapter-vercel": "^5.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^4.0.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^5.0.3"}, "dependencies": {"@procureserve/shared-types": "file:../../packages/shared-types", "@procureserve/shared-utils": "file:../../packages/shared-utils", "@psii/security-service": "1.0.0", "@psii/audit-service": "1.0.0", "@psii/domain-detection-service": "1.0.0", "@psii/email-service": "1.0.0", "@psii/business-email-workflow": "1.0.0", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "lucide-svelte": "^0.445.0", "shadcn-svelte": "^1.0.2", "tailwind-merge": "^3.3.1", "zod": "^3.25.63"}, "type": "module"}