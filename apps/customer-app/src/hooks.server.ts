import { createSupabaseLoadClient } from '$lib/supabase'
import { redirect } from '@sveltejs/kit'
import type { Handle } from '@sveltejs/kit'

const DEBUG_AUTH = process.env.NODE_ENV === 'development'

function log(message: string, data?: any) {
  if (DEBUG_AUTH) {
    console.log(`[CUSTOMER-SECURITY] ${message}`, data ? JSON.stringify(data, null, 2) : '')
  }
}

/**
 * Cross-app session validation to prevent authentication bleeding
 */
async function validateCustomerAppAccess(event: any, user: any): Promise<boolean> {
  if (!user) return false
  
  try {
    const supabase = createSupabaseLoadClient(event.fetch, event)

    // Check if user exists in other app tables (security breach detection)
    const [consoleCheck, candidateCheck] = await Promise.all([
      supabase.from('console_users').select('id').eq('id', user.id).single(),
      supabase.from('candidates').select('id').eq('id', user.id).single()
    ])

    // If user is found in console or candidate tables, this is a cross-app breach
    if (consoleCheck.data || candidateCheck.data) {
      log('SECURITY BREACH DETECTED: Cross-app access attempt', {
        userId: user.id,
        email: user.email,
        foundInConsole: !!consoleCheck.data,
        foundInCandidates: !!candidateCheck.data
      })
      
      // Clear all sessions to prevent further breach
      await supabase.auth.signOut()
      
      // Clear all potentially compromised cookies
      const allCookies = event.cookies.getAll()
      allCookies.forEach(cookie => {
        if (cookie.name.startsWith('sb-')) {
          event.cookies.delete(cookie.name, { path: '/' })
        }
      })
      
      return false
    }

    // Check if user exists in users table (valid customer user)
    const { data: customerUser, error } = await supabase
      .from('users')
      .select('id, email, role, company_id')
      .eq('id', user.id)
      .single()

    if (error || !customerUser) {
      log('SECURITY BREACH DETECTED: Non-customer user attempting access', {
        userId: user.id,
        email: user.email,
        error: error?.message
      })
      
      // Clear session and cookies
      await supabase.auth.signOut()
      const allCookies = event.cookies.getAll()
      allCookies.forEach(cookie => {
        if (cookie.name.startsWith('sb-')) {
          event.cookies.delete(cookie.name, { path: '/' })
        }
      })
      
      return false
    }

    return true
  } catch (error) {
    log('Error in cross-app validation:', error)
    return false
  }
}

export const handle: Handle = async ({ event, resolve }) => {
  const url = new URL(event.request.url)
  log(`Processing customer request to: ${url.pathname}`)
  
  // Create Supabase client for server-side operations with app-specific session isolation
  event.locals.supabase = createSupabaseLoadClient(event.fetch, event)
  
  // Define route types first
  const isAuthPage = url.pathname.startsWith('/login') || 
                     url.pathname.startsWith('/register') || 
                     url.pathname.startsWith('/activate') || 
                     url.pathname.startsWith('/reset-password')
  const isPublicPage = ['/', '/about', '/contact', '/test-users', '/select-process', '/debug-auth', '/logout', '/create-users', '/env-check', '/test-connection', '/registration/submitted'].includes(url.pathname)
  const isApiRoute = url.pathname.startsWith('/api/')
  const isProcessRoute = url.pathname.startsWith('/recruitment/') || url.pathname.startsWith('/bench-sales/')
  const isSelectProcessPage = url.pathname === '/select-process' || url.pathname.startsWith('/select-process/')
  const isAccessDeniedPage = url.pathname === '/access-denied'
  
  // SECURITY FIX: Use getUser() instead of getSession() for server-side validation
  const {
    data: { user },
    error: userError
  } = await event.locals.supabase.auth.getUser()
  
  // Only log auth errors that are unexpected (not missing sessions on auth pages)
  if (userError) {
    if (userError.name === 'AuthSessionMissingError' && (isAuthPage || isPublicPage)) {
      log('No active session (expected for auth/public pages)')
    } else {
      log('Auth error:', userError)
    }
  }
  
  log('User from getUser():', user ? { id: user.id, email: user.email } : null)
  
  // CRITICAL SECURITY: Validate cross-app access
  let isValidCustomerAccess = false
  let customerUser = null
  
  if (user) {
    isValidCustomerAccess = await validateCustomerAppAccess(event, user)
    
    if (!isValidCustomerAccess) {
      log('SECURITY BREACH BLOCKED: Cross-app access denied')
      
      // Redirect to login with security warning
      throw redirect(303, '/login?error=security_breach_detected')
    }

    // SECURITY ENHANCEMENT: Check for valid customer app user AND clear invalid sessions
    const { data: userData, error: userDataError } = await event.locals.supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single()
    
    if (!userDataError && userData) {
      customerUser = userData;
      log('Customer user found:', { id: userData.id, email: userData.email, role: userData.role })
    } else {
      log('No customer user record found for authenticated user - SECURITY VIOLATION DETECTED')
      
      // SECURITY FIX: Clear invalid session immediately
      log('Clearing invalid session for security')
      await event.locals.supabase.auth.signOut()
      
      // If this is not a public page or auth page, redirect to login
      if (!isPublicPage && !isAuthPage && !isApiRoute) {
        log('Redirecting unauthorized user to login after session cleanup')
        throw redirect(303, '/login?error=session_invalid')
      }
    }
  }
  
  // Set locals for downstream usage - only if user is validated as customer
  event.locals.user = user && customerUser && isValidCustomerAccess ? user : null
  event.locals.customerUser = customerUser
  event.locals.session = user && customerUser && isValidCustomerAccess ? {
    user,
    access_token: '',
    refresh_token: '',
    expires_in: 0,
    token_type: 'bearer'
  } : null
  
  log('Route analysis:', {
    isAuthPage,
    isPublicPage,
    isApiRoute,
    isProcessRoute,
    isSelectProcessPage,
    isAccessDeniedPage,
    hasValidUser: !!(user && customerUser && isValidCustomerAccess)
  })
  
  // Redirect authenticated users away from auth pages (except API routes)
  if (user && customerUser && isValidCustomerAccess && isAuthPage && !isApiRoute) {
    log('Redirecting authenticated user away from auth page to select-process')
    throw redirect(303, '/select-process')
  }
  
  // Redirect unauthenticated users to login (except for public pages and API routes)
  if ((!user || !customerUser || !isValidCustomerAccess) && !isAuthPage && !isPublicPage && !isApiRoute) {
    log('Redirecting unauthenticated user to login')
    throw redirect(303, '/login')
  }
  
  // For authenticated users accessing process routes, verify permissions
  if (user && customerUser && isValidCustomerAccess && isProcessRoute) {
    log('Checking process permissions for authenticated user')
    
    // Get user process permissions from customer user data
    const processFromUrl = url.pathname.startsWith('/recruitment/') ? 'recruitment' : 'bench_sales'
    
    // Safely access process_permissions with proper type checking
    let userPermissions: string[] = []
    if (customerUser) {
      // Try to get from profile first, then directly from user
      const profilePermissions = customerUser.profile && typeof customerUser.profile === 'object' && customerUser.profile !== null 
        ? (customerUser.profile as any)['process_permissions'] 
        : null
      const directPermissions = (customerUser as any)['process_permissions']
      
      userPermissions = profilePermissions || directPermissions || []
      
      // Ensure it's an array
      if (!Array.isArray(userPermissions)) {
        userPermissions = []
      }
    }
    
    const hasPermission = userPermissions.includes(processFromUrl)
    
    log('Process permission check:', {
      processFromUrl,
      userPermissions: userPermissions,
      hasPermission
    })
    
    if (!hasPermission) {
      log('User lacks permission for requested process, redirecting to access-denied')
      throw redirect(303, '/access-denied')
    }
  }
  
  // Add security headers
  const response = await resolve(event)
  
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('X-Customer-App', 'true') // App identification header
  
  // CSP for customer app
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co",
    "font-src 'self' https://fonts.gstatic.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ')
  
  response.headers.set('Content-Security-Policy', csp)

  log('Request processing complete, continuing to route handler')
  return response
}
