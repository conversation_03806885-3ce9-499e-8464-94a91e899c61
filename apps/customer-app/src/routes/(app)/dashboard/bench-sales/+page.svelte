<script lang="ts">
	import type { PageData } from './$types'
	import { goto } from '$app/navigation'
	import { Search, Plus, Users, Target, Award } from 'lucide-svelte'

	export let data: PageData

	function navigateToBenchCandidates() {
		goto('/bench-sales/candidates')
	}

	function navigateToProjects() {
		goto('/bench-sales/projects')
	}

	function navigateToPlacements() {
		goto('/bench-sales/placements')
	}

	function addBenchCandidate() {
		goto('/bench-sales/candidates/create')
	}

	function searchProjects() {
		goto('/bench-sales/projects/search')
	}
</script>

<svelte:head>
	<title>Bench Sales Dashboard - ProcureServe</title>
	<meta name="description" content="Manage your bench sales process with consultants, projects, and placements" />
</svelte:head>

<div class="space-y-6">
	<!-- Header -->
	<div class="flex justify-between items-center">
		<div>
			<h1 class="text-2xl font-semibold text-gray-900">Bench Sales Dashboard</h1>
			<p class="text-gray-600">Manage consultants, projects, and placements</p>
		</div>
		<div class="flex gap-3">
			<button
				on:click={searchProjects}
				class="border border-gray-300 hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg flex items-center gap-2">
				<Search class="w-4 h-4" />
				Find Projects
			</button>
			<button
				on:click={addBenchCandidate}
				class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
				<Plus class="w-4 h-4" />
				Add Consultant
			</button>
		</div>
	</div>

	<!-- Stats Cards -->
	<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
		<div class="bg-white rounded-lg border p-6">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm font-medium text-gray-600">Available Consultants</p>
					<p class="text-2xl font-semibold text-gray-900">
						{data.dashboard_data.bench_candidates.filter(c => c.status === 'available').length}
					</p>
				</div>
				<div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
					<Users class="w-6 h-6 text-green-600" />
				</div>
			</div>
		</div>

		<div class="bg-white rounded-lg border p-6">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm font-medium text-gray-600">Active Projects</p>
					<p class="text-2xl font-semibold text-gray-900">
						{data.dashboard_data.project_opportunities.filter(p => p.status === 'published').length}
					</p>
				</div>
				<div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
					<Target class="w-6 h-6 text-blue-600" />
				</div>
			</div>
		</div>

		<div class="bg-white rounded-lg border p-6">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm font-medium text-gray-600">Active Placements</p>
					<p class="text-2xl font-semibold text-gray-900">
						{data.dashboard_data.recent_placements.filter(p => p.status === 'active').length}
					</p>
				</div>
				<div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
					<Award class="w-6 h-6 text-purple-600" />
				</div>
			</div>
		</div>
	</div>

	<!-- Recent Activity -->
	<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
		<!-- Bench Candidates -->
		<div class="bg-white rounded-lg border">
			<div class="p-6 border-b">
				<div class="flex justify-between items-center">
					<h2 class="text-lg font-semibold">Bench Candidates</h2>
					<button
						on:click={navigateToBenchCandidates}
						class="text-green-600 hover:text-green-700 text-sm">
						View all
					</button>
				</div>
			</div>
			<div class="p-6">
				{#if data.dashboard_data.bench_candidates.length > 0}
					<div class="space-y-4">
						{#each data.dashboard_data.bench_candidates as candidate}
							<div class="flex items-center justify-between">
								<div>
									<p class="font-medium text-gray-900">{candidate.name}</p>
									<p class="text-sm text-gray-500">
										{candidate.email} • {candidate.skills?.slice(0, 2).join(', ') || 'No skills listed'}
									</p>
								</div>
								<span class="bg-{candidate.status === 'available' ? 'green' : candidate.status === 'placed' ? 'blue' : 'gray'}-100
								           text-{candidate.status === 'available' ? 'green' : candidate.status === 'placed' ? 'blue' : 'gray'}-800
								           px-2 py-1 rounded-full text-xs font-medium">
									{candidate.status}
								</span>
							</div>
						{/each}
					</div>
				{:else}
					<div class="text-center py-8 text-gray-500">
						<Users class="w-12 h-12 mx-auto mb-4 text-gray-300" />
						<p>No bench candidates yet</p>
						<button
							on:click={addBenchCandidate}
							class="mt-2 text-green-600 hover:text-green-700 text-sm">
							Add your first bench candidate
						</button>
					</div>
				{/if}
			</div>
		</div>

		<!-- Project Opportunities -->
		<div class="bg-white rounded-lg border">
			<div class="p-6 border-b">
				<div class="flex justify-between items-center">
					<h2 class="text-lg font-semibold">Project Opportunities</h2>
					<button
						on:click={navigateToProjects}
						class="text-blue-600 hover:text-blue-700 text-sm">
						View all
					</button>
				</div>
			</div>
			<div class="p-6">
				{#if data.dashboard_data.project_opportunities.length > 0}
					<div class="space-y-4">
						{#each data.dashboard_data.project_opportunities as project}
							<div class="flex items-center justify-between">
								<div>
									<p class="font-medium text-gray-900">{project.title}</p>
									<p class="text-sm text-gray-500">
										{project.employment_type} • {new Date(project.created_at).toLocaleDateString()}
									</p>
								</div>
								<span class="bg-{project.status === 'published' ? 'blue' : project.status === 'draft' ? 'yellow' : 'gray'}-100
								           text-{project.status === 'published' ? 'blue' : project.status === 'draft' ? 'yellow' : 'gray'}-800
								           px-2 py-1 rounded-full text-xs font-medium">
									{project.status}
								</span>
							</div>
						{/each}
					</div>
				{:else}
					<div class="text-center py-8 text-gray-500">
						<Target class="w-12 h-12 mx-auto mb-4 text-gray-300" />
						<p>No project opportunities yet</p>
						<button
							on:click={searchProjects}
							class="mt-2 text-blue-600 hover:text-blue-700 text-sm">
							Search for projects
						</button>
					</div>
				{/if}
			</div>
		</div>
	</div>
	
	<!-- Recent Placements -->
	{#if data.dashboard_data.recent_placements.length > 0}
		<div class="bg-white rounded-lg border">
			<div class="p-6 border-b">
				<div class="flex justify-between items-center">
					<h2 class="text-lg font-semibold">Recent Placements</h2>
					<button 
						on:click={navigateToPlacements}
						class="text-purple-600 hover:text-purple-700 text-sm">
						View all
					</button>
				</div>
			</div>
			<div class="p-6">
				<div class="space-y-4">
					{#each data.dashboard_data.recent_placements as placement}
						<div class="flex items-center justify-between">
							<div>
								<p class="font-medium text-gray-900">
									{placement.candidates?.[0]?.name || 'Unknown'} → {placement.jobs?.[0]?.title || 'Unknown Project'}
								</p>
								<p class="text-sm text-gray-500">
									{placement.jobs?.[0]?.employment_type || 'Contract'} • {new Date(placement.created_at).toLocaleDateString()}
								</p>
							</div>
							<span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium">
								Placed
							</span>
						</div>
					{/each}
				</div>
			</div>
		</div>
	{/if}
	
	<!-- Quick Actions -->
	<div class="bg-white rounded-lg border p-6">
		<h2 class="text-lg font-semibold mb-4">Quick Actions</h2>
		<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
			<button 
				on:click={addBenchCandidate}
				class="p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors group">
				<div class="text-center">
					<div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-green-200">
						<Plus class="w-6 h-6 text-green-600" />
					</div>
					<p class="font-medium text-gray-900">Add Candidate</p>
					<p class="text-sm text-gray-500">Add to bench</p>
				</div>
			</button>
			
			<button 
				on:click={searchProjects}
				class="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group">
				<div class="text-center">
					<div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-200">
						<Search class="w-6 h-6 text-blue-600" />
					</div>
					<p class="font-medium text-gray-900">Find Projects</p>
					<p class="text-sm text-gray-500">Search opportunities</p>
				</div>
			</button>
			
			<button 
				on:click={navigateToBenchCandidates}
				class="p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors group">
				<div class="text-center">
					<div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-orange-200">
						<Users class="w-6 h-6 text-orange-600" />
					</div>
					<p class="font-medium text-gray-900">Manage Bench</p>
					<p class="text-sm text-gray-500">View candidates</p>
				</div>
			</button>
			
			<button 
				on:click={navigateToPlacements}
				class="p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors group">
				<div class="text-center">
					<div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-purple-200">
						<Award class="w-6 h-6 text-purple-600" />
					</div>
					<p class="font-medium text-gray-900">Track Placements</p>
					<p class="text-sm text-gray-500">Monitor success</p>
				</div>
			</button>
		</div>
	</div>
</div>
