<!-- Notifications page -->
<script lang="ts">
  import { onMount, onDestroy } from 'svelte'
  import { Bell, CheckCircle, Filter, Trash2 } from 'lucide-svelte'
  import { createNotificationService } from '$lib/notification-service'
  import { supabase } from '$lib/supabase'
  import NotificationItem from '$lib/components/notifications/NotificationItem.svelte'
  import NotificationToast from '$lib/components/notifications/NotificationToast.svelte'
  import type { PageData } from './$types'
  import type { Notification, NotificationEvent } from '$lib/notification-service'

  export let data: PageData

  let notifications = data.notifications
  let unreadCount = data.unreadCount
  let notificationService: any
  let realtimeChannel: any
  let toastNotifications: Notification[] = []
  let showFilters = false
  let filterRead = 'all' // 'all', 'unread', 'read'

  onMount(async () => {
    const { service } = createNotificationService(supabase)
    notificationService = service

    // Subscribe to real-time updates
    realtimeChannel = notificationService.subscribeToNotifications(
      data.user.id,
      handleRealtimeEvent
    )
  })

  onDestroy(() => {
    if (notificationService) {
      notificationService.unsubscribeFromNotifications()
    }
  })

  function handleRealtimeEvent(event: NotificationEvent) {
    if (event.type === 'notification_created') {
      notifications = [event.payload, ...notifications]
      unreadCount += 1
      
      // Show toast for high priority notifications
      if (event.payload.priority === 'high' || event.payload.priority === 'urgent') {
        toastNotifications = [...toastNotifications, event.payload]
      }
    }
  }

  async function markAsRead(notificationId: string) {
    if (!notificationService) return

    const { success } = await notificationService.markAsRead(notificationId)
    if (success) {
      notifications = notifications.map(n => 
        n.id === notificationId ? { ...n, is_read: true, read_at: new Date().toISOString() } : n
      )
      unreadCount = Math.max(0, unreadCount - 1)
    }
  }

  async function markAllAsRead() {
    if (!notificationService) return

    const { success, count } = await notificationService.markAllAsRead()
    if (success) {
      notifications = notifications.map(n => ({
        ...n, 
        is_read: true, 
        read_at: new Date().toISOString()
      }))
      unreadCount = 0
    }
  }

  function dismissToast(notification: Notification) {
    toastNotifications = toastNotifications.filter(n => n.id !== notification.id)
  }

  $: filteredNotifications = notifications.filter(n => {
    if (filterRead === 'unread') return !n.is_read
    if (filterRead === 'read') return n.is_read
    return true
  })
</script>

<svelte:head>
  <title>Notifications - ProcureServe</title>
</svelte:head>

<!-- Toast notifications for real-time alerts -->
{#each toastNotifications as notification}
  <NotificationToast 
    {notification} 
    onDismiss={() => dismissToast(notification)}
  />
{/each}

<div class="max-w-4xl mx-auto px-4 py-6">
  <!-- Header -->
  <div class="flex items-center justify-between mb-6">
    <div class="flex items-center space-x-3">
      <Bell size={24} class="text-gray-700" />
      <h1 class="text-2xl font-bold text-gray-900">Notifications</h1>
      {#if unreadCount > 0}
        <span class="px-2 py-1 text-xs font-medium text-white bg-red-500 rounded-full">
          {unreadCount}
        </span>
      {/if}
    </div>

    <div class="flex items-center space-x-2">
      <button
        on:click={() => showFilters = !showFilters}
        class="flex items-center space-x-1 px-3 py-2 text-sm text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
      >
        <Filter size={16} />
        <span>Filter</span>
      </button>

      {#if unreadCount > 0}
        <button
          on:click={markAllAsRead}
          class="flex items-center space-x-1 px-3 py-2 text-sm text-green-600 border border-green-300 rounded-md hover:bg-green-50"
        >
          <CheckCircle size={16} />
          <span>Mark All Read</span>
        </button>
      {/if}
    </div>
  </div>

  <!-- Filters -->
  {#if showFilters}
    <div class="mb-4 p-4 bg-gray-50 rounded-lg">
      <div class="flex items-center space-x-4">
        <label class="text-sm font-medium text-gray-700">Show:</label>
        <label class="flex items-center">
          <input type="radio" bind:group={filterRead} value="all" class="mr-2" />
          <span class="text-sm">All</span>
        </label>
        <label class="flex items-center">
          <input type="radio" bind:group={filterRead} value="unread" class="mr-2" />
          <span class="text-sm">Unread</span>
        </label>
        <label class="flex items-center">
          <input type="radio" bind:group={filterRead} value="read" class="mr-2" />
          <span class="text-sm">Read</span>
        </label>
      </div>
    </div>
  {/if}

  <!-- Notifications list -->
  <div class="bg-white rounded-lg shadow divide-y divide-gray-200">
    {#if filteredNotifications.length === 0}
      <div class="text-center py-12">
        <Bell size={48} class="mx-auto text-gray-300 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">No notifications</h3>
        <p class="text-gray-500">
          {filterRead === 'unread' ? 'No unread notifications' : 'You\'re all caught up!'}
        </p>
      </div>
    {:else}
      {#each filteredNotifications as notification}
        <NotificationItem 
          {notification} 
          onMarkAsRead={markAsRead}
        />
      {/each}
    {/if}
  </div>
</div>
