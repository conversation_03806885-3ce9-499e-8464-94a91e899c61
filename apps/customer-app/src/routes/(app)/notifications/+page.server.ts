// Notifications page server logic
import { createNotificationService } from '$lib/notification-service'
import { redirect } from '@sveltejs/kit'
import type { PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ locals: { supabase, user } }) => {
  if (!user) {
    throw redirect(303, '/login')
  }

  const { service } = createNotificationService(supabase)

  // Get user's notifications with pagination
  const { data: notifications, error: notificationsError } = await service.getUserNotifications(
    user.id,
    { limit: 50 }
  )

  if (notificationsError) {
    console.error('Failed to load notifications:', notificationsError)
  }

  // Get unread count
  const { count: unreadCount } = await service.getUnreadCount(user.id)

  return {
    notifications: notifications || [],
    unreadCount,
    user
  }
}
