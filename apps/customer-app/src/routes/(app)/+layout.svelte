<script lang="ts">
  import { onMount } from 'svelte'
  import { goto } from '$app/navigation'
  import AppLayout from '$lib/components/layout/AppLayout.svelte'
  import { validateCustomerSession, clearCustomerSession } from '$lib/auth-security'
  import type { LayoutData } from './$types'
  
  export let data: LayoutData
  
  // Security validation on client-side navigation
  onMount(async () => {
    const validation = await validateCustomerSession()
    
    if (!validation.valid) {
      console.log('[SECURITY] Invalid session detected:', validation.reason)
      await clearCustomerSession()
      goto('/login?error=session_invalid')
    }
  })
</script>

<AppLayout user={data.user} company={data.company}>
  <slot />
</AppLayout>
