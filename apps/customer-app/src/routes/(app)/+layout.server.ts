import { redirect } from '@sveltejs/kit'
import type { LayoutServerLoad } from './$types'
import { determineUserRedirect } from '$lib/server/auth'

export const load: LayoutServerLoad = async ({ locals, url }) => {
  // The hooks.server.ts file already validates the user and customerUser.
  // If we reach this point, locals.user and locals.customerUser are considered valid.
  if (!locals.user || !locals.customerUser) {
    // This is a safety net, but the hook should have already redirected.
    throw redirect(302, '/login')
  }

  // Use the centralized redirect logic to handle complex cases
  const redirectPath = determineUserRedirect(locals.customerUser, url.pathname)
  if (redirectPath) {
    throw redirect(303, redirectPath)
  }
  
  // Get company info for business users
  let company = null
  try {
    if (locals.customerUser.company_id) {
      const { data: companyData, error } = await locals.supabase
        .from('companies')
        .select('*')
        .eq('id', locals.customerUser.company_id)
        .single()

      if (error) throw error
      company = companyData
    }
  } catch (error) {
    console.error('Layout load error (company fetch):', error)
    // Invalidate the session if the company is missing for a supposedly valid user
    await locals.supabase.auth.signOut()
    throw redirect(303, '/login?error=session_invalid')
  }

  return {
    user: locals.user,
    customerUser: locals.customerUser,
    company
  }
}
