<!-- Notification preferences page -->
<script lang="ts">
  import { enhance } from '$app/forms'
  import { Bell, Mail, Smartphone, Settings } from 'lucide-svelte'
  import type { PageData, ActionData } from './$types'
  import type { NotificationType } from '$lib/notification-service'

  export let data: PageData
  export let form: ActionData

  let saving = false
  let preferences = new Map()

  // Initialize preferences from server data
  data.notificationTypes.forEach((type: NotificationType) => {
    const existingPref = data.preferences.get(type.id)
    preferences.set(type.id, {
      notification_type_id: type.id,
      email_enabled: existingPref?.email_enabled ?? type.default_enabled,
      in_app_enabled: existingPref?.in_app_enabled ?? type.default_enabled,
      push_enabled: existingPref?.push_enabled ?? false
    })
  })

  // Group notification types by category
  $: groupedTypes = data.notificationTypes.reduce((acc: Record<string, NotificationType[]>, type: NotificationType) => {
    if (!acc[type.category]) {
      acc[type.category] = []
    }
    acc[type.category].push(type)
    return acc
  }, {})

  function updatePreference(typeId: string, field: string, value: boolean) {
    const current = preferences.get(typeId)
    preferences.set(typeId, { ...current, [field]: value })
    preferences = new Map(preferences) // Trigger reactivity
  }

  function getCategoryDisplayName(category: string): string {
    const categoryNames: Record<string, string> = {
      user_management: 'User Management',
      jobs: 'Jobs & Recruiting',
      applications: 'Applications',
      interviews: 'Interviews',
      system: 'System & Security'
    }
    return categoryNames[category] || category
  }

  function getCategoryDescription(category: string): string {
    const descriptions: Record<string, string> = {
      user_management: 'Team member invitations, role changes, and account updates',
      jobs: 'New job postings, applications, and status changes',
      applications: 'Candidate application updates and status changes',
      interviews: 'Interview scheduling, reminders, and feedback',
      system: 'System maintenance, security alerts, and feature announcements'
    }
    return descriptions[category] || ''
  }
</script>

<svelte:head>
  <title>Notification Preferences - ProcureServe</title>
</svelte:head>

<div class="max-w-4xl mx-auto">
  <div class="mb-8">
    <div class="flex items-center space-x-3 mb-4">
      <Bell size={24} class="text-gray-700" />
      <h1 class="text-2xl font-bold text-gray-900">Notification Preferences</h1>
    </div>
    <p class="text-gray-600">
      Choose how you want to be notified about important events and updates.
    </p>
  </div>

  {#if form?.success}
    <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
      <p class="text-green-800">Your notification preferences have been updated.</p>
    </div>
  {/if}

  {#if form?.error}
    <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
      <p class="text-red-800">{form.error}</p>
    </div>
  {/if}

  <form 
    method="POST" 
    action="?/updatePreferences"
    use:enhance={() => {
      saving = true
      return async ({ update }) => {
        await update()
        saving = false
      }
    }}
  >
    <input 
      type="hidden" 
      name="preferences" 
      value={JSON.stringify(Array.from(preferences.values()))}
    />

    <div class="space-y-8">
      {#each Object.entries(groupedTypes) as [category, types]}
        <div class="bg-white rounded-lg shadow border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">
              {getCategoryDisplayName(category)}
            </h3>
            <p class="text-sm text-gray-500 mt-1">
              {getCategoryDescription(category)}
            </p>
          </div>

          <div class="divide-y divide-gray-200">
            {#each types as type}
              {@const pref = preferences.get(type.id)}
              <div class="px-6 py-4">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-2">
                      <h4 class="text-sm font-medium text-gray-900">
                        {type.display_name}
                      </h4>
                      <div 
                        class="w-3 h-3 rounded-full" 
                        style="background-color: {type.color}"
                      ></div>
                    </div>
                    <p class="text-sm text-gray-600 mb-3">
                      {type.description}
                    </p>

                    <div class="flex items-center space-x-6">
                      <!-- In-app notifications -->
                      {#if type.supports_in_app}
                        <label class="flex items-center space-x-2">
                          <input 
                            type="checkbox" 
                            checked={pref?.in_app_enabled}
                            on:change={(e) => updatePreference(type.id, 'in_app_enabled', e.target.checked)}
                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <Bell size={16} class="text-gray-400" />
                          <span class="text-sm text-gray-700">In-app</span>
                        </label>
                      {/if}

                      <!-- Email notifications -->
                      {#if type.supports_email}
                        <label class="flex items-center space-x-2">
                          <input 
                            type="checkbox" 
                            checked={pref?.email_enabled}
                            on:change={(e) => updatePreference(type.id, 'email_enabled', e.target.checked)}
                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <Mail size={16} class="text-gray-400" />
                          <span class="text-sm text-gray-700">Email</span>
                        </label>
                      {/if}

                      <!-- Push notifications -->
                      {#if type.supports_push}
                        <label class="flex items-center space-x-2">
                          <input 
                            type="checkbox" 
                            checked={pref?.push_enabled}
                            on:change={(e) => updatePreference(type.id, 'push_enabled', e.target.checked)}
                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <Smartphone size={16} class="text-gray-400" />
                          <span class="text-sm text-gray-700">Push</span>
                        </label>
                      {/if}
                    </div>
                  </div>
                </div>
              </div>
            {/each}
          </div>
        </div>
      {/each}
    </div>

    <div class="mt-8 flex justify-end">
      <button 
        type="submit"
        disabled={saving}
        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
      >
        {#if saving}
          <Settings size={16} class="animate-spin" />
          <span>Saving...</span>
        {:else}
          <span>Save Preferences</span>
        {/if}
      </button>
    </div>
  </form>
</div>
