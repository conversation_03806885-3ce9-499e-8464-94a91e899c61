// Notification preferences server logic
import { error, fail } from '@sveltejs/kit'
import { redirect } from '@sveltejs/kit'
import type { PageServerLoad, Actions } from './$types'

export const load: PageServerLoad = async ({ locals: { supabase, user } }) => {
  if (!user) {
    throw redirect(303, '/login')
  }

  // Get notification types for the company
  const { data: notificationTypes, error: typesError } = await supabase
    .from('notification_types')
    .select('*')
    .eq('company_id', user.company_id)
    .order('category', { ascending: true })

  if (typesError) {
    console.error('Failed to load notification types:', typesError)
    throw error(500, 'Failed to load notification types')
  }

  // Get user's current preferences
  const { data: preferences, error: prefsError } = await supabase
    .from('user_notification_preferences')
    .select('*, notification_type(*)')
    .eq('user_id', user.id)

  if (prefsError) {
    console.error('Failed to load preferences:', prefsError)
  }

  // Create a map of preferences by notification type ID
  const preferencesMap = new Map()
  preferences?.forEach(pref => {
    preferencesMap.set(pref.notification_type_id, pref)
  })

  return {
    notificationTypes: notificationTypes || [],
    preferences: preferencesMap,
    user
  }
}

export const actions: Actions = {
  updatePreferences: async ({ request, locals: { supabase, user } }) => {
    if (!user) {
      return fail(401, { error: 'Unauthorized' })
    }

    const formData = await request.formData()
    const preferences = JSON.parse(formData.get('preferences') as string)

    try {
      // Update preferences for each notification type
      for (const pref of preferences) {
        const { error: upsertError } = await supabase
          .from('user_notification_preferences')
          .upsert({
            user_id: user.id,
            company_id: user.company_id,
            notification_type_id: pref.notification_type_id,
            email_enabled: pref.email_enabled,
            in_app_enabled: pref.in_app_enabled,
            push_enabled: pref.push_enabled
          })

        if (upsertError) {
          console.error('Failed to update preference:', upsertError)
          return fail(500, { error: 'Failed to update preferences' })
        }
      }

      return { success: true }
    } catch (err) {
      console.error('Error updating preferences:', err)
      return fail(500, { error: 'Failed to update preferences' })
    }
  }
}
