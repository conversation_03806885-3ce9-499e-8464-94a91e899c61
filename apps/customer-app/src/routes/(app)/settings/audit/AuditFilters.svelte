<script lang="ts">
  import { createEventDispatcher } from 'svelte'

  export let filters: any
  export let users: any[]
  export let actionTypes: string[]

  const dispatch = createEventDispatcher()

  let localFilters = { ...filters }

  const formatUserName = (user: any) => {
    if (user.profile?.first_name || user.profile?.last_name) {
      return `${user.profile.first_name || ''} ${user.profile.last_name || ''}`.trim()
    }
    return user.email?.split('@')[0] || 'Unknown'
  }

  const formatActionType = (actionType: string) => {
    return actionType.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const applyFilters = () => {
    dispatch('filterChange', localFilters)
  }

  const clearFilters = () => {
    localFilters = {
      actionType: '',
      performedBy: '',
      dateFrom: '',
      dateTo: ''
    }
    dispatch('filterChange', localFilters)
  }

  const hasActiveFilters = () => {
    return Object.values(localFilters).some(value => value)
  }

  // Common action types for quick filtering
  const commonActionTypes = [
    'user_created',
    'user_updated', 
    'user_deactivated',
    'user_invited',
    'password_reset_requested',
    'business_unit_created',
    'business_unit_updated',
    'business_unit_deleted'
  ]

  // Get today's date in YYYY-MM-DD format for max date
  const today = new Date().toISOString().split('T')[0]
</script>

<div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
  <div class="flex items-center justify-between mb-4">
    <h3 class="text-lg font-medium text-gray-900">Filters</h3>
    {#if hasActiveFilters()}
      <button
        type="button"
        on:click={clearFilters}
        class="text-sm text-red-600 hover:text-red-800"
      >
        Clear all filters
      </button>
    {/if}
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
    <!-- Action Type Filter -->
    <div>
      <label for="actionType" class="block text-sm font-medium text-gray-700 mb-2">
        Action Type
      </label>
      <select
        id="actionType"
        bind:value={localFilters.actionType}
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
      >
        <option value="">All actions</option>
        {#each commonActionTypes as actionType}
          <option value={actionType}>{formatActionType(actionType)}</option>
        {/each}
        {#each actionTypes.filter(type => !commonActionTypes.includes(type)) as actionType}
          <option value={actionType}>{formatActionType(actionType)}</option>
        {/each}
      </select>
    </div>

    <!-- User Filter -->
    <div>
      <label for="performedBy" class="block text-sm font-medium text-gray-700 mb-2">
        Performed By
      </label>
      <select
        id="performedBy"
        bind:value={localFilters.performedBy}
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
      >
        <option value="">All users</option>
        {#each users as user}
          <option value={user.id}>
            {formatUserName(user)} ({user.email})
          </option>
        {/each}
      </select>
    </div>

    <!-- Date From -->
    <div>
      <label for="dateFrom" class="block text-sm font-medium text-gray-700 mb-2">
        From Date
      </label>
      <input
        type="date"
        id="dateFrom"
        bind:value={localFilters.dateFrom}
        max={today}
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
      />
    </div>

    <!-- Date To -->
    <div>
      <label for="dateTo" class="block text-sm font-medium text-gray-700 mb-2">
        To Date
      </label>
      <input
        type="date"
        id="dateTo"
        bind:value={localFilters.dateTo}
        max={today}
        min={localFilters.dateFrom || undefined}
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
      />
    </div>
  </div>

  <!-- Quick Filters -->
  <div class="mb-4">
    <label class="block text-sm font-medium text-gray-700 mb-2">
      Quick Filters
    </label>
    <div class="flex flex-wrap gap-2">
      <button
        type="button"
        on:click={() => {
          localFilters.dateFrom = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]
          localFilters.dateTo = today
        }}
        class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200"
      >
        Last 24 hours
      </button>
      
      <button
        type="button"
        on:click={() => {
          localFilters.dateFrom = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
          localFilters.dateTo = today
        }}
        class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200"
      >
        Last 7 days
      </button>
      
      <button
        type="button"
        on:click={() => {
          localFilters.dateFrom = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
          localFilters.dateTo = today
        }}
        class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200"
      >
        Last 30 days
      </button>
      
      <button
        type="button"
        on:click={() => {
          localFilters.actionType = 'user_created'
        }}
        class="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-full hover:bg-green-200"
      >
        User Created
      </button>
      
      <button
        type="button"
        on:click={() => {
          localFilters.actionType = 'user_deactivated'
        }}
        class="px-3 py-1 text-xs bg-red-100 text-red-700 rounded-full hover:bg-red-200"
      >
        User Deactivated
      </button>
      
      <button
        type="button"
        on:click={() => {
          localFilters.actionType = 'password_reset_requested'
        }}
        class="px-3 py-1 text-xs bg-yellow-100 text-yellow-700 rounded-full hover:bg-yellow-200"
      >
        Password Resets
      </button>
    </div>
  </div>

  <!-- Apply Filters Button -->
  <div class="flex items-center justify-end">
    <button
      type="button"
      on:click={applyFilters}
      class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700"
    >
      Apply Filters
    </button>
  </div>
</div>
