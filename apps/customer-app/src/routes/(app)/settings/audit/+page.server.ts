import type { PageServerLoad } from './$types'
import { error, redirect } from '@sveltejs/kit'
import type { CompanyAuditLog } from '$shared-types'

export const load: PageServerLoad = async ({ url, locals: { supabase, user, session } }) => {
  if (!session || !user) {
    throw redirect(302, '/login')
  }

  // Only admin and manager users can view audit logs
  if (!['admin', 'manager'].includes(user.role)) {
    throw error(403, 'Insufficient permissions')
  }

  // Parse query parameters for filtering
  const page = parseInt(url.searchParams.get('page') || '1')
  const limit = parseInt(url.searchParams.get('limit') || '50')
  const actionType = url.searchParams.get('action_type') || ''
  const performedBy = url.searchParams.get('performed_by') || ''
  const dateFrom = url.searchParams.get('date_from') || ''
  const dateTo = url.searchParams.get('date_to') || ''

  const offset = (page - 1) * limit

  // Build query
  let query = supabase
    .from('company_audit_logs')
    .select(`
      *,
      performed_by_user:users!company_audit_logs_performed_by_fkey(id, email, profile),
      target_user:users!company_audit_logs_target_user_id_fkey(id, email, profile)
    `)
    .eq('company_id', user.company_id)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1)

  // Apply filters
  if (actionType) {
    query = query.eq('action_type', actionType)
  }
  if (performedBy) {
    query = query.eq('performed_by', performedBy)
  }
  if (dateFrom) {
    query = query.gte('created_at', new Date(dateFrom).toISOString())
  }
  if (dateTo) {
    query = query.lte('created_at', new Date(dateTo + 'T23:59:59').toISOString())
  }

  const { data: auditLogs, error: logsError } = await query

  if (logsError) {
    console.error('Audit logs load error:', logsError)
  }

  // Get total count for pagination
  let countQuery = supabase
    .from('company_audit_logs')
    .select('*', { count: 'exact', head: true })
    .eq('company_id', user.company_id)

  if (actionType) countQuery = countQuery.eq('action_type', actionType)
  if (performedBy) countQuery = countQuery.eq('performed_by', performedBy)
  if (dateFrom) countQuery = countQuery.gte('created_at', new Date(dateFrom).toISOString())
  if (dateTo) countQuery = countQuery.lte('created_at', new Date(dateTo + 'T23:59:59').toISOString())

  const { count } = await countQuery

  // Get users for filter dropdown
  const { data: users } = await supabase
    .from('users')
    .select('id, email, profile')
    .eq('company_id', user.company_id)
    .order('email')

  // Get recent statistics
  const { data: recentStats } = await supabase
    .from('company_audit_logs')
    .select('action_type, created_at')
    .eq('company_id', user.company_id)
    .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())

  return {
    auditLogs: auditLogs || [],
    users: users || [],
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit),
      hasNext: count ? offset + limit < count : false,
      hasPrev: page > 1
    },
    filters: {
      actionType,
      performedBy,
      dateFrom,
      dateTo
    },
    stats: {
      totalLogs: count || 0,
      recentActivity: recentStats?.length || 0,
      actionTypes: [...new Set(recentStats?.map(log => log.action_type) || [])]
    },
    canExport: user.role === 'admin'
  }
}
