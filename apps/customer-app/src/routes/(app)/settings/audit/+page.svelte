<script lang="ts">
  import { page } from '$app/stores'
  import { goto } from '$app/navigation'
  import type { PageData } from './$types'
  import AuditLogTable from './AuditLogTable.svelte'
  import AuditFilters from './AuditFilters.svelte'

  export let data: PageData

  $: ({ auditLogs, users, pagination, filters, stats, canExport } = data)

  const handleFilterChange = (newFilters: any) => {
    const params = new URLSearchParams()
    
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) params.set(key, value as string)
    })
    
    // Reset to page 1 when filters change
    params.set('page', '1')
    
    goto(`/settings/audit?${params.toString()}`)
  }

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams($page.url.searchParams)
    params.set('page', newPage.toString())
    goto(`/settings/audit?${params.toString()}`)
  }

  const exportAuditLogs = () => {
    // Create CSV content
    const headers = ['Date', 'User', 'Action', 'Target', 'IP Address', 'Details']
    const csvContent = [
      headers.join(','),
      ...auditLogs.map(log => [
        new Date(log.created_at).toISOString(),
        log.performed_by_user?.email || 'Unknown',
        log.action_type,
        log.target_user?.email || 'N/A',
        log.ip_address || 'Unknown',
        `"${JSON.stringify(log.new_values || {}).replace(/"/g, '""')}"`
      ].join(','))
    ].join('\n')

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }
</script>

<svelte:head>
  <title>Audit Trail | ProcureServe</title>
</svelte:head>

<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
  <!-- Header -->
  <div class="mb-8">
    <nav class="flex items-center text-sm text-gray-500 mb-4">
      <a href="/settings" class="hover:text-gray-700">Settings</a>
      <span class="mx-2">/</span>
      <span class="text-gray-900">Audit Trail</span>
    </nav>
    
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Audit Trail</h1>
        <p class="mt-1 text-sm text-gray-600">
          Monitor security events and administrative actions
        </p>
      </div>
      
      {#if canExport && auditLogs.length > 0}
        <button 
          type="button"
          on:click={exportAuditLogs}
          class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700"
        >
          Export CSV
        </button>
      {/if}
    </div>
  </div>

  <!-- Statistics -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Total Logs</p>
          <p class="text-2xl font-bold text-gray-900">{stats.totalLogs}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Last 7 Days</p>
          <p class="text-2xl font-bold text-gray-900">{stats.recentActivity}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Action Types</p>
          <p class="text-2xl font-bold text-gray-900">{stats.actionTypes.length}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <AuditFilters 
    {filters}
    {users}
    actionTypes={stats.actionTypes}
    on:filterChange={(e) => handleFilterChange(e.detail)}
  />

  <!-- Audit Logs Table -->
  <AuditLogTable 
    {auditLogs}
    {pagination}
    on:pageChange={(e) => handlePageChange(e.detail)}
  />

  {#if auditLogs.length === 0}
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No audit logs found</h3>
      <p class="mt-1 text-sm text-gray-500">
        {Object.values(filters).some(f => f) ? 'Try adjusting your filters.' : 'Audit logs will appear here as users perform actions.'}
      </p>
    </div>
  {/if}
</div>
