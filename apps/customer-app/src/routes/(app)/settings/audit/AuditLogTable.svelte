<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import type { CompanyAuditLog } from '$shared-types'

  export let auditLogs: (CompanyAuditLog & { performed_by_user?: any, target_user?: any })[]
  export let pagination: any

  const dispatch = createEventDispatcher()

  const formatUserName = (user: any) => {
    if (!user) return 'Unknown User'
    if (user.profile?.first_name || user.profile?.last_name) {
      return `${user.profile.first_name || ''} ${user.profile.last_name || ''}`.trim()
    }
    return user.email?.split('@')[0] || 'Unknown'
  }

  const formatActionType = (actionType: string) => {
    return actionType.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const getActionTypeColor = (actionType: string) => {
    const colors = {
      'user_created': 'bg-green-100 text-green-800',
      'user_updated': 'bg-blue-100 text-blue-800',
      'user_deactivated': 'bg-red-100 text-red-800',
      'user_invited': 'bg-purple-100 text-purple-800',
      'password_reset_requested': 'bg-yellow-100 text-yellow-800',
      'business_unit_created': 'bg-green-100 text-green-800',
      'business_unit_updated': 'bg-blue-100 text-blue-800',
      'business_unit_deleted': 'bg-red-100 text-red-800',
      'login_success': 'bg-green-100 text-green-800',
      'login_failed': 'bg-red-100 text-red-800'
    }
    return colors[actionType] || 'bg-gray-100 text-gray-800'
  }

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return `${diffInSeconds}s ago`
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
    
    return date.toLocaleDateString()
  }

  const formatChanges = (oldValues: any, newValues: any) => {
    if (!oldValues && !newValues) return 'No details'
    
    if (!oldValues && newValues) {
      // Creation event
      const keys = Object.keys(newValues).filter(k => k !== 'id' && k !== 'created_at' && k !== 'updated_at')
      return keys.length > 0 ? `Created with: ${keys.join(', ')}` : 'Created'
    }
    
    if (oldValues && newValues) {
      // Update event
      const changes = []
      for (const [key, newValue] of Object.entries(newValues)) {
        if (oldValues[key] !== newValue && key !== 'updated_at') {
          changes.push(`${key}: ${oldValues[key]} → ${newValue}`)
        }
      }
      return changes.length > 0 ? changes.join(', ') : 'No changes detected'
    }
    
    return 'Deleted'
  }

  let expandedLogs = new Set()

  const toggleExpanded = (logId: string) => {
    if (expandedLogs.has(logId)) {
      expandedLogs.delete(logId)
    } else {
      expandedLogs.add(logId)
    }
    expandedLogs = expandedLogs
  }
</script>

<div class="bg-white shadow rounded-lg overflow-hidden">
  <!-- Table -->
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Time
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            User
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Action
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Target
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            IP Address
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Details
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        {#each auditLogs as log}
          <tr class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <div class="flex flex-col">
                <span class="font-medium">{formatRelativeTime(log.created_at)}</span>
                <span class="text-xs text-gray-500">
                  {new Date(log.created_at).toLocaleString()}
                </span>
              </div>
            </td>
            
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-8 w-8 bg-indigo-100 rounded-full flex items-center justify-center">
                  <span class="text-xs font-medium text-indigo-800">
                    {formatUserName(log.performed_by_user).charAt(0).toUpperCase()}
                  </span>
                </div>
                <div class="ml-3">
                  <div class="text-sm font-medium text-gray-900">
                    {formatUserName(log.performed_by_user)}
                  </div>
                  <div class="text-xs text-gray-500">
                    {log.performed_by_user?.email || 'Unknown'}
                  </div>
                </div>
              </div>
            </td>
            
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full {getActionTypeColor(log.action_type)}">
                {formatActionType(log.action_type)}
              </span>
            </td>
            
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {#if log.target_user}
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-6 w-6 bg-gray-100 rounded-full flex items-center justify-center">
                    <span class="text-xs text-gray-600">
                      {formatUserName(log.target_user).charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div class="ml-2">
                    <div class="text-sm text-gray-900">{formatUserName(log.target_user)}</div>
                  </div>
                </div>
              {:else}
                <span class="text-gray-500">-</span>
              {/if}
            </td>
            
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {log.ip_address || 'Unknown'}
            </td>
            
            <td class="px-6 py-4 text-sm text-gray-900">
              <div class="max-w-xs">
                <div class="truncate">
                  {formatChanges(log.old_values, log.new_values)}
                </div>
                {#if (log.old_values || log.new_values)}
                  <button
                    type="button"
                    on:click={() => toggleExpanded(log.id)}
                    class="text-xs text-indigo-600 hover:text-indigo-800 mt-1"
                  >
                    {expandedLogs.has(log.id) ? 'Show less' : 'Show more'}
                  </button>
                {/if}
              </div>
            </td>
          </tr>
          
          <!-- Expanded Details Row -->
          {#if expandedLogs.has(log.id)}
            <tr class="bg-gray-50">
              <td colspan="6" class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                  {#if log.old_values}
                    <div>
                      <h4 class="font-medium text-gray-900 mb-2">Before</h4>
                      <pre class="bg-white p-2 rounded border text-xs overflow-x-auto">{JSON.stringify(log.old_values, null, 2)}</pre>
                    </div>
                  {/if}
                  
                  {#if log.new_values}
                    <div>
                      <h4 class="font-medium text-gray-900 mb-2">After</h4>
                      <pre class="bg-white p-2 rounded border text-xs overflow-x-auto">{JSON.stringify(log.new_values, null, 2)}</pre>
                    </div>
                  {/if}
                  
                  <div class="md:col-span-2">
                    <h4 class="font-medium text-gray-900 mb-2">Additional Info</h4>
                    <div class="text-gray-600">
                      <p>User Agent: {log.user_agent || 'Unknown'}</p>
                      <p>Log ID: {log.id}</p>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          {/if}
        {/each}
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  {#if pagination.totalPages > 1}
    <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
      <div class="flex items-center justify-between">
        <div class="flex-1 flex justify-between items-center">
          <div class="text-sm text-gray-700">
            Showing page {pagination.page} of {pagination.totalPages} 
            ({pagination.total} total logs)
          </div>
          
          <div class="flex space-x-2">
            <button
              type="button"
              disabled={!pagination.hasPrev}
              on:click={() => dispatch('pageChange', pagination.page - 1)}
              class="px-3 py-1 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            
            <span class="px-3 py-1 text-sm bg-indigo-600 text-white border border-indigo-600 rounded-md">
              {pagination.page}
            </span>
            
            <button
              type="button"
              disabled={!pagination.hasNext}
              on:click={() => dispatch('pageChange', pagination.page + 1)}
              class="px-3 py-1 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>
