import type { PageServerLoad, Actions } from './$types'
import { error, redirect, fail } from '@sveltejs/kit'
import type { UserEditForm, UserManagementUser, CompanyAuditLog } from '$shared-types'

export const load: PageServerLoad = async ({ params, locals: { supabase, user, session } }) => {
  if (!session || !user) {
    throw redirect(302, '/login')
  }

  // Only admin and manager users can edit users
  if (!['admin', 'manager'].includes(user.role)) {
    throw error(403, 'Insufficient permissions')
  }

  const { id: userId } = params

  // Load user to edit
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select(`
      *,
      business_unit:business_units(id, name),
      invited_by_user:users!users_invited_by_fkey(id, email, profile)
    `)
    .eq('id', userId)
    .eq('company_id', user.company_id)
    .single()

  if (userError || !userData) {
    throw error(404, 'User not found')
  }

  // Load business units for selection
  const { data: businessUnits } = await supabase
    .from('business_units')
    .select('id, name, description')
    .eq('company_id', user.company_id)
    .order('name')

  return {
    user: userData as UserManagementUser,
    businessUnits: businessUnits || [],
    canEditRole: user.role === 'admin' // Only admin can change roles
  }
}

export const actions: Actions = {
  updateUser: async ({ request, params, locals: { supabase, user, session } }) => {
    if (!session || !user) {
      return fail(401, { message: 'Unauthorized' })
    }

    if (!['admin', 'manager'].includes(user.role)) {
      return fail(403, { message: 'Insufficient permissions' })
    }

    const { id: userId } = params
    const formData = await request.formData()
    
    const updateData: Partial<UserEditForm> = {
      process_permissions: formData.getAll('process_permissions') as ('recruitment' | 'bench_sales')[],
      is_active: formData.get('is_active') === 'true'
    }

    // Only admin can change roles and business units
    if (user.role === 'admin') {
      updateData.role = formData.get('role') as any
      const businessUnitId = formData.get('business_unit_id') as string
      updateData.business_unit_id = businessUnitId || null
    }

    // Store old values for audit log
    const { data: oldUser } = await supabase
      .from('users')
      .select('role, process_permissions, business_unit_id, is_active')
      .eq('id', userId)
      .eq('company_id', user.company_id)
      .single()

    if (!oldUser) {
      return fail(404, { message: 'User not found' })
    }

    // Update user
    const { error: updateError } = await supabase
      .from('users')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .eq('company_id', user.company_id)

    if (updateError) {
      console.error('User update error:', updateError)
      return fail(500, { message: 'Failed to update user' })
    }

    // Create audit log
    const auditLogData: Omit<CompanyAuditLog, 'id' | 'created_at'> = {
      company_id: user.company_id,
      performed_by: user.id,
      action_type: 'user_updated',
      target_user_id: userId,
      old_values: oldUser,
      new_values: updateData,
      ip_address: request.headers.get('x-forwarded-for') || 'unknown',
      user_agent: request.headers.get('user-agent') || 'unknown'
    }

    await supabase.from('company_audit_logs').insert(auditLogData)

    return { success: true, message: 'User updated successfully' }
  },

  deactivateUser: async ({ params, locals: { supabase, user, session } }) => {
    if (!session || !user) {
      return fail(401, { message: 'Unauthorized' })
    }

    if (user.role !== 'admin') {
      return fail(403, { message: 'Only admin users can deactivate users' })
    }

    const { id: userId } = params

    // Prevent admin from deactivating themselves
    if (userId === user.id) {
      return fail(400, { message: 'Cannot deactivate your own account' })
    }

    const { error: updateError } = await supabase
      .from('users')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .eq('company_id', user.company_id)

    if (updateError) {
      return fail(500, { message: 'Failed to deactivate user' })
    }

    // Create audit log
    const auditLogData: Omit<CompanyAuditLog, 'id' | 'created_at'> = {
      company_id: user.company_id,
      performed_by: user.id,
      action_type: 'user_deactivated',
      target_user_id: userId,
      old_values: { is_active: true },
      new_values: { is_active: false }
    }

    await supabase.from('company_audit_logs').insert(auditLogData)

    return { success: true, message: 'User deactivated successfully' }
  },

  resetPassword: async ({ params, locals: { supabase, user, session } }) => {
    if (!session || !user) {
      return fail(401, { message: 'Unauthorized' })
    }

    if (!['admin', 'manager'].includes(user.role)) {
      return fail(403, { message: 'Insufficient permissions' })
    }

    const { id: userId } = params

    // Get user email for password reset
    const { data: targetUser } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .eq('company_id', user.company_id)
      .single()

    if (!targetUser) {
      return fail(404, { message: 'User not found' })
    }

    // Send password reset email via Supabase Auth
    const { error: resetError } = await supabase.auth.resetPasswordForEmail(
      targetUser.email,
      {
        redirectTo: `${process.env.PUBLIC_SITE_URL}/reset-password`
      }
    )

    if (resetError) {
      console.error('Password reset error:', resetError)
      return fail(500, { message: 'Failed to send password reset email' })
    }

    // Create audit log
    const auditLogData: Omit<CompanyAuditLog, 'id' | 'created_at'> = {
      company_id: user.company_id,
      performed_by: user.id,
      action_type: 'password_reset_requested',
      target_user_id: userId,
      old_values: null,
      new_values: { email_sent: targetUser.email }
    }

    await supabase.from('company_audit_logs').insert(auditLogData)

    return { success: true, message: 'Password reset email sent successfully' }
  }
}
