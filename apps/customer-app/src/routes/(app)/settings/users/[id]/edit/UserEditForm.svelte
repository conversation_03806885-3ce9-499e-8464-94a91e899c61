<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { enhance } from '$app/forms'
  import type { UserManagementUser, BusinessUnit } from '$shared-types'

  export let user: UserManagementUser
  export let businessUnits: BusinessUnit[]
  export let canEditRole: boolean
  export let loading: boolean = false

  const dispatch = createEventDispatcher()

  let formData = {
    role: user.role,
    process_permissions: [...user.process_permissions],
    business_unit_id: user.business_unit_id || '',
    is_active: user.is_active
  }

  let showDeactivateModal = false

  const handlePermissionChange = (permission: 'recruitment' | 'bench_sales', checked: boolean) => {
    if (checked) {
      formData.process_permissions = [...formData.process_permissions, permission]
    } else {
      formData.process_permissions = formData.process_permissions.filter(p => p !== permission)
    }
  }

  const formatUserName = (user: UserManagementUser) => {
    if (user.profile?.first_name || user.profile?.last_name) {
      return `${user.profile.first_name || ''} ${user.profile.last_name || ''}`.trim()
    }
    return user.email.split('@')[0]
  }

  const handleResetPassword = () => {
    dispatch('resetPassword')
  }

  const handleDeactivateUser = () => {
    dispatch('deactivateUser')
  }
</script>

<div class="bg-white shadow rounded-lg">
  <div class="px-6 py-4 border-b border-gray-200">
    <h2 class="text-lg font-medium text-gray-900">User Information</h2>
  </div>

  <form 
    method="POST" 
    action="?/updateUser"
    use:enhance={() => {
      loading = true
      return async ({ result }) => {
        loading = false
        if (result.type === 'success') {
          dispatch('success')
        }
      }
    }}
    class="p-6 space-y-6"
  >
    <!-- User Details (Read-only) -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 class="block text-sm font-medium text-gray-700 mb-2">User</h3>
        <div class="flex items-center">
          <div class="flex-shrink-0 h-10 w-10 bg-indigo-100 rounded-full flex items-center justify-center">
            <span class="text-sm font-medium text-indigo-800">
              {formatUserName(user).charAt(0).toUpperCase()}
            </span>
          </div>
          <div class="ml-3">
            <div class="text-sm font-medium text-gray-900">{formatUserName(user)}</div>
            <div class="text-sm text-gray-500">{user.email}</div>
          </div>
        </div>
      </div>

      <div>
        <h3 class="block text-sm font-medium text-gray-700 mb-2">Current Status</h3>
        <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full {user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
          {user.is_active ? 'Active' : 'Inactive'}
        </span>
      </div>
    </div>

    <!-- Role Selection (Admin Only) -->
    {#if canEditRole}
      <div>
        <label for="role" class="block text-sm font-medium text-gray-700 mb-2">Role</label>
        <select 
          id="role"
          name="role"
          bind:value={formData.role}
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          required
        >
          <option value="admin">Admin</option>
          <option value="manager">Manager</option>
          <option value="recruiter">Recruiter</option>
          <option value="viewer">Viewer</option>
        </select>
        <p class="mt-1 text-xs text-gray-500">Only admin users can modify user roles</p>
      </div>
    {:else}
      <div>
        <h3 class="block text-sm font-medium text-gray-700 mb-2">Role</h3>
        <div class="text-sm text-gray-900 capitalize">{user.role}</div>
        <p class="mt-1 text-xs text-gray-500">Contact an admin to change user roles</p>
      </div>
    {/if}

    <!-- Process Permissions -->
    <div>
      <h3 class="block text-sm font-medium text-gray-700 mb-3">Process Access</h3>
      <div class="space-y-3">
        <label class="flex items-center">
          <input
            type="checkbox"
            name="process_permissions"
            value="recruitment"
            checked={formData.process_permissions.includes('recruitment')}
            on:change={(e) => handlePermissionChange('recruitment', e.currentTarget.checked)}
            class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
          />
          <span class="ml-3 text-sm text-gray-700">Recruitment Process</span>
        </label>
        
        <label class="flex items-center">
          <input
            type="checkbox"
            name="process_permissions"
            value="bench_sales"
            checked={formData.process_permissions.includes('bench_sales')}
            on:change={(e) => handlePermissionChange('bench_sales', e.currentTarget.checked)}
            class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
          />
          <span class="ml-3 text-sm text-gray-700">Bench Sales Process</span>
        </label>
      </div>
      <p class="mt-2 text-xs text-gray-500">Users need at least one process permission to access the system</p>
    </div>

    <!-- Business Unit (Admin Only) -->
    {#if canEditRole && businessUnits.length > 0}
      <div>
        <label for="business_unit_id" class="block text-sm font-medium text-gray-700 mb-2">Business Unit</label>
        <select 
          id="business_unit_id"
          name="business_unit_id"
          bind:value={formData.business_unit_id}
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
        >
          <option value="">No specific unit</option>
          {#each businessUnits as unit}
            <option value={unit.id}>{unit.name}</option>
          {/each}
        </select>
      </div>
    {/if}

    <!-- Account Status -->
    <div>
      <h3 class="block text-sm font-medium text-gray-700 mb-3">Account Status</h3>
      <label class="flex items-center">
        <input
          type="checkbox"
          name="is_active"
          value="true"
          bind:checked={formData.is_active}
          class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
        />
        <span class="ml-3 text-sm text-gray-700">User account is active</span>
      </label>
      <p class="mt-1 text-xs text-gray-500">Inactive users cannot log in to the system</p>
    </div>

    <!-- Form Actions -->
    <div class="flex items-center justify-between pt-6 border-t border-gray-200">
      <div class="flex space-x-3">
        <button
          type="submit"
          disabled={loading || formData.process_permissions.length === 0}
          class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Saving...' : 'Save Changes'}
        </button>
        
        <button
          type="button"
          on:click={() => dispatch('cancel')}
          class="px-4 py-2 bg-white text-gray-700 text-sm font-medium border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Cancel
        </button>
      </div>

      <div class="flex space-x-3">
        {#if canEditRole}
          <button
            type="button"
            on:click={handleResetPassword}
            class="px-4 py-2 bg-yellow-600 text-white text-sm font-medium rounded-md hover:bg-yellow-700"
          >
            Reset Password
          </button>
        {/if}

        {#if canEditRole && user.is_active}
          <button
            type="button"
            on:click={() => showDeactivateModal = true}
            class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700"
          >
            Deactivate User
          </button>
        {/if}
      </div>
    </div>
  </form>
</div>

<!-- Deactivate Confirmation Modal -->
{#if showDeactivateModal}
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3 text-center">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Deactivate User</h3>
        <p class="text-sm text-gray-500 mb-6">
          Are you sure you want to deactivate {formatUserName(user)}? They will no longer be able to access the system.
        </p>
        
        <div class="flex justify-center space-x-3">
          <button
            type="button"
            on:click={handleDeactivateUser}
            class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700"
          >
            Deactivate
          </button>
          
          <button
            type="button"
            on:click={() => showDeactivateModal = false}
            class="px-4 py-2 bg-white text-gray-700 text-sm font-medium border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}
