<script lang="ts">
  import { page } from '$app/stores'
  import { enhance } from '$app/forms'
  import { goto } from '$app/navigation'
  import type { PageData } from './$types'
  import UserEditForm from './UserEditForm.svelte'

  export let data: PageData
  export let form: any

  $: ({ user, businessUnits, canEditRole } = data)

  let loading = false

  const handleCancel = () => {
    goto('/settings/users')
  }

  const handleSuccess = () => {
    setTimeout(() => {
      goto('/settings/users')
    }, 1500)
  }

  const handleResetPassword = async () => {
    if (!confirm('Are you sure you want to reset this user\'s password?')) {
      return
    }
    
    const form = new FormData()
    form.append('user_id', user.id)
    
    const response = await fetch('?/resetPassword', {
      method: 'POST',
      body: form
    })
    
    if (response.ok) {
      // Handle success
      alert('Password reset email sent!')
    }
  }

  const handleDeactivateUser = async () => {
    const form = new FormData()
    form.append('user_id', user.id)
    
    const response = await fetch('?/deactivateUser', {
      method: 'POST',
      body: form
    })
    
    if (response.ok) {
      // Redirect back to users list
      goto('/settings/users')
    }
  }
</script>

<svelte:head>
  <title>Edit User - {user.profile?.first_name} {user.profile?.last_name} | ProcureServe</title>
</svelte:head>

<div class="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
  <!-- Header -->
  <div class="mb-8">
    <nav class="flex items-center text-sm text-gray-500 mb-4">
      <a href="/settings" class="hover:text-gray-700">Settings</a>
      <span class="mx-2">/</span>
      <a href="/settings/users" class="hover:text-gray-700">Users</a>
      <span class="mx-2">/</span>
      <span class="text-gray-900">Edit User</span>
    </nav>
    
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Edit User</h1>
        <p class="mt-1 text-sm text-gray-600">
          Modify user permissions, role, and account status
        </p>
      </div>
      
      <button 
        type="button"
        on:click={handleCancel}
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
      >
        Back to Users
      </button>
    </div>
  </div>

  <!-- Success Message -->
  {#if form?.success}
    <div class="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-green-800">
            {form.message}
          </p>
        </div>
      </div>
    </div>
  {/if}

  <!-- Error Message -->
  {#if form?.message && !form?.success}
    <div class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-red-800">
            {form.message}
          </p>
        </div>
      </div>
    </div>
  {/if}

  <!-- User Edit Form -->
  <UserEditForm 
    {user} 
    {businessUnits} 
    {canEditRole}
    {loading}
    on:cancel={handleCancel}
    on:success={handleSuccess}
    on:resetPassword={handleResetPassword}
    on:deactivateUser={handleDeactivateUser}
  />
</div>
