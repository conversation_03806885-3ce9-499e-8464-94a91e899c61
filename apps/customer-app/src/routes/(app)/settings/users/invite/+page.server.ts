import { fail, redirect } from '@sveltejs/kit'
import type { Actions } from './$types'
import { createNotificationService } from '$lib/notification-service'

export const actions: Actions = {
  default: async ({ request, locals: { supabase, user } }) => {
    if (!user) {
      return fail(401, { error: 'Unauthorized' })
    }

    if (!['admin', 'manager'].includes(user.role)) {
      return fail(403, { error: 'Insufficient permissions' })
    }

    const formData = await request.formData()
    const email = formData.get('email')?.toString()
    const role = formData.get('role')?.toString()
    const processPermissions = JSON.parse(formData.get('process_permissions')?.toString() || '[]')
    const businessUnitId = formData.get('business_unit_id')?.toString() || null

    if (!email || !role) {
      return fail(400, { error: 'Email and role are required', email, role })
    }

    try {
      // Check if invitation already exists
      const { data: existingInvitation } = await supabase
        .from('user_invitations')
        .select('id')
        .eq('email', email)
        .eq('company_id', user.company_id)
        .is('accepted_at', null)
        .gt('expires_at', new Date().toISOString())
        .single()

      if (existingInvitation) {
        return fail(400, { error: 'Pending invitation already exists for this email', email, role })
      }

      // Create invitation
      const { data: invitation, error: inviteError } = await supabase
        .from('user_invitations')
        .insert({
          company_id: user.company_id,
          email,
          role,
          process_permissions: processPermissions,
          business_unit_id: businessUnitId,
          invited_by: user.id
        })
        .select()
        .single()

      if (inviteError) throw inviteError

      // Log the invitation action
      await supabase
        .from('company_audit_logs')
        .insert({
          company_id: user.company_id,
          performed_by: user.id,
          action_type: 'user_invited',
          new_values: {
            email,
            role,
            process_permissions: processPermissions,
            business_unit_id: businessUnitId
          }
        })

      // Send notification to all admins (except the inviter)
      const { service: notificationService, helpers } = createNotificationService(supabase)
      
      const { data: adminUsers } = await supabase
        .from('users')
        .select('id, profile')
        .eq('company_id', user.company_id)
        .eq('role', 'admin')
        .neq('id', user.id)

      if (adminUsers?.length) {
        const inviterName = `${user.profile?.first_name || ''} ${user.profile?.last_name || ''}`.trim() || user.email
        
        for (const admin of adminUsers) {
          await helpers.notifyUserInvited(
            admin.id,
            inviterName,
            email,
            role
          )
        }
      }

      throw redirect(302, '/settings/users?invited=true')

    } catch (err) {
      console.error('Error creating invitation:', err)
      return fail(500, { error: 'Failed to send invitation', email, role })
    }
  }
}
