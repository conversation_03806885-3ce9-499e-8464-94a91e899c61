<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { enhance } from '$app/forms'

  export let unit: any = null // null for create, object for edit
  export let potentialManagers: any[] = []
  export let parentOptions: any[] = []

  const dispatch = createEventDispatcher()

  let formData = {
    name: unit?.name || '',
    description: unit?.description || '',
    parent_id: unit?.parent_id || '',
    manager_id: unit?.manager_id || ''
  }

  let loading = false

  const isEditing = !!unit
  const title = isEditing ? 'Edit Business Unit' : 'Create Business Unit'
  const action = isEditing ? '?/updateBusinessUnit' : '?/createBusinessUnit'

  const formatUserName = (user: any) => {
    if (user.profile?.first_name || user.profile?.last_name) {
      return `${user.profile.first_name || ''} ${user.profile.last_name || ''}`.trim()
    }
    return user.email.split('@')[0]
  }

  // Filter parent options to prevent circular hierarchy
  $: availableParents = parentOptions.filter(option => {
    if (isEditing) {
      // Can't be parent of itself or its descendants
      return option.id !== unit.id && !isDescendant(option.id, unit.id)
    }
    return true
  })

  const isDescendant = (potentialParentId: string, unitId: string): boolean => {
    const children = parentOptions.filter(u => u.parent_id === unitId)
    return children.some(child => 
      child.id === potentialParentId || isDescendant(potentialParentId, child.id)
    )
  }
</script>

<!-- Modal Backdrop -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-8 mx-auto p-0 border w-full max-w-2xl shadow-lg rounded-lg bg-white">
    
    <!-- Modal Header -->
    <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">{title}</h3>
      <button 
        type="button"
        on:click={() => dispatch('close')}
        class="text-gray-400 hover:text-gray-600"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Modal Body -->
    <form 
      method="POST" 
      {action}
      use:enhance={() => {
        loading = true
        return async ({ result }) => {
          loading = false
          if (result.type === 'success') {
            dispatch('close')
          }
        }
      }}
      class="px-6 py-4 space-y-6"
    >
      {#if isEditing}
        <input type="hidden" name="id" value={unit.id} />
      {/if}

      <!-- Unit Name -->
      <div>
        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
          Unit Name *
        </label>
        <input
          type="text"
          id="name"
          name="name"
          bind:value={formData.name}
          required
          placeholder="e.g., Engineering, Sales, HR"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
        />
      </div>

      <!-- Description -->
      <div>
        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
          Description
        </label>
        <textarea
          id="description"
          name="description"
          bind:value={formData.description}
          rows="3"
          placeholder="Brief description of this business unit's purpose"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
        ></textarea>
      </div>

      <!-- Parent Unit -->
      <div>
        <label for="parent_id" class="block text-sm font-medium text-gray-700 mb-2">
          Parent Unit
        </label>
        <select
          id="parent_id"
          name="parent_id"
          bind:value={formData.parent_id}
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
        >
          <option value="">No parent (top-level unit)</option>
          {#each availableParents as parentUnit}
            <option value={parentUnit.id}>{parentUnit.name}</option>
          {/each}
        </select>
        <p class="mt-1 text-xs text-gray-500">
          Create organizational hierarchy by assigning parent units
        </p>
      </div>

      <!-- Manager -->
      <div>
        <label for="manager_id" class="block text-sm font-medium text-gray-700 mb-2">
          Manager
        </label>
        <select
          id="manager_id"
          name="manager_id"
          bind:value={formData.manager_id}
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
        >
          <option value="">No manager assigned</option>
          {#each potentialManagers as manager}
            <option value={manager.id}>
              {formatUserName(manager)} ({manager.email})
            </option>
          {/each}
        </select>
        <p class="mt-1 text-xs text-gray-500">
          Only admin and manager role users can be assigned as managers
        </p>
      </div>

      <!-- Form Actions -->
      <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          on:click={() => dispatch('close')}
          class="px-4 py-2 bg-white text-gray-700 text-sm font-medium border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Cancel
        </button>
        
        <button
          type="submit"
          disabled={loading || !formData.name.trim()}
          class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Saving...' : (isEditing ? 'Update Unit' : 'Create Unit')}
        </button>
      </div>
    </form>
  </div>
</div>
