<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { enhance } from '$app/forms'

  export let unit: any
  export let childUnits: any[] = []
  export let allUnits: any[] = []
  export let canEdit: boolean = false

  const dispatch = createEventDispatcher()

  let showDeleteModal = false
  let expanded = false

  const formatUserName = (user: any) => {
    if (!user) return 'No manager assigned'
    if (user.profile?.first_name || user.profile?.last_name) {
      return `${user.profile.first_name || ''} ${user.profile.last_name || ''}`.trim()
    }
    return user.email.split('@')[0]
  }

  const getNestedChildCount = (unitId: string): number => {
    const directChildren = allUnits.filter(u => u.parent_id === unitId)
    return directChildren.reduce((count, child) => {
      return count + 1 + getNestedChildCount(child.id)
    }, 0)
  }

  $: nestedChildCount = getNestedChildCount(unit.id)
  $: userCount = unit._count?.count || 0
</script>

<div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
  <!-- Main Unit Card -->
  <div class="p-6">
    <div class="flex items-center justify-between">
      <div class="flex-1">
        <div class="flex items-center">
          <div class="flex-shrink-0 w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          
          <div class="ml-4 flex-1">
            <h3 class="text-lg font-medium text-gray-900">{unit.name}</h3>
            {#if unit.description}
              <p class="text-sm text-gray-500 mt-1">{unit.description}</p>
            {/if}
          </div>
        </div>

        <!-- Unit Details -->
        <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <dt class="text-xs font-medium text-gray-500 uppercase">Manager</dt>
            <dd class="mt-1 text-sm text-gray-900">{formatUserName(unit.manager)}</dd>
          </div>
          
          <div>
            <dt class="text-xs font-medium text-gray-500 uppercase">Users</dt>
            <dd class="mt-1 text-sm text-gray-900">{userCount} user{userCount !== 1 ? 's' : ''}</dd>
          </div>
          
          <div>
            <dt class="text-xs font-medium text-gray-500 uppercase">Sub-units</dt>
            <dd class="mt-1 text-sm text-gray-900">{nestedChildCount} unit{nestedChildCount !== 1 ? 's' : ''}</dd>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center space-x-2 ml-6">
        {#if childUnits.length > 0}
          <button
            type="button"
            on:click={() => expanded = !expanded}
            class="p-2 text-gray-400 hover:text-gray-600"
            title={expanded ? 'Collapse' : 'Expand'}
          >
            <svg class="w-5 h-5 transform {expanded ? 'rotate-180' : ''}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        {/if}

        {#if canEdit}
          <button
            type="button"
            on:click={() => dispatch('edit')}
            class="p-2 text-indigo-600 hover:text-indigo-800"
            title="Edit unit"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </button>

          <button
            type="button"
            on:click={() => showDeleteModal = true}
            class="p-2 text-red-600 hover:text-red-800"
            title="Delete unit"
            disabled={userCount > 0 || nestedChildCount > 0}
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        {/if}
      </div>
    </div>
  </div>

  <!-- Child Units (Expandable) -->
  {#if expanded && childUnits.length > 0}
    <div class="border-t border-gray-200 bg-gray-50 px-6 py-4">
      <h4 class="text-sm font-medium text-gray-700 mb-3">Sub-units</h4>
      <div class="space-y-3">
        {#each childUnits as childUnit}
          <svelte:self 
            unit={childUnit} 
            childUnits={allUnits.filter(u => u.parent_id === childUnit.id)}
            {allUnits}
            {canEdit}
            on:edit
          />
        {/each}
      </div>
    </div>
  {/if}
</div>

<!-- Delete Confirmation Modal -->
{#if showDeleteModal}
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3 text-center">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Delete Business Unit</h3>
        <p class="text-sm text-gray-500 mb-6">
          Are you sure you want to delete "{unit.name}"? This action cannot be undone.
        </p>
        
        {#if userCount > 0 || nestedChildCount > 0}
          <div class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p class="text-sm text-yellow-800">
              Cannot delete: This unit has {userCount} user{userCount !== 1 ? 's' : ''} 
              {#if nestedChildCount > 0}
                and {nestedChildCount} sub-unit{nestedChildCount !== 1 ? 's' : ''}
              {/if}.
            </p>
          </div>
        {/if}
        
        <div class="flex justify-center space-x-3">
          {#if userCount === 0 && nestedChildCount === 0}
            <form method="POST" action="?/deleteBusinessUnit" use:enhance>
              <input type="hidden" name="id" value={unit.id} />
              <button
                type="submit"
                class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700"
              >
                Delete
              </button>
            </form>
          {/if}
          
          <button
            type="button"
            on:click={() => showDeleteModal = false}
            class="px-4 py-2 bg-white text-gray-700 text-sm font-medium border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}
