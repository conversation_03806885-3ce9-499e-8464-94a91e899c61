<script lang="ts">
  import { enhance } from '$app/forms'
  import type { PageData } from './$types'
  import BusinessUnitCard from './BusinessUnitCard.svelte'
  import BusinessUnitForm from './BusinessUnitForm.svelte'

  export let data: PageData
  export let form: any

  $: ({ businessUnits, potentialManagers, canEdit } = data)

  let showCreateForm = false
  let editingUnit: any = null

  const handleCreateNew = () => {
    editingUnit = null
    showCreateForm = true
  }

  const handleEdit = (unit: any) => {
    editingUnit = unit
    showCreateForm = true
  }

  const handleFormClose = () => {
    showCreateForm = false
    editingUnit = null
  }

  // Organize units into hierarchy
  $: rootUnits = businessUnits.filter(unit => !unit.parent_id)
  $: getChildUnits = (parentId: string) => 
    businessUnits.filter(unit => unit.parent_id === parentId)

  // Statistics
  $: totalUnits = businessUnits.length
  $: unitsWithManagers = businessUnits.filter(unit => unit.manager_id).length
  $: totalUsers = businessUnits.reduce((sum, unit) => sum + (unit._count?.count || 0), 0)
</script>

<svelte:head>
  <title>Business Units | ProcureServe</title>
</svelte:head>

<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
  <!-- Header -->
  <div class="mb-8">
    <nav class="flex items-center text-sm text-gray-500 mb-4">
      <a href="/settings" class="hover:text-gray-700">Settings</a>
      <span class="mx-2">/</span>
      <span class="text-gray-900">Business Units</span>
    </nav>
    
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Business Units & Departments</h1>
        <p class="mt-1 text-sm text-gray-600">
          Organize your company structure and assign managers
        </p>
      </div>
      
      {#if canEdit}
        <button 
          type="button"
          on:click={handleCreateNew}
          class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700"
        >
          Create Business Unit
        </button>
      {/if}
    </div>
  </div>

  <!-- Statistics -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Total Units</p>
          <p class="text-2xl font-bold text-gray-900">{totalUnits}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">With Managers</p>
          <p class="text-2xl font-bold text-gray-900">{unitsWithManagers}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Total Users</p>
          <p class="text-2xl font-bold text-gray-900">{totalUsers}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Success/Error Messages -->
  {#if form?.success}
    <div class="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
      <p class="text-sm font-medium text-green-800">{form.message}</p>
    </div>
  {/if}

  {#if form?.message && !form?.success}
    <div class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
      <p class="text-sm font-medium text-red-800">{form.message}</p>
    </div>
  {/if}

  <!-- Business Units List -->
  <div class="space-y-6">
    {#if businessUnits.length === 0}
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No business units</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating your first business unit.</p>
        {#if canEdit}
          <div class="mt-6">
            <button 
              type="button"
              on:click={handleCreateNew}
              class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700"
            >
              Create Business Unit
            </button>
          </div>
        {/if}
      </div>
    {:else}
      <!-- Root Level Units -->
      {#each rootUnits as unit}
        <BusinessUnitCard 
          {unit} 
          childUnits={getChildUnits(unit.id)}
          allUnits={businessUnits}
          {canEdit} 
          on:edit={() => handleEdit(unit)}
        />
      {/each}
    {/if}
  </div>
</div>

<!-- Create/Edit Form Modal -->
{#if showCreateForm}
  <BusinessUnitForm 
    unit={editingUnit}
    {potentialManagers}
    parentOptions={businessUnits.filter(u => u.id !== editingUnit?.id)}
    on:close={handleFormClose}
  />
{/if}
