import type { PageServerLoad, Actions } from './$types'
import { error, redirect, fail } from '@sveltejs/kit'
import type { BusinessUnit, CompanyAuditLog } from '$shared-types'

export const load: PageServerLoad = async ({ locals: { supabase, user, session } }) => {
  if (!session || !user) {
    throw redirect(302, '/login')
  }

  // Only admin and manager users can manage business units
  if (!['admin', 'manager'].includes(user.role)) {
    throw error(403, 'Insufficient permissions')
  }

  // Load business units with manager information
  const { data: businessUnits, error: unitsError } = await supabase
    .from('business_units')
    .select(`
      *,
      manager:users!business_units_manager_id_fkey(id, email, profile)
    `)
    .eq('company_id', user.company_id)
    .order('name')

  if (unitsError) {
    console.error('Business units load error:', unitsError)
  }

  // Load potential managers (admin and manager role users)
  const { data: potentialManagers } = await supabase
    .from('users')
    .select('id, email, profile')
    .eq('company_id', user.company_id)
    .in('role', ['admin', 'manager'])
    .eq('is_active', true)
    .order('email')

  return {
    businessUnits: businessUnits || [],
    potentialManagers: potentialManagers || [],
    canEdit: user.role === 'admin' // Only admin can create/edit/delete
  }
}

export const actions: Actions = {
  createBusinessUnit: async ({ request, locals: { supabase, user, session } }) => {
    if (!session || !user) {
      return fail(401, { message: 'Unauthorized' })
    }

    if (user.role !== 'admin') {
      return fail(403, { message: 'Only admin users can create business units' })
    }

    const formData = await request.formData()
    const name = formData.get('name') as string
    const description = formData.get('description') as string
    const parentId = formData.get('parent_id') as string
    const managerId = formData.get('manager_id') as string

    if (!name?.trim()) {
      return fail(400, { message: 'Business unit name is required' })
    }

    const newUnit: Omit<BusinessUnit, 'id' | 'created_at' | 'updated_at'> = {
      company_id: user.company_id,
      name: name.trim(),
      description: description?.trim() || null,
      parent_id: parentId || null,
      manager_id: managerId || null,
      settings: {}
    }

    const { data, error: createError } = await supabase
      .from('business_units')
      .insert(newUnit)
      .select()
      .single()

    if (createError) {
      console.error('Business unit creation error:', createError)
      return fail(500, { message: 'Failed to create business unit' })
    }

    // Create audit log
    const auditLogData: Omit<CompanyAuditLog, 'id' | 'created_at'> = {
      company_id: user.company_id,
      performed_by: user.id,
      action_type: 'business_unit_created',
      target_user_id: null,
      old_values: null,
      new_values: newUnit,
      ip_address: request.headers.get('x-forwarded-for') || 'unknown',
      user_agent: request.headers.get('user-agent') || 'unknown'
    }

    await supabase.from('company_audit_logs').insert(auditLogData)

    return { success: true, message: 'Business unit created successfully' }
  },

  updateBusinessUnit: async ({ request, locals: { supabase, user, session } }) => {
    if (!session || !user) {
      return fail(401, { message: 'Unauthorized' })
    }

    if (user.role !== 'admin') {
      return fail(403, { message: 'Only admin users can update business units' })
    }

    const formData = await request.formData()
    const id = formData.get('id') as string
    const name = formData.get('name') as string
    const description = formData.get('description') as string
    const parentId = formData.get('parent_id') as string
    const managerId = formData.get('manager_id') as string

    if (!id || !name?.trim()) {
      return fail(400, { message: 'Business unit ID and name are required' })
    }

    // Get old values for audit log
    const { data: oldUnit } = await supabase
      .from('business_units')
      .select('*')
      .eq('id', id)
      .eq('company_id', user.company_id)
      .single()

    if (!oldUnit) {
      return fail(404, { message: 'Business unit not found' })
    }

    const updateData = {
      name: name.trim(),
      description: description?.trim() || null,
      parent_id: parentId || null,
      manager_id: managerId || null,
      updated_at: new Date().toISOString()
    }

    const { error: updateError } = await supabase
      .from('business_units')
      .update(updateData)
      .eq('id', id)
      .eq('company_id', user.company_id)

    if (updateError) {
      console.error('Business unit update error:', updateError)
      return fail(500, { message: 'Failed to update business unit' })
    }

    // Create audit log
    const auditLogData: Omit<CompanyAuditLog, 'id' | 'created_at'> = {
      company_id: user.company_id,
      performed_by: user.id,
      action_type: 'business_unit_updated',
      target_user_id: null,
      old_values: oldUnit,
      new_values: updateData
    }

    await supabase.from('company_audit_logs').insert(auditLogData)

    return { success: true, message: 'Business unit updated successfully' }
  },

  deleteBusinessUnit: async ({ request, locals: { supabase, user, session } }) => {
    if (!session || !user) {
      return fail(401, { message: 'Unauthorized' })
    }

    if (user.role !== 'admin') {
      return fail(403, { message: 'Only admin users can delete business units' })
    }

    const formData = await request.formData()
    const id = formData.get('id') as string

    if (!id) {
      return fail(400, { message: 'Business unit ID is required' })
    }

    // Check if unit has users assigned
    const { data: usersCount } = await supabase
      .from('users')
      .select('id', { count: 'exact', head: true })
      .eq('business_unit_id', id)
      .eq('company_id', user.company_id)

    if (usersCount && usersCount > 0) {
      return fail(400, { message: 'Cannot delete business unit with assigned users' })
    }

    // Check if unit has child units
    const { data: childUnits } = await supabase
      .from('business_units')
      .select('id', { count: 'exact', head: true })
      .eq('parent_id', id)
      .eq('company_id', user.company_id)

    if (childUnits && childUnits > 0) {
      return fail(400, { message: 'Cannot delete business unit with sub-units' })
    }

    // Get unit data for audit log
    const { data: unitData } = await supabase
      .from('business_units')
      .select('*')
      .eq('id', id)
      .eq('company_id', user.company_id)
      .single()

    const { error: deleteError } = await supabase
      .from('business_units')
      .delete()
      .eq('id', id)
      .eq('company_id', user.company_id)

    if (deleteError) {
      console.error('Business unit deletion error:', deleteError)
      return fail(500, { message: 'Failed to delete business unit' })
    }

    // Create audit log
    if (unitData) {
      const auditLogData: Omit<CompanyAuditLog, 'id' | 'created_at'> = {
        company_id: user.company_id,
        performed_by: user.id,
        action_type: 'business_unit_deleted',
        target_user_id: null,
        old_values: unitData,
        new_values: null
      }

      await supabase.from('company_audit_logs').insert(auditLogData)
    }

    return { success: true, message: 'Business unit deleted successfully' }
  }
}
