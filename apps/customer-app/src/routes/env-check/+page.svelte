<!-- Simple page to check environment variables -->
<script lang="ts">
  import { PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY } from '$env/static/public'
  
  const envStatus = {
    url: PUBLIC_SUPABASE_URL || 'NOT_SET',
    key: PUBLIC_SUPABASE_ANON_KEY ? 'SET' : 'NOT_SET',
    keyLength: PUBLIC_SUPABASE_ANON_KEY?.length || 0
  }
</script>

<svelte:head>
  <title>Environment Check - ProcureServe</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 py-12 px-4">
  <div class="max-w-2xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-900 mb-8">🔧 Environment Check</h1>
    
    <div class="bg-white rounded-lg shadow p-6">
      <h2 class="text-xl font-semibold mb-4">Environment Variables Status</h2>
      
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="font-medium">PUBLIC_SUPABASE_URL:</span>
          <span class="font-mono text-sm {envStatus.url !== 'NOT_SET' ? 'text-green-600' : 'text-red-600'}">
            {envStatus.url}
          </span>
        </div>
        
        <div class="flex justify-between">
          <span class="font-medium">PUBLIC_SUPABASE_ANON_KEY:</span>
          <span class="font-mono text-sm {envStatus.key === 'SET' ? 'text-green-600' : 'text-red-600'}">
            {envStatus.key} ({envStatus.keyLength} chars)
          </span>
        </div>
      </div>
      
      {#if envStatus.url !== 'NOT_SET' && envStatus.key === 'SET'}
        <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded">
          <p class="text-green-800 font-medium">✅ Environment variables are configured correctly!</p>
          <div class="mt-2 space-y-1">
            <a href="/test-connection" class="text-green-700 underline block">→ Test Supabase connection</a>
            <a href="/" class="text-green-700 underline block">→ Go to main app</a>
          </div>
        </div>
      {:else}
        <div class="mt-6 p-4 bg-red-50 border border-red-200 rounded">
          <p class="text-red-800 font-medium">❌ Environment variables need to be configured</p>
          <p class="text-red-700 text-sm mt-1">Check .env.local file in the customer-app directory</p>
        </div>
      {/if}
    </div>
  </div>
</div>
