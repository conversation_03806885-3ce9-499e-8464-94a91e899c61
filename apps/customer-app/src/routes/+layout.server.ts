export const load = async ({ locals, depends, url }) => {
  depends('supabase:auth')

  let userProfile = null
  
  // Define public pages that don't need user profile data
  const publicPages = [
    '/', 
    '/about', 
    '/contact', 
    '/test-users', 
    '/select-process', 
    '/debug-auth', 
    '/logout', 
    '/create-users', 
    '/env-check', 
    '/test-connection', 
    '/registration/submitted'
  ]
  
  const isPublicPage = publicPages.includes(url.pathname)
  
  // If user is authenticated and not on a public page, get their profile and process permissions
  if (locals.session?.user && !isPublicPage) {
    const { data } = await locals.supabase
      .from('users')
      .select('process_permissions, current_process, role, profile')
      .eq('id', locals.session.user.id)
      .single()
    
    userProfile = data
  }

  return {
    session: locals.session,
    user: locals.user,
    userProfile
  }
}
