<script lang="ts">
	import Card from '$lib/components/ui/card.svelte'
	import CardHeader from '$lib/components/ui/card-header.svelte'
	import CardTitle from '$lib/components/ui/card-title.svelte'
	import CardContent from '$lib/components/ui/card-content.svelte'
	import Button from '$lib/components/ui/button.svelte'
	import Badge from '$lib/components/ui/badge.svelte'
	import EmptyState from '$lib/components/ui/empty-state.svelte'
	import { UserPlus, Search, Filter, MoreVertical, Mail, Phone, MapPin } from 'lucide-svelte'
	import { goto } from '$app/navigation'
	
	export let data
	
	function getStatusColor(status: string): string {
		const colors = {
			'available': 'bg-green-100 text-green-800',
			'placed': 'bg-blue-100 text-blue-800',
			'unavailable': 'bg-gray-100 text-gray-800',
			'interviewing': 'bg-yellow-100 text-yellow-800'
		}
		return colors[status] || 'bg-gray-100 text-gray-800'
	}
	
	function formatRate(rate: number): string {
		return rate ? `$${rate}/hr` : 'Rate TBD'
	}
	
	function formatExperience(years: number): string {
		if (!years) return 'Experience TBD'
		return years === 1 ? '1 year' : `${years} years`
	}
	
	function navigateToConsultant(id: string) {
		goto(`/bench-sales/consultants/${id}`)
	}
</script>

<svelte:head>
	<title>Consultants - Bench Sales - ProcureServe</title>
</svelte:head>

<div class="space-y-6">
	<!-- Header -->
	<div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
		<div>
			<h1 class="text-2xl font-bold text-gray-900">Consultants</h1>
			<p class="text-gray-600">Manage your available talent pool</p>
		</div>
		<div class="flex gap-3">
			<Button variant="outline" class="flex items-center gap-2">
				<Filter class="w-4 h-4" />
				Filter
			</Button>
			<Button class="flex items-center gap-2" on:click={() => goto('/bench-sales/consultants/new')}>
				<UserPlus class="w-4 h-4" />
				Add Consultant
			</Button>
		</div>
	</div>
	
	<!-- Search -->
	<Card>
		<CardContent class="p-4">
			<div class="flex items-center gap-3">
				<Search class="w-5 h-5 text-gray-400" />
				<input
					type="text"
					placeholder="Search consultants by name, skills, or location..."
					class="flex-1 border-0 bg-transparent focus:outline-none text-gray-900 placeholder-gray-500"
				/>
			</div>
		</CardContent>
	</Card>
	
	<!-- Consultants List -->
	{#if data.consultants.length > 0}
		<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
			{#each data.consultants as consultant}
				<Card class="hover:shadow-lg transition-shadow cursor-pointer" on:click={() => navigateToConsultant(consultant.id)}>
					<CardContent class="p-6">
						<div class="flex justify-between items-start mb-4">
							<div class="flex-1">
								<h3 class="font-semibold text-lg text-gray-900">
									{consultant.first_name} {consultant.last_name}
								</h3>
								<Badge class={getStatusColor(consultant.availability_status)}>
									{consultant.availability_status}
								</Badge>
							</div>
							<button class="p-1 hover:bg-gray-100 rounded">
								<MoreVertical class="w-4 h-4 text-gray-400" />
							</button>
						</div>
						
						<div class="space-y-3">
							{#if consultant.email}
								<div class="flex items-center gap-2 text-sm text-gray-600">
									<Mail class="w-4 h-4" />
									<span class="truncate">{consultant.email}</span>
								</div>
							{/if}
							
							{#if consultant.phone}
								<div class="flex items-center gap-2 text-sm text-gray-600">
									<Phone class="w-4 h-4" />
									<span>{consultant.phone}</span>
								</div>
							{/if}
							
							{#if consultant.location}
								<div class="flex items-center gap-2 text-sm text-gray-600">
									<MapPin class="w-4 h-4" />
									<span class="truncate">{consultant.location}</span>
								</div>
							{/if}
						</div>
						
						<div class="flex justify-between items-center mt-4 pt-4 border-t border-gray-100">
							<div class="text-sm">
								<span class="font-medium text-gray-900">{formatRate(consultant.hourly_rate)}</span>
								<span class="text-gray-500"> • {formatExperience(consultant.experience_years)}</span>
							</div>
						</div>
						
						{#if consultant.skills && consultant.skills.length > 0}
							<div class="mt-3 flex flex-wrap gap-1">
								{#each consultant.skills.slice(0, 3) as skill}
									<span class="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
										{skill}
									</span>
								{/each}
								{#if consultant.skills.length > 3}
									<span class="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-500 rounded">
										+{consultant.skills.length - 3} more
									</span>
								{/if}
							</div>
						{/if}
					</CardContent>
				</Card>
			{/each}
		</div>
	{:else}
		<EmptyState 
			icon={UserPlus}
			title="No consultants found"
			description="Add your first consultant to start building your talent pool"
			actionText="Add Consultant"
			on:action={() => goto('/bench-sales/consultants/new')}
		/>
	{/if}
</div>
