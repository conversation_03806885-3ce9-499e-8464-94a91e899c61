<script lang="ts">
	import Card from '$lib/components/ui/card.svelte'
	import CardHeader from '$lib/components/ui/card-header.svelte'
	import CardTitle from '$lib/components/ui/card-title.svelte'
	import CardContent from '$lib/components/ui/card-content.svelte'
	import Button from '$lib/components/ui/button.svelte'
	import { ArrowLeft, Save, UserPlus } from 'lucide-svelte'
	import { goto } from '$app/navigation'
	
	export let data
	
	let formData = {
		first_name: '',
		last_name: '',
		email: '',
		phone: '',
		location: '',
		availability_status: 'available',
		hourly_rate: null,
		experience_years: null,
		skills: '',
		notes: ''
	}
	
	let loading = false
	let errors = {}
	
	async function handleSubmit() {
		loading = true
		errors = {}
		
		try {
			// Convert skills string to array
			const skillsArray = formData.skills
				.split(',')
				.map(skill => skill.trim())
				.filter(skill => skill.length > 0)
			
			const consultantData = {
				...formData,
				skills: skillsArray,
				company_id: data.user.company_id,
				hourly_rate: formData.hourly_rate ? parseFloat(formData.hourly_rate) : null,
				experience_years: formData.experience_years ? parseInt(formData.experience_years) : null
			}
			
			const response = await fetch('/api/consultants', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(consultantData)
			})
			
			if (response.ok) {
				goto('/bench-sales/consultants')
			} else {
				const result = await response.json()
				errors = result.errors || { general: 'Failed to create consultant' }
			}
		} catch (error) {
			errors = { general: 'An unexpected error occurred' }
		} finally {
			loading = false
		}
	}
	
	function goBack() {
		goto('/bench-sales/consultants')
	}
</script>

<svelte:head>
	<title>Add Consultant - Bench Sales - ProcureServe</title>
</svelte:head>

<div class="max-w-2xl mx-auto space-y-6">
	<!-- Header -->
	<div class="flex items-center gap-4">
		<Button variant="outline" size="sm" on:click={goBack}>
			<ArrowLeft class="w-4 h-4" />
		</Button>
		<div>
			<h1 class="text-2xl font-bold text-gray-900">Add New Consultant</h1>
			<p class="text-gray-600">Add a consultant to your talent pool</p>
		</div>
	</div>
	
	<!-- Form -->
	<form on:submit|preventDefault={handleSubmit}>
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<UserPlus class="w-5 h-5" />
					Consultant Information
				</CardTitle>
			</CardHeader>
			<CardContent class="space-y-6">
				<!-- Basic Information -->
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">
							First Name *
						</label>
						<input
							id="first_name"
							type="text"
							bind:value={formData.first_name}
							required
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
							placeholder="John"
						/>
						{#if errors.first_name}
							<p class="mt-1 text-sm text-red-600">{errors.first_name}</p>
						{/if}
					</div>
					
					<div>
						<label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">
							Last Name *
						</label>
						<input
							id="last_name"
							type="text"
							bind:value={formData.last_name}
							required
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
							placeholder="Doe"
						/>
						{#if errors.last_name}
							<p class="mt-1 text-sm text-red-600">{errors.last_name}</p>
						{/if}
					</div>
				</div>
				
				<!-- Contact Information -->
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<label for="email" class="block text-sm font-medium text-gray-700 mb-1">
							Email *
						</label>
						<input
							id="email"
							type="email"
							bind:value={formData.email}
							required
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
							placeholder="<EMAIL>"
						/>
						{#if errors.email}
							<p class="mt-1 text-sm text-red-600">{errors.email}</p>
						{/if}
					</div>
					
					<div>
						<label for="phone" class="block text-sm font-medium text-gray-700 mb-1">
							Phone
						</label>
						<input
							id="phone"
							type="tel"
							bind:value={formData.phone}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
							placeholder="+****************"
						/>
					</div>
				</div>
				
				<!-- Professional Information -->
				<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div>
						<label for="availability_status" class="block text-sm font-medium text-gray-700 mb-1">
							Availability Status *
						</label>
						<select
							id="availability_status"
							bind:value={formData.availability_status}
							required
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
						>
							<option value="available">Available</option>
							<option value="placed">Placed</option>
							<option value="unavailable">Unavailable</option>
							<option value="interviewing">Interviewing</option>
						</select>
					</div>
					
					<div>
						<label for="hourly_rate" class="block text-sm font-medium text-gray-700 mb-1">
							Hourly Rate ($)
						</label>
						<input
							id="hourly_rate"
							type="number"
							step="0.01"
							min="0"
							bind:value={formData.hourly_rate}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
							placeholder="85.00"
						/>
					</div>
					
					<div>
						<label for="experience_years" class="block text-sm font-medium text-gray-700 mb-1">
							Years of Experience
						</label>
						<input
							id="experience_years"
							type="number"
							min="0"
							max="50"
							bind:value={formData.experience_years}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
							placeholder="5"
						/>
					</div>
				</div>
				
				<!-- Location -->
				<div>
					<label for="location" class="block text-sm font-medium text-gray-700 mb-1">
						Location
					</label>
					<input
						id="location"
						type="text"
						bind:value={formData.location}
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
						placeholder="New York, NY"
					/>
				</div>
				
				<!-- Skills -->
				<div>
					<label for="skills" class="block text-sm font-medium text-gray-700 mb-1">
						Skills
					</label>
					<input
						id="skills"
						type="text"
						bind:value={formData.skills}
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
						placeholder="Java, Spring Boot, React, AWS (comma-separated)"
					/>
					<p class="mt-1 text-sm text-gray-500">Enter skills separated by commas</p>
				</div>
				
				<!-- Notes -->
				<div>
					<label for="notes" class="block text-sm font-medium text-gray-700 mb-1">
						Notes
					</label>
					<textarea
						id="notes"
						bind:value={formData.notes}
						rows="3"
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
						placeholder="Additional notes about the consultant..."
					></textarea>
				</div>
				
				{#if errors.general}
					<div class="p-4 bg-red-50 border border-red-200 rounded-md">
						<p class="text-sm text-red-600">{errors.general}</p>
					</div>
				{/if}
				
				<!-- Actions -->
				<div class="flex justify-end gap-3 pt-6 border-t border-gray-200">
					<Button variant="outline" type="button" on:click={goBack} disabled={loading}>
						Cancel
					</Button>
					<Button type="submit" disabled={loading} class="flex items-center gap-2">
						{#if loading}
							<div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
						{:else}
							<Save class="w-4 h-4" />
						{/if}
						Save Consultant
					</Button>
				</div>
			</CardContent>
		</Card>
	</form>
</div>
