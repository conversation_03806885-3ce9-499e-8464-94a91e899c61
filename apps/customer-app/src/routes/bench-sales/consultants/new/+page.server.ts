import { redirect } from '@sveltejs/kit'
import type { PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ locals }) => {
  const { data: { session } } = await locals.supabase.auth.getSession()
  
  if (!session) {
    throw redirect(303, '/login')
  }
  
  // Get user and verify process access
  const { data: user, error: userError } = await locals.supabase
    .from('users')
    .select(`
      *,
      companies (
        id,
        name,
        bench_sales_enabled,
        settings
      )
    `)
    .eq('id', session.user.id)
    .single()
  
  if (userError || !user) {
    throw redirect(303, '/access-denied')
  }
  
  // Check if user has bench sales access
  const hasBenchSalesAccess = user.process_permissions?.includes('bench_sales')
  if (!hasBenchSalesAccess) {
    throw redirect(303, '/access-denied')
  }
  
  return {
    user: {
      ...user,
      current_process: 'bench_sales'
    },
    company: user.companies
  }
}
