<script lang="ts">
	import Card from '$lib/components/ui/card.svelte'
	import CardHeader from '$lib/components/ui/card-header.svelte'
	import CardTitle from '$lib/components/ui/card-title.svelte'
	import CardContent from '$lib/components/ui/card-content.svelte'
	import Button from '$lib/components/ui/button.svelte'
	import Badge from '$lib/components/ui/badge.svelte'
	import EmptyState from '$lib/components/ui/empty-state.svelte'
	import { Plus, Search, Filter, MoreVertical, Building2, Calendar, DollarSign, MapPin } from 'lucide-svelte'
	import { goto } from '$app/navigation'
	
	export let data
	
	function getStatusColor(status: string): string {
		const colors = {
			'active': 'bg-green-100 text-green-800',
			'draft': 'bg-gray-100 text-gray-800',
			'completed': 'bg-blue-100 text-blue-800',
			'cancelled': 'bg-red-100 text-red-800',
			'on_hold': 'bg-yellow-100 text-yellow-800'
		}
		return colors[status] || 'bg-gray-100 text-gray-800'
	}
	
	function formatBudget(budget: number): string {
		if (!budget) return 'Budget TBD'
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: 'USD',
			minimumFractionDigits: 0,
			maximumFractionDigits: 0,
		}).format(budget)
	}
	
	function formatDuration(duration: string): string {
		if (!duration) return 'Duration TBD'
		return duration
	}
	
	function formatDate(dateString: string): string {
		if (!dateString) return 'Date TBD'
		return new Date(dateString).toLocaleDateString()
	}
	
	function navigateToProject(id: string) {
		goto(`/bench-sales/projects/${id}`)
	}
</script>

<svelte:head>
	<title>Projects - Bench Sales - ProcureServe</title>
</svelte:head>

<div class="space-y-6">
	<!-- Header -->
	<div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
		<div>
			<h1 class="text-2xl font-bold text-gray-900">Client Projects</h1>
			<p class="text-gray-600">Manage client requirements and project opportunities</p>
		</div>
		<div class="flex gap-3">
			<Button variant="outline" class="flex items-center gap-2">
				<Filter class="w-4 h-4" />
				Filter
			</Button>
			<Button class="flex items-center gap-2" on:click={() => goto('/bench-sales/projects/new')}>
				<Plus class="w-4 h-4" />
				Add Project
			</Button>
		</div>
	</div>
	
	<!-- Search -->
	<Card>
		<CardContent class="p-4">
			<div class="flex items-center gap-3">
				<Search class="w-5 h-5 text-gray-400" />
				<input
					type="text"
					placeholder="Search projects by title, client, or skills..."
					class="flex-1 border-0 bg-transparent focus:outline-none text-gray-900 placeholder-gray-500"
				/>
			</div>
		</CardContent>
	</Card>
	
	<!-- Projects List -->
	{#if data.projects.length > 0}
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
			{#each data.projects as project}
				<Card class="hover:shadow-lg transition-shadow cursor-pointer" on:click={() => navigateToProject(project.id)}>
					<CardContent class="p-6">
						<div class="flex justify-between items-start mb-4">
							<div class="flex-1">
								<h3 class="font-semibold text-lg text-gray-900 mb-1">
									{project.title}
								</h3>
								{#if project.clients}
									<p class="text-sm text-gray-600 flex items-center gap-1">
										<Building2 class="w-3 h-3" />
										{project.clients.name}
									</p>
								{/if}
							</div>
							<div class="flex items-center gap-2">
								<Badge class={getStatusColor(project.status)}>
									{project.status.replace('_', ' ')}
								</Badge>
								<button class="p-1 hover:bg-gray-100 rounded">
									<MoreVertical class="w-4 h-4 text-gray-400" />
								</button>
							</div>
						</div>
						
						{#if project.description}
							<p class="text-sm text-gray-600 mb-4 line-clamp-2">
								{project.description}
							</p>
						{/if}
						
						<div class="space-y-2 mb-4">
							{#if project.budget}
								<div class="flex items-center gap-2 text-sm text-gray-600">
									<DollarSign class="w-4 h-4" />
									<span>{formatBudget(project.budget)} • {formatDuration(project.duration)}</span>
								</div>
							{/if}
							
							{#if project.start_date}
								<div class="flex items-center gap-2 text-sm text-gray-600">
									<Calendar class="w-4 h-4" />
									<span>{formatDate(project.start_date)}</span>
									{#if project.end_date}
										<span>- {formatDate(project.end_date)}</span>
									{/if}
								</div>
							{/if}
							
							{#if project.location}
								<div class="flex items-center gap-2 text-sm text-gray-600">
									<MapPin class="w-4 h-4" />
									<span class="truncate">{project.location}</span>
								</div>
							{/if}
						</div>
						
						{#if project.skills_required && project.skills_required.length > 0}
							<div class="flex flex-wrap gap-1">
								{#each project.skills_required.slice(0, 4) as skill}
									<span class="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded">
										{skill}
									</span>
								{/each}
								{#if project.skills_required.length > 4}
									<span class="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-500 rounded">
										+{project.skills_required.length - 4} more
									</span>
								{/if}
							</div>
						{/if}
					</CardContent>
				</Card>
			{/each}
		</div>
	{:else}
		<EmptyState 
			icon={Plus}
			title="No projects found"
			description="Add your first client project to start managing requirements"
			actionText="Add Project"
			on:action={() => goto('/bench-sales/projects/new')}
		/>
	{/if}
</div>
