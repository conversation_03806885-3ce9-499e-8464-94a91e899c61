<script lang="ts">
	import EmptyState from '$lib/components/ui/empty-state.svelte'
	import { Building2 } from 'lucide-svelte'
	
	export let data
</script>

<svelte:head>
	<title>Clients - Bench Sales - ProcureServe</title>
</svelte:head>

<div class="space-y-6">
	<div>
		<h1 class="text-2xl font-bold text-gray-900">Clients</h1>
		<p class="text-gray-600">Manage client relationships and contacts</p>
	</div>
	
	<EmptyState 
		icon={Building2}
		title="Clients Module Coming Soon"
		description="Client management features will be implemented in the next phase"
	/>
</div>
