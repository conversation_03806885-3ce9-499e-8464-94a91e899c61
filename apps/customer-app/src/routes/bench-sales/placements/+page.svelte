<script lang="ts">
	import EmptyState from '$lib/components/ui/empty-state.svelte'
	import { FileText } from 'lucide-svelte'
	
	export let data
</script>

<svelte:head>
	<title>Placements - Bench Sales - ProcureServe</title>
</svelte:head>

<div class="space-y-6">
	<div>
		<h1 class="text-2xl font-bold text-gray-900">Placements</h1>
		<p class="text-gray-600">Track consultant placements and submissions</p>
	</div>
	
	<EmptyState 
		icon={FileText}
		title="Placements Module Coming Soon"
		description="Placement tracking features will be implemented in the next phase"
	/>
</div>
