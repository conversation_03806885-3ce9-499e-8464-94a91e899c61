<script lang="ts">
	import Card from '$lib/components/ui/card.svelte'
	import CardHeader from '$lib/components/ui/card-header.svelte'
	import CardTitle from '$lib/components/ui/card-title.svelte'
	import CardContent from '$lib/components/ui/card-content.svelte'
	import Button from '$lib/components/ui/button.svelte'
	import EmptyState from '$lib/components/ui/empty-state.svelte'
	import { Search, Target, Users, Briefcase, ArrowRight } from 'lucide-svelte'
	import { goto } from '$app/navigation'
	
	export let data
</script>

<svelte:head>
	<title>AI Matching - Bench Sales - ProcureServe</title>
</svelte:head>

<div class="space-y-6">
	<!-- Header -->
	<div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
		<div>
			<h1 class="text-2xl font-bold text-gray-900">AI Consultant Matching</h1>
			<p class="text-gray-600">Find the best consultant matches for your client projects</p>
		</div>
	</div>
	
	<!-- Coming Soon Card -->
	<div class="max-w-2xl mx-auto">
		<EmptyState 
			icon={Target}
			title="AI Matching Coming Soon"
			description="Advanced AI-powered matching between consultants and projects will be available in the next release. For now, browse consultants and projects manually."
		>
			<div class="flex flex-col sm:flex-row gap-3 mt-6">
				<Button on:click={() => goto('/bench-sales/consultants')} class="flex items-center gap-2">
					<Users class="w-4 h-4" />
					Browse Consultants
					<ArrowRight class="w-4 h-4" />
				</Button>
				<Button variant="outline" on:click={() => goto('/bench-sales/projects')} class="flex items-center gap-2">
					<Briefcase class="w-4 h-4" />
					Browse Projects
					<ArrowRight class="w-4 h-4" />
				</Button>
			</div>
		</EmptyState>
	</div>
	
	<!-- Feature Preview -->
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
		<Card>
			<CardContent class="p-6 text-center">
				<div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
					<Search class="w-6 h-6 text-blue-600" />
				</div>
				<h3 class="font-semibold text-gray-900 mb-2">Skill Matching</h3>
				<p class="text-sm text-gray-600">Automatically match consultant skills with project requirements</p>
			</CardContent>
		</Card>
		
		<Card>
			<CardContent class="p-6 text-center">
				<div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
					<Target class="w-6 h-6 text-green-600" />
				</div>
				<h3 class="font-semibold text-gray-900 mb-2">Availability Sync</h3>
				<p class="text-sm text-gray-600">Real-time availability checking and project timeline matching</p>
			</CardContent>
		</Card>
		
		<Card>
			<CardContent class="p-6 text-center">
				<div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
					<Users class="w-6 h-6 text-purple-600" />
				</div>
				<h3 class="font-semibold text-gray-900 mb-2">Smart Scoring</h3>
				<p class="text-sm text-gray-600">AI-powered compatibility scoring based on multiple factors</p>
			</CardContent>
		</Card>
	</div>
</div>
