<!-- Test connection page -->
<script lang="ts">
  export let data
</script>

<svelte:head>
  <title>Connection Test - ProcureServe</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 py-12 px-4">
  <div class="max-w-2xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-900 mb-8">🔧 Connection Test</h1>
    
    <div class="bg-white rounded-lg shadow p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4">Status: 
        <span class="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium {data.status === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
          {data.status === 'success' ? '✅ Connected' : '❌ Error'}
        </span>
      </h2>
      
      {#if data.status === 'success'}
        <div class="space-y-4">
          <div>
            <h3 class="font-medium text-gray-900">Database Connection</h3>
            <p class="text-sm text-gray-600">Companies found: {data.database.companies}</p>
            <p class="text-sm text-gray-600">Notification types table: {data.database.notification_types_table}</p>
            <p class="text-sm text-gray-600">Notifications table: {data.database.notifications_table}</p>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-900">Configuration</h3>
            <p class="text-sm text-gray-600">Supabase URL: {data.supabase_url}</p>
            <p class="text-sm text-gray-600">Tables tested: {data.tables_tested.join(', ')}</p>
          </div>
        </div>
      {:else}
        <div class="bg-red-50 border border-red-200 rounded p-4">
          <h3 class="font-medium text-red-800">Error Details</h3>
          <p class="text-sm text-red-700 mt-1">{data.message}</p>
          {#if data.error}
            <pre class="text-xs text-red-600 mt-2 bg-red-100 p-2 rounded overflow-x-auto">{data.error}</pre>
          {/if}
        </div>
      {/if}
    </div>
    
    <div class="bg-blue-50 border border-blue-200 rounded p-4">
      <h3 class="font-medium text-blue-800">Next Steps</h3>
      <ul class="text-sm text-blue-700 mt-1 space-y-1">
        <li>• If status is ✅, try accessing <a href="/" class="underline">main app</a></li>
        <li>• If status is ❌, check Supabase is running and environment variables</li>
        <li>• Test notification system at <a href="/notifications" class="underline">/notifications</a></li>
      </ul>
    </div>
  </div>
</div>
