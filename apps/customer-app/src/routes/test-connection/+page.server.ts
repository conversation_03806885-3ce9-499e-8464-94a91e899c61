// Test page to validate Supabase connection and notifications
import type { PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ locals: { supabase } }) => {
  try {
    // Test basic Supabase connection
    const { data: companies, error: companiesError } = await supabase
      .from('companies')
      .select('id, name')
      .limit(1)

    if (companiesError) {
      return {
        status: 'error',
        message: 'Database connection failed',
        error: companiesError.message
      }
    }

    // Test notification tables
    const { data: notificationTypes, error: notificationError } = await supabase
      .from('notification_types')
      .select('count')
      .limit(1)

    const { data: notifications, error: notificationsTableError } = await supabase
      .from('notifications')
      .select('count')
      .limit(1)

    return {
      status: 'success',
      database: {
        companies: companies?.length || 0,
        notification_types_table: notificationError ? 'Error' : 'OK',
        notifications_table: notificationsTableError ? 'Error' : 'OK'
      },
      supabase_url: process.env.VITE_SUPABASE_URL,
      tables_tested: ['companies', 'notification_types', 'notifications']
    }
  } catch (err) {
    return {
      status: 'error',
      message: 'Unexpected error',
      error: err instanceof Error ? err.message : 'Unknown error'
    }
  }
}
