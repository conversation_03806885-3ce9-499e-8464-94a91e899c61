<script>
	import Card from '$lib/components/ui/card.svelte'
	import CardHeader from '$lib/components/ui/card-header.svelte'
	import CardTitle from '$lib/components/ui/card-title.svelte'
	import CardDescription from '$lib/components/ui/card-description.svelte'
	import CardContent from '$lib/components/ui/card-content.svelte'
	import Button from '$lib/components/ui/button.svelte'
	import { Building2, ArrowRight, CheckCircle } from 'lucide-svelte'
	import { goto } from '$app/navigation'

	function navigateToBusinessRegister() {
		goto('/register/business')
	}

	function navigateToLogin() {
		goto('/login')
	}
</script>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4">
	<div class="max-w-2xl w-full">
		<div class="text-center mb-12">
			<h1 class="text-4xl font-bold text-gray-900 mb-4">Join ProcureServe</h1>
			<p class="text-xl text-gray-600">
				Register your business to get started with our staffing platform
			</p>
		</div>

		<!-- Business Registration Card -->
		<Card class="relative overflow-hidden">
			<CardHeader class="text-center pb-6">
				<div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
					<Building2 class="w-8 h-8 text-primary" />
				</div>
				<CardTitle class="text-2xl">Business Registration</CardTitle>
				<CardDescription class="text-base">
					For staffing agencies and direct employers
				</CardDescription>
			</CardHeader>
			<CardContent class="text-center">
				<div class="space-y-4 mb-6">
					<div class="flex items-center text-sm text-gray-600">
						<CheckCircle class="w-4 h-4 text-green-600 mr-2" />
						Post and manage job openings
					</div>
					<div class="flex items-center text-sm text-gray-600">
						<CheckCircle class="w-4 h-4 text-green-600 mr-2" />
						Search and manage candidates
					</div>
					<div class="flex items-center text-sm text-gray-600">
						<CheckCircle class="w-4 h-4 text-green-600 mr-2" />
						Track applications and submissions
					</div>
					<div class="flex items-center text-sm text-gray-600">
						<CheckCircle class="w-4 h-4 text-green-600 mr-2" />
						AI-powered matching and insights
					</div>
					<div class="flex items-center text-sm text-gray-600">
						<CheckCircle class="w-4 h-4 text-green-600 mr-2" />
						Enterprise security and compliance
					</div>
				</div>
				
				<Button on:click={navigateToBusinessRegister} class="w-full" size="lg">
					Register Your Business
					<ArrowRight class="w-4 h-4 ml-2" />
				</Button>
				
				<p class="text-xs text-gray-500 mt-3">
					Includes document verification and approval process
				</p>
			</CardContent>
		</Card>

		<!-- Login Option -->
		<div class="text-center mt-8">
			<p class="text-gray-600 mb-4">Already have a business account?</p>
			<Button variant="ghost" on:click={navigateToLogin} size="lg">
				Sign In to Your Account
			</Button>
		</div>

		<!-- Additional Information -->
		<div class="mt-12 text-center">
			<div class="bg-gray-100 rounded-lg p-6">
				<h3 class="font-semibold text-gray-900 mb-2">Why Choose ProcureServe?</h3>
				<p class="text-sm text-gray-600">
					Our platform streamlines the entire staffing process with enterprise-grade security, 
					AI-powered matching, and comprehensive compliance tools. Join thousands of businesses 
					already using ProcureServe to accelerate their staffing success.
				</p>
			</div>
		</div>

		<!-- Candidate Portal Reference -->
		<div class="mt-8 text-center">
			<div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
				<h4 class="font-medium text-blue-900 mb-2">Looking for job opportunities?</h4>
				<p class="text-sm text-blue-700">
					Job seekers can access our dedicated candidate portal at 
					<a href="http://localhost:3007" class="underline font-medium">candidates.procureserve.com</a>
				</p>
			</div>
		</div>
	</div>
</div>
