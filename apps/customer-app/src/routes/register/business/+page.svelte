<script lang="ts">
	import Card from '$lib/components/ui/card.svelte'
	import CardContent from '$lib/components/ui/card-content.svelte'
	import Button from '$lib/components/ui/button.svelte'
	import { ArrowLeft } from 'lucide-svelte'
	import { goto } from '$app/navigation'
	
	// Step Components
	import CompanyInfoStep from '$lib/components/registration/CompanyInfoStep.svelte'
	import AddressStep from '$lib/components/registration/AddressStep.svelte'
	import ContactStep from '$lib/components/registration/ContactStep.svelte'
	import SettingsStep from '$lib/components/registration/SettingsStep.svelte'
	import FormDebug from '$lib/components/registration/FormDebug.svelte'
	import type { ActionData } from './$types'
	
	let { form }: { form: ActionData } = $props()

	let loading = $state(false)
	let currentStep = $state(1)
	const totalSteps = 4
	const DEBUG_MODE = true

	// Form data, safely initialized from the `form` prop
	let formData = $state({
		company_name: form?.formData?.company_name || '',
		legal_entity_type: form?.formData?.legal_entity_type || '',
		tax_id: form?.formData?.tax_id || '',
		business_type: form?.formData?.business_type || 'staffing_agency',
		estimated_annual_volume: form?.formData?.estimated_annual_volume || '',
		street_address: form?.formData?.street_address || '',
		city: form?.formData?.city || '',
		state: form?.formData?.state || '',
		zip_code: form?.formData?.zip_code || '',
		first_name: form?.formData?.first_name || '',
		last_name: form?.formData?.last_name || '',
		email: form?.formData?.email || '',
		phone: form?.formData?.phone || '',
		title: form?.formData?.title || '',
		recruitment_enabled: form?.formData?.recruitment_enabled !== 'false',
		bench_sales_enabled: form?.formData?.bench_sales_enabled !== 'false',
		time_zone: form?.formData?.time_zone || 'America/New_York',
		work_start: form?.formData?.work_start || '09:00',
		work_end: form?.formData?.work_end || '17:00'
	})

	// Validation
	let step1Valid = $derived(formData.company_name && formData.legal_entity_type &&
		formData.tax_id && formData.business_type)
	let step2Valid = $derived(formData.street_address && formData.city &&
		formData.state && formData.zip_code)
	let step3Valid = $derived(formData.first_name && formData.last_name &&
		formData.email && formData.phone && formData.title)
	let step4Valid = $derived(formData.recruitment_enabled || formData.bench_sales_enabled)

	let canProceed: { [key: number]: boolean } = $derived({ 
		1: !!step1Valid, 
		2: !!step2Valid, 
		3: !!step3Valid, 
		4: step4Valid 
	})
	
	function nextStep() {
		if (currentStep < totalSteps && canProceed[currentStep]) currentStep++
	}
	
	function prevStep() {
		if (currentStep > 1) currentStep--
	}
</script>

<div class="min-h-screen bg-gray-50 py-12 px-4">
	<div class="max-w-4xl mx-auto">
		<!-- Header -->
		<div class="mb-8">
			<Button variant="ghost" onclick={() => goto('/')} class="mb-4">
				<ArrowLeft class="w-4 h-4 mr-2" />
				Back to Home
			</Button>
			
			<!-- Progress -->
			<div class="mb-6">
				<div class="flex items-center justify-center mb-4">
					<div class="flex items-center space-x-4">
						{#each Array(totalSteps) as _, i}
							<div class="flex items-center">
								<div class="w-8 h-8 rounded-full {currentStep > i ? 'bg-primary text-white' : currentStep === i + 1 ? 'bg-primary text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center text-sm font-medium">
									{i + 1}
								</div>
								{#if i < totalSteps - 1}
									<div class="w-12 h-px bg-gray-300 ml-4"></div>
								{/if}
							</div>
						{/each}
					</div>
				</div>
				<div class="text-center">
					<h1 class="text-3xl font-bold text-gray-900">Business Registration</h1>
					<p class="mt-2 text-gray-600">Join ProcureServe as a business partner</p>
				</div>
			</div>
		</div>

		{#if form?.error}
			<div class="mb-6 p-4 rounded-lg bg-red-50 border border-red-200">
				<p class="text-red-800">{form.error}</p>
			</div>
		{/if}

		<form method="POST" action="?/register">
			<!-- Visible Fields for the current step -->
			<Card>
				<CardContent class="p-8">
					{#if currentStep === 1}
						<CompanyInfoStep 
							bind:formData 
							canProceed={canProceed[1]} 
							onNext={nextStep} 
						/>
					{:else if currentStep === 2}
						<AddressStep 
							bind:formData 
							canProceed={canProceed[2]} 
							onNext={nextStep} 
							onPrev={prevStep} 
						/>
					{:else if currentStep === 3}
						<ContactStep 
							{formData}
							canProceed={canProceed[3]} 
							onNext={nextStep} 
							onPrev={prevStep} 
						/>
					{:else if currentStep === 4}
						<SettingsStep 
							bind:formData 
							canProceed={canProceed[4]} 
							onPrev={prevStep}
							{loading}
						/>
					{/if}
				</CardContent>
			</Card>
			
			<!-- Hidden fields to ensure all data is submitted regardless of current step -->
			<div class="hidden">
				<input type="text" name="company_name" value={formData.company_name} />
				<input type="text" name="legal_entity_type" value={formData.legal_entity_type} />
				<input type="text" name="tax_id" value={formData.tax_id} />
				<input type="text" name="business_type" value={formData.business_type} />
				<input type="text" name="estimated_annual_volume" value={formData.estimated_annual_volume} />
				<input type="text" name="street_address" value={formData.street_address} />
				<input type="text" name="city" value={formData.city} />
				<input type="text" name="state" value={formData.state} />
				<input type="text" name="zip_code" value={formData.zip_code} />
				<input type="text" name="first_name" value={formData.first_name} />
				<input type="text" name="last_name" value={formData.last_name} />
				<input type="email" name="email" value={formData.email} />
				<input type="tel" name="phone" value={formData.phone} />
				<input type="text" name="title" value={formData.title} />
				<input type="text" name="recruitment_enabled" value={formData.recruitment_enabled.toString()} />
				<input type="text" name="bench_sales_enabled" value={formData.bench_sales_enabled.toString()} />
				<input type="text" name="time_zone" value={formData.time_zone} />
				<input type="text" name="work_start" value={formData.work_start} />
				<input type="text" name="work_end" value={formData.work_end} />
			</div>
			
			{#if DEBUG_MODE}
				<FormDebug {formData} />
			{/if}
		</form>

		<div class="mt-8 text-center">
			<p class="text-sm text-gray-600">
				Need help with registration? 
				<a href="mailto:<EMAIL>" class="text-primary hover:underline">Contact our support team</a>
			</p>
		</div>
	</div>
</div>
