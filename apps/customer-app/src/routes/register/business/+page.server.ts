import { redirect, fail } from '@sveltejs/kit'
import type { Actions, PageServerLoad } from './$types'
import { createSupabaseServiceClient } from '$lib/supabase-service'
import { businessRegistrationEmails } from '$lib/business-registration-emails'
import { validateBusinessEmail } from '$lib/business-email-validator'
import { z, ZodError } from 'zod'

// Import the shared email workflow (temporarily disabled)
// import { sendAdminNotifications } from '@psii/business-email-workflow'
import { PUBLIC_SUPABASE_URL } from '$env/static/public'
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private'

// Import domain detection service
import { DomainDetectionService } from '@psii/domain-detection-service'
// Import audit service
import { AuditLogger } from '@psii/audit-service'

// Simplified Zod schema for the new application process.
// We are no longer capturing password at this stage.
const applicationSchema = z.object({
  company_name: z.string().min(1, 'Company name is required'),
  legal_entity_type: z.string().min(1, 'Legal entity type is required'),
  tax_id: z.string().regex(/^\d{2}-\d{7}$/, 'Invalid Tax ID format (XX-XXXXXXX)'),
  business_type: z.string().min(1, 'Business type is required'),
  estimated_annual_volume: z.string().min(1, 'Estimated annual volume is required'),
  recruitment_enabled: z.preprocess((val) => val === 'true', z.boolean()),
  bench_sales_enabled: z.preprocess((val) => val === 'true', z.boolean()),
  time_zone: z.string().min(1, 'Timezone is required'),
  street_address: z.string().min(1, 'Street address is required'),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  zip_code: z.string().min(1, 'Zip code is required'),
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  phone: z.string().min(1, 'Phone number is required'),
  title: z.string().min(1, 'Title is required'),
  work_start: z.string(),
  work_end: z.string(),
})

export const load: PageServerLoad = async () => {
  // Ensure the page always has a consistent data structure
  return {
    formData: null
  }
}

export const actions: Actions = {
  register: async ({ request }) => {
    console.log('[BUSINESS_REGISTRATION] Starting business registration process')
    
    const formData = await request.formData()
    const rawData = Object.fromEntries(formData)
    
    console.log('[BUSINESS_REGISTRATION] Form data received:', {
      company_name: rawData.company_name,
      email: rawData.email
    })

    try {
      // Test service client creation
      const serviceClient = createSupabaseServiceClient()
      console.log('[BUSINESS_REGISTRATION] Service client created successfully')
    } catch (serviceError) {
      console.error('[BUSINESS_REGISTRATION] Service client creation failed:', serviceError)
      return fail(500, {
        error: 'Database connection failed. Please try again.',
        formData: rawData
      })
    }

    const serviceClient = createSupabaseServiceClient()

    try {
      // 1. Validate form data
      console.log('[BUSINESS_REGISTRATION] Validating form data...')
      const validatedData = applicationSchema.parse(rawData)
      console.log('[BUSINESS_REGISTRATION] Form validation successful')
      
      // 1.5. Validate business email
      console.log('[BUSINESS_REGISTRATION] Validating business email...')
      const emailValidation = validateBusinessEmail(validatedData.email)
      if (!emailValidation.isBusinessEmail) {
        console.log('[BUSINESS_REGISTRATION] Business email validation failed:', emailValidation.reason)
        return fail(400, {
          error: emailValidation.reason || 'Please use a business email address',
          formData: rawData
        })
      }
      
      const emailDomain = emailValidation.domain
      console.log('[BUSINESS_REGISTRATION] Business email validation passed for domain:', emailDomain)

      // 1.7. Domain detection for intelligent routing
      console.log('[BUSINESS_REGISTRATION] Running domain detection...')
      const domainDetector = new DomainDetectionService(serviceClient)
      const detectionResult = await domainDetector.detectDomain(validatedData.email)
      
      console.log('[BUSINESS_REGISTRATION] Domain detection result:', {
        type: detectionResult.isExistingCustomer ? 'EXISTING_CUSTOMER' : 'NEW_COMPANY',
        routing_authority: detectionResult.routingAuthority,
        existing_company: detectionResult.detectedCompanyName || 'none'
      })

      // 1.8. Audit log domain detection
      const auditLogger = new AuditLogger(serviceClient)
      await auditLogger.logDomainDetection(
        'pending', // Will be updated with actual registration ID after insert
        emailDomain,
        detectionResult,
        // Note: In a real app, you'd get IP from request headers
        undefined
      )

      // 2. Check against company blacklist
      console.log('[BUSINESS_REGISTRATION] Checking company blacklist...')
      const { data: blacklisted, error: blacklistError } = await serviceClient
        .from('company_blacklist')
        .select('domain')
        .eq('domain', emailDomain)
        .maybeSingle()

      if (blacklistError) {
        console.error('[BUSINESS_REGISTRATION] Blacklist check failed:', blacklistError)
        return fail(500, {
          error: 'Unable to verify company eligibility. Please try again.',
          formData: rawData
        })
      }

      if (blacklisted) {
        console.log('[BUSINESS_REGISTRATION] Domain is blacklisted:', emailDomain)
        return fail(400, {
          error: 'Registrations from this domain are not permitted.',
          formData: rawData
        })
      }

      console.log('[BUSINESS_REGISTRATION] Domain check passed, inserting business lead...')

      // 3. Insert the validated data as a business lead for admin review.
      // We are no longer creating a user or company record directly.
      const insertData = {
        company_name: validatedData.company_name,
        primary_contact: {
          first_name: validatedData.first_name,
          last_name: validatedData.last_name,
          email: validatedData.email,
          phone: validatedData.phone,
          title: validatedData.title
        },
        company_details: {
          legal_entity_type: validatedData.legal_entity_type,
          tax_id: validatedData.tax_id,
          business_type: validatedData.business_type,
          estimated_annual_volume: validatedData.estimated_annual_volume,
          recruitment_enabled: validatedData.recruitment_enabled,
          bench_sales_enabled: validatedData.bench_sales_enabled,
          time_zone: validatedData.time_zone,
          business_address: {
            street: validatedData.street_address,
            city: validatedData.city,
            state: validatedData.state,
            zip_code: validatedData.zip_code,
            country: 'US'
          },
          working_hours: {
            start: validatedData.work_start,
            end: validatedData.work_end
          }
        }
      }

      console.log('[BUSINESS_REGISTRATION] Attempting to insert business lead for:', insertData.company_name)

      const { data, error } = await serviceClient
        .from('business_registrations')
        .insert({
          contact_person_name: `${validatedData.first_name} ${validatedData.last_name}`,
          contact_person_title: validatedData.title,
          contact_person_email: validatedData.email,
          contact_person_phone: validatedData.phone,
          company_name: validatedData.company_name,
          company_domain: emailDomain,
          status: 'pending',
          // Domain detection results - map correctly
          domain_detection_result: {
            type: detectionResult.isExistingCustomer ? 'EXISTING_CUSTOMER' : 'NEW_COMPANY',
            domain: detectionResult.domain,
            confidence: detectionResult.confidence,
            detected_company_id: detectionResult.detectedCompanyId || null,
            detected_company_name: detectionResult.detectedCompanyName || null,
            detection_timestamp: detectionResult.detectionTimestamp
          },
          routing_authority: detectionResult.routingAuthority,
          detected_company_id: detectionResult.detectedCompanyId || null
        })
        .select()

      if (error) {
        console.error('[BUSINESS_REGISTRATION] Database insert failed:', {
          code: error.code,
          message: error.message,
          details: error.details
        })
        return fail(500, {
          error: 'There was a problem submitting your application. Please try again.',
          formData: rawData
        })
      }

      console.log('[BUSINESS_REGISTRATION] Business lead created successfully with ID:', data?.[0]?.id)

      // Update audit log with actual registration ID
      if (data?.[0]?.id) {
        await auditLogger.logDomainDetection(
          data[0].id,
          emailDomain,
          detectionResult,
          undefined
        )
      }

      // Send acknowledgment email
      try {
        await businessRegistrationEmails.sendRegistrationSubmittedEmail({
          company_name: validatedData.company_name,
          contact_name: `${validatedData.first_name} ${validatedData.last_name}`,
          contact_email: validatedData.email,
          submission_date: new Date().toLocaleDateString(),
          registration_id: data?.[0]?.id || 'Unknown'
        })
        console.log('[BUSINESS_REGISTRATION] Acknowledgment email sent successfully')
      } catch (emailError) {
        console.error('[BUSINESS_REGISTRATION] Failed to send acknowledgment email:', emailError)
        // Continue with registration even if email fails
      }

      // Send admin notifications based on domain detection
      try {
        console.log('[BUSINESS_REGISTRATION] Sending admin notifications...')
        
        // Simple admin notification - will enhance later
        if (detectionResult.routingAuthority === 'console_admin') {
          console.log('[BUSINESS_REGISTRATION] New company registration - Console admin notification required')
          // TODO: Send console admin email notification
        } else {
          console.log('[BUSINESS_REGISTRATION] Existing company registration - Company admin notification required')
          // TODO: Send company admin email notification
        }
        
        console.log('[BUSINESS_REGISTRATION] Admin notification routing completed')
        
      } catch (adminEmailError) {
        console.error('[BUSINESS_REGISTRATION] Failed to process admin notifications:', adminEmailError)
        // Continue with registration even if admin notifications fail
      }

    } catch (e: unknown) {
      if (e instanceof ZodError) {
        console.error('[BUSINESS_REGISTRATION] Validation error:', e.flatten().fieldErrors)
        const errors = e.flatten().fieldErrors
        return fail(400, {
          error: Object.values(errors).flat().join(', '),
          formData: rawData
        })
      }
      
      console.error('[BUSINESS_REGISTRATION] Unexpected error:', e)
      return fail(500, {
        error: 'An unexpected error occurred. Please try again.',
        formData: rawData
      })
    }

    // 4. Redirect to a new "application submitted" page
    console.log('[BUSINESS_REGISTRATION] Registration completed successfully, redirecting...')
    throw redirect(303, '/registration/submitted')
  }
}

