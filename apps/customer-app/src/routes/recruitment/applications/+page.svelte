<script lang="ts">
	import EmptyState from '$lib/components/ui/empty-state.svelte'
	import { FileText } from 'lucide-svelte'
	
	export let data
</script>

<svelte:head>
	<title>Applications - Recruitment - ProcureServe</title>
</svelte:head>

<div class="space-y-6">
	<div>
		<h1 class="text-2xl font-bold text-gray-900">Applications</h1>
		<p class="text-gray-600">Track and manage job applications</p>
	</div>
	
	<EmptyState 
		icon={FileText}
		title="Applications Module Coming Soon"
		description="Application tracking features will be implemented in the next phase"
	/>
</div>
