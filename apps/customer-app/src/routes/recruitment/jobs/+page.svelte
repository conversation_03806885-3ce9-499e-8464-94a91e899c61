<script lang="ts">
	import EmptyState from '$lib/components/ui/empty-state.svelte'
	import { Briefcase } from 'lucide-svelte'
	
	export let data
</script>

<svelte:head>
	<title>Jobs - Recruitment - ProcureServe</title>
</svelte:head>

<div class="space-y-6">
	<div>
		<h1 class="text-2xl font-bold text-gray-900">Jobs</h1>
		<p class="text-gray-600">Manage job postings and requirements</p>
	</div>
	
	<EmptyState 
		icon={Briefcase}
		title="Jobs Module Coming Soon"
		description="Job management features will be implemented in the next phase"
	/>
</div>
