<script lang="ts">
	import EmptyState from '$lib/components/ui/empty-state.svelte'
	import { Users } from 'lucide-svelte'
	
	export let data
</script>

<svelte:head>
	<title>Candidates - Recruitment - ProcureServe</title>
</svelte:head>

<div class="space-y-6">
	<div>
		<h1 class="text-2xl font-bold text-gray-900">Candidates</h1>
		<p class="text-gray-600">Manage candidate profiles and applications</p>
	</div>
	
	<EmptyState 
		icon={Users}
		title="Candidates Module Coming Soon"
		description="Candidate management features will be implemented in the next phase"
	/>
</div>
