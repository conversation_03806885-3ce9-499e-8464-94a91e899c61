import { redirect } from '@sveltejs/kit'
import type { PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ locals }) => {
  const { data: { session } } = await locals.supabase.auth.getSession()
  
  if (!session) {
    throw redirect(303, '/login')
  }
  
  const { data: user, error: userError } = await locals.supabase
    .from('users')
    .select('*')
    .eq('id', session.user.id)
    .single()
  
  if (userError || !user || !user.process_permissions?.includes('recruitment')) {
    throw redirect(303, '/access-denied')
  }
  
  return { user }
}
