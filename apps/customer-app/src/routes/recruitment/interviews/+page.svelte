<script lang="ts">
	import EmptyState from '$lib/components/ui/empty-state.svelte'
	import { Calendar } from 'lucide-svelte'
	
	export let data
</script>

<svelte:head>
	<title>Interviews - Recruitment - ProcureServe</title>
</svelte:head>

<div class="space-y-6">
	<div>
		<h1 class="text-2xl font-bold text-gray-900">Interviews</h1>
		<p class="text-gray-600">Schedule and manage candidate interviews</p>
	</div>
	
	<EmptyState 
		icon={Calendar}
		title="Interviews Module Coming Soon"
		description="Interview scheduling features will be implemented in the next phase"
	/>
</div>
