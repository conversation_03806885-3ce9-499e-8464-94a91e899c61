<!-- Create test users page -->
<script lang="ts">
  let creating = false
  let results: any = null

  async function createUsers() {
    creating = true
    try {
      const response = await fetch('/api/create-test-users', {
        method: 'POST'
      })
      results = await response.json()
    } catch (err) {
      results = { error: 'Failed to create users', details: err.message }
    }
    creating = false
  }
</script>

<svelte:head>
  <title>Create Test Users - ProcureServe</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 py-12 px-4">
  <div class="max-w-2xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-900 mb-8">🔧 Create Test Users</h1>
    
    <div class="bg-white rounded-lg shadow p-6">
      <p class="text-gray-600 mb-6">
        This will create Supabase Auth users for testing the notification system.
        The users will be created with email/password authentication.
      </p>
      
      <div class="space-y-4">
        <div>
          <h3 class="font-medium">Users to be created:</h3>
          <ul class="text-sm text-gray-600 mt-2 space-y-1">
            <li>• <EMAIL> (password123)</li>
            <li>• <EMAIL> (password123)</li>
          </ul>
        </div>

        <button 
          on:click={createUsers}
          disabled={creating}
          class="w-full py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {creating ? 'Creating Users...' : 'Create Test Users'}
        </button>
      </div>

      {#if results}
        <div class="mt-6 p-4 {results.error ? 'bg-red-50 border-red-200' : 'bg-green-50 border-green-200'} border rounded">
          <h3 class="font-medium {results.error ? 'text-red-800' : 'text-green-800'}">
            {results.error ? 'Error' : 'Results'}
          </h3>
          
          {#if results.error}
            <p class="text-red-700 text-sm mt-1">{results.error}</p>
            {#if results.details}
              <pre class="text-xs text-red-600 mt-2 bg-red-100 p-2 rounded overflow-x-auto">{results.details}</pre>
            {/if}
          {:else}
            <div class="space-y-2 mt-2">
              {#each results.results as result}
                <div class="text-sm">
                  <span class="font-medium">{result.email}:</span>
                  <span class="{result.success ? 'text-green-700' : 'text-red-700'}">
                    {result.success ? '✅ Created' : '❌ Failed'}
                  </span>
                  {#if result.error}
                    <span class="text-red-600">({result.error})</span>
                  {/if}
                </div>
              {/each}
            </div>
            
            {#if results.results?.some(r => r.success)}
              <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
                <p class="text-blue-800 text-sm">
                  ✅ Users created successfully! You can now test login at <a href="/login" class="underline">/login</a>
                </p>
              </div>
            {/if}
          {/if}
        </div>
      {/if}
    </div>
  </div>
</div>
