import { json } from '@sveltejs/kit'
import type { RequestHandler } from './$types'

export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    const { data: { session } } = await locals.supabase.auth.getSession()
    
    if (!session) {
      return json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Get user and verify permissions
    const { data: user } = await locals.supabase
      .from('users')
      .select('company_id, process_permissions')
      .eq('id', session.user.id)
      .single()
    
    if (!user || !user.process_permissions?.includes('bench_sales')) {
      return json({ error: 'Access denied' }, { status: 403 })
    }
    
    const consultantData = await request.json()
    
    // Validate required fields
    if (!consultantData.first_name || !consultantData.last_name || !consultantData.email) {
      return json({ 
        errors: { 
          general: 'First name, last name, and email are required' 
        } 
      }, { status: 400 })
    }
    
    // Insert consultant
    const { data: consultant, error } = await locals.supabase
      .from('consultants')
      .insert({
        ...consultantData,
        company_id: user.company_id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      console.error('Error creating consultant:', error)
      return json({ 
        errors: { 
          general: 'Failed to create consultant' 
        } 
      }, { status: 500 })
    }
    
    return json({ consultant })
    
  } catch (error) {
    console.error('Error in consultant creation:', error)
    return json({ 
      errors: { 
        general: 'An unexpected error occurred' 
      } 
    }, { status: 500 })
  }
}
