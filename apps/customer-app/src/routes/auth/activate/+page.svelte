<script lang="ts">
	import { enhance } from '$app/forms'
	import { Eye, EyeOff, Shield, Key, CheckCircle } from 'lucide-svelte'
	import Button from '$lib/components/ui/button.svelte'
	import Input from '$lib/components/ui/input.svelte'
	import Label from '$lib/components/ui/label.svelte'
	import Card from '$lib/components/ui/card.svelte'
	import CardContent from '$lib/components/ui/card-content.svelte'
	
	export let data
	export let form
	
	let loading = false
	let showPassword = false
	let showConfirmPassword = false
	
	// Password strength validation
	let password = ''
	let confirmPassword = ''
	
	$: passwordStrength = calculatePasswordStrength(password)
	$: passwordsMatch = password === confirmPassword && confirmPassword.length > 0
	$: formValid = password.length >= 8 && passwordsMatch
	
	function calculatePasswordStrength(pwd: string) {
		if (!pwd) return { score: 0, label: '', color: '' }
		
		let score = 0
		if (pwd.length >= 8) score++
		if (/[a-z]/.test(pwd)) score++
		if (/[A-Z]/.test(pwd)) score++
		if (/\d/.test(pwd)) score++
		if (/[^a-zA-Z\d]/.test(pwd)) score++
		
		if (score <= 2) return { score, label: 'Weak', color: 'text-red-600' }
		if (score <= 3) return { score, label: 'Fair', color: 'text-yellow-600' }
		if (score <= 4) return { score, label: 'Good', color: 'text-blue-600' }
		return { score, label: 'Strong', color: 'text-green-600' }
	}
</script>

<svelte:head>
	<title>Activate Account - ProcureServe</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
	<div class="sm:mx-auto sm:w-full sm:max-w-md">
		<div class="text-center">
			<Shield class="mx-auto h-12 w-12 text-primary" />
			<h2 class="mt-6 text-3xl font-bold text-gray-900">Activate Your Account</h2>
			<p class="mt-2 text-sm text-gray-600">
				Welcome to <strong>{data.companyName}</strong>! Set your password to complete account activation.
			</p>
		</div>
	</div>

	<div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
		{#if form?.error}
			<div class="mb-6 p-4 rounded-lg bg-red-50 border border-red-200">
				<p class="text-red-800 text-sm">{form.error}</p>
				
				{#if form && 'show_request_new' in form && form.show_request_new}
					<div class="mt-4">
						<a 
							href="/auth/request-activation?company_id={data.companyId}&expired=true"
							class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
						>
							Request New Activation Link
						</a>
					</div>
				{/if}
			</div>
		{/if}

		<Card>
			<CardContent class="py-6">
				<!-- Account Info -->
				<div class="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
					<h3 class="text-sm font-medium text-blue-900 mb-2">Account Details</h3>
					<div class="text-sm text-blue-800 space-y-1">
						<p><strong>Company:</strong> {data.companyName}</p>
						<p><strong>Email:</strong> {data.contactEmail}</p>
						<p><strong>Name:</strong> {data.contactName}</p>
					</div>
				</div>

				<form 
					method="POST" 
					action="?/activate"
					use:enhance={() => {
						loading = true
						return async ({ update }) => {
							await update()
							loading = false
						}
					}}
					class="space-y-6"
				>
					<!-- Hidden fields -->
					<input type="hidden" name="token" value={data.token} />
					<input type="hidden" name="company_id" value={data.companyId} />

					<!-- Password -->
					<div>
						<Label for="password">Password *</Label>
						<div class="mt-1 relative">
							<Input
								id="password"
								name="password"
								type={showPassword ? 'text' : 'password'}
								bind:value={password}
								required
								class="pr-10"
								placeholder="Enter your password"
							/>
							<button
								type="button"
								class="absolute inset-y-0 right-0 pr-3 flex items-center"
								on:click={() => showPassword = !showPassword}
							>
								{#if showPassword}
									<EyeOff class="h-4 w-4 text-gray-400" />
								{:else}
									<Eye class="h-4 w-4 text-gray-400" />
								{/if}
							</button>
						</div>
						
						{#if password}
							<div class="mt-2">
								<div class="flex items-center justify-between text-xs">
									<span class="text-gray-600">Password strength:</span>
									<span class="{passwordStrength.color} font-medium">{passwordStrength.label}</span>
								</div>
								<div class="mt-1 w-full bg-gray-200 rounded-full h-1.5">
									<div 
										class="h-1.5 rounded-full transition-all duration-300 {
											passwordStrength.score <= 2 ? 'bg-red-500' :
											passwordStrength.score <= 3 ? 'bg-yellow-500' :
											passwordStrength.score <= 4 ? 'bg-blue-500' : 'bg-green-500'
										}"
										style="width: {(passwordStrength.score / 5) * 100}%"
									></div>
								</div>
							</div>
						{/if}
						
						<div class="mt-2 text-xs text-gray-600">
							<p>Password must be at least 8 characters and include:</p>
							<ul class="mt-1 space-y-1 list-disc list-inside">
								<li class="{password.length >= 8 ? 'text-green-600' : ''}">At least 8 characters</li>
								<li class="{/[a-z]/.test(password) ? 'text-green-600' : ''}">Lowercase letter</li>
								<li class="{/[A-Z]/.test(password) ? 'text-green-600' : ''}">Uppercase letter</li>
								<li class="{/\d/.test(password) ? 'text-green-600' : ''}">Number</li>
								<li class="{/[^a-zA-Z\d]/.test(password) ? 'text-green-600' : ''}">Special character</li>
							</ul>
						</div>
					</div>

					<!-- Confirm Password -->
					<div>
						<Label for="confirm_password">Confirm Password *</Label>
						<div class="mt-1 relative">
							<Input
								id="confirm_password"
								name="confirm_password"
								type={showConfirmPassword ? 'text' : 'password'}
								bind:value={confirmPassword}
								required
								class="pr-10"
								placeholder="Confirm your password"
							/>
							<button
								type="button"
								class="absolute inset-y-0 right-0 pr-3 flex items-center"
								on:click={() => showConfirmPassword = !showConfirmPassword}
							>
								{#if showConfirmPassword}
									<EyeOff class="h-4 w-4 text-gray-400" />
								{:else}
									<Eye class="h-4 w-4 text-gray-400" />
								{/if}
							</button>
						</div>
						
						{#if confirmPassword}
							<div class="mt-1 flex items-center">
								{#if passwordsMatch}
									<CheckCircle class="h-4 w-4 text-green-500 mr-1" />
									<span class="text-xs text-green-600">Passwords match</span>
								{:else}
									<span class="text-xs text-red-600">Passwords don't match</span>
								{/if}
							</div>
						{/if}
					</div>

					<!-- Submit -->
					<Button
						type="submit"
						disabled={loading || !formValid}
						class="w-full"
					>
						{#if loading}
							<div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
							Activating Account...
						{:else}
							<Key class="w-4 h-4 mr-2" />
							Activate Account
						{/if}
					</Button>
				</form>

				<!-- Security Notice -->
				<div class="mt-6 p-4 bg-gray-50 rounded-lg border">
					<h4 class="text-sm font-medium text-gray-900 mb-2">Security Notice</h4>
					<ul class="text-xs text-gray-600 space-y-1">
						<li>• This activation link can only be used once</li>
						<li>• Your account will be ready for immediate use after activation</li>
						<li>• You can sign in using your email and the password you set here</li>
					</ul>
				</div>
			</CardContent>
		</Card>

		<!-- Footer -->
		<div class="mt-6 text-center">
			<p class="text-xs text-gray-600">
				Need help? Contact us at 
				<a href="mailto:<EMAIL>" class="text-primary hover:underline">
					<EMAIL>
				</a>
			</p>
		</div>
	</div>
</div> 