import { redirect, fail } from '@sveltejs/kit'
import type { PageServerLoad, Actions } from './$types'
import { createSupabaseServiceClient } from '$lib/supabase-service'
import { z } from 'zod'

const activationSchema = z.object({
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirm_password: z.string().min(8, 'Please confirm your password')
}).refine(data => data.password === data.confirm_password, {
  message: "Passwords don't match",
  path: ["confirm_password"]
})

export const load: PageServerLoad = async ({ url, locals }) => {
  const token = url.searchParams.get('token')
  const companyId = url.searchParams.get('company_id')
  
  if (!token || !companyId) {
    throw redirect(303, '/login?error=invalid_activation_link')
  }

  // Verify the activation token is valid
  const serviceClient = createSupabaseServiceClient()
  
  const { data: activationToken, error } = await serviceClient
    .from('activation_tokens')
    .select(`
      *,
      companies (
        name,
        primary_contact
      )
    `)
    .eq('token', token)
    .eq('company_id', companyId)
    .eq('used', false)
    .single()

  if (error || !activationToken) {
    console.error('[ACTIVATION] Token not found:', error)
    throw redirect(303, '/auth/request-activation?error=token_not_found')
  }

  // Check if token has expired
  if (new Date(activationToken.expires_at) < new Date()) {
    console.log('[ACTIVATION] Token expired for:', activationToken.contact_email)
    throw redirect(303, `/auth/request-activation?token=${token}&company_id=${companyId}&expired=true`)
  }

  return {
    token,
    companyId,
    companyName: activationToken.companies?.name,
    contactEmail: activationToken.contact_email,
    contactName: activationToken.contact_name
  }
}

export const actions: Actions = {
  activate: async ({ request, url }) => {
    const formData = await request.formData()
    const rawData = Object.fromEntries(formData)
    
    const token = rawData.token as string
    const companyId = rawData.company_id as string
    
    try {
      // Validate form data
      const validatedData = activationSchema.parse(rawData)
      
      const serviceClient = createSupabaseServiceClient()
      
      // Verify token is still valid
      const { data: activationToken, error: tokenError } = await serviceClient
        .from('activation_tokens')
        .select('*')
        .eq('token', token)
        .eq('company_id', companyId)
        .eq('used', false)
        .single()

      if (tokenError || !activationToken) {
        console.error('[ACTIVATION] Token verification failed:', tokenError)
        return fail(400, {
          error: 'Invalid activation link. Please request a new one.',
          formData: rawData,
          show_request_new: true
        })
      }

      // Check if token has expired
      if (new Date(activationToken.expires_at) < new Date()) {
        console.log('[ACTIVATION] Token expired during activation')
        return fail(400, {
          error: 'This activation link has expired. Please request a new one.',
          formData: rawData,
          show_request_new: true,
          expired: true
        })
      }

      // Create user in Supabase Auth
      const { data: authUser, error: authError } = await serviceClient.auth.admin.createUser({
        email: activationToken.contact_email,
        password: validatedData.password,
        email_confirm: true, // Auto-confirm email since it's pre-verified
        user_metadata: {
          first_name: activationToken.contact_name.split(' ')[0],
          last_name: activationToken.contact_name.split(' ').slice(1).join(' '),
          company_id: companyId
        }
      })

      if (authError) {
        console.error('[ACTIVATION] Auth user creation failed:', authError)
        return fail(500, {
          error: 'Failed to create user account. Please try again.',
          formData: rawData
        })
      }

      // Create user record in customer app
      const { error: userError } = await serviceClient
        .from('users')
        .insert({
          id: authUser.user.id,
          email: activationToken.contact_email,
          role: 'admin', // First user is company admin
          company_id: companyId,
          is_active: true,
          process_permissions: activationToken.process_permissions || ['recruitment', 'bench_sales'],
          current_process: null, // Will be set on first login
          profile: {
            first_name: activationToken.contact_name.split(' ')[0],
            last_name: activationToken.contact_name.split(' ').slice(1).join(' '),
            process_permissions: activationToken.process_permissions || ['recruitment', 'bench_sales']
          }
        })

      if (userError) {
        console.error('[ACTIVATION] User record creation failed:', userError)
        
        // Clean up auth user if customer user creation fails
        await serviceClient.auth.admin.deleteUser(authUser.user.id)
        
        return fail(500, {
          error: 'Failed to complete account setup. Please try again.',
          formData: rawData
        })
      }

      // Mark token as used
      await serviceClient
        .from('activation_tokens')
        .update({ 
          used: true, 
          used_at: new Date().toISOString(),
          activated_user_id: authUser.user.id
        })
        .eq('id', activationToken.id)

      // Update company status to active
      await serviceClient
        .from('companies')
        .update({ 
          registration_status: 'active',
          activated_at: new Date().toISOString()
        })
        .eq('id', companyId)

      console.log('[ACTIVATION] Account activated successfully for:', activationToken.contact_email)

      // Redirect to success page with instructions to sign in
      throw redirect(303, '/auth/activation-complete')

    } catch (e: any) {
      if (e instanceof z.ZodError) {
        const errors = e.flatten().fieldErrors
        return fail(400, {
          error: Object.values(errors).flat().join(', '),
          formData: rawData
        })
      }
      
      console.error('[ACTIVATION] Unexpected error:', e)
      return fail(500, {
        error: 'An unexpected error occurred. Please try again.',
        formData: rawData
      })
    }
  }
} 