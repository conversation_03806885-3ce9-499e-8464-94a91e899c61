import { redirect, fail } from '@sveltejs/kit'
import type { PageServerLoad, Actions } from './$types'
import { createSupabaseServiceClient } from '$lib/supabase-service'
import { businessRegistrationEmails } from '$lib/business-registration-emails'
import { renderEmailTemplate } from '@psii/email-service'
import { z } from 'zod'
import crypto from 'crypto'

const requestSchema = z.object({
  email: z.string().email('Please enter a valid email address')
})

export const load: PageServerLoad = async ({ url }) => {
  const token = url.searchParams.get('token')
  const companyId = url.searchParams.get('company_id')
  const expired = url.searchParams.get('expired') === 'true'
  const error = url.searchParams.get('error')

  let companyInfo = null
  let contactEmail = null

  if (token && companyId) {
    // Try to get company info from the expired token
    const serviceClient = createSupabaseServiceClient()
    const { data: activationToken } = await serviceClient
      .from('activation_tokens')
      .select(`
        contact_email,
        contact_name,
        companies (
          name
        )
      `)
      .eq('token', token)
      .eq('company_id', companyId)
      .single()

    if (activationToken) {
      companyInfo = {
        name: activationToken.companies?.name,
        contactName: activationToken.contact_name
      }
      contactEmail = activationToken.contact_email
    }
  }

  return {
    expired,
    error,
    companyInfo,
    contactEmail,
    companyId
  }
}

export const actions: Actions = {
  request: async ({ request, url }) => {
    const formData = await request.formData()
    const rawData = Object.fromEntries(formData)

    try {
      const validatedData = requestSchema.parse(rawData)
      const serviceClient = createSupabaseServiceClient()

      // Find the company associated with this email
      const { data: existingToken, error: tokenError } = await serviceClient
        .from('activation_tokens')
        .select(`
          *,
          companies (
            id,
            name,
            registration_status
          )
        `)
        .eq('contact_email', validatedData.email)
        .eq('used', false)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (tokenError || !existingToken) {
        // For security, don't reveal if email exists or not
        return {
          success: true,
          message: 'If a pending activation exists for this email, a new activation link has been sent.'
        }
      }

      // Check if company is already active
      if (existingToken.companies?.registration_status === 'active') {
        return {
          success: true,
          message: 'This account is already activated. You can sign in normally.'
        }
      }

      // Check rate limiting - only allow one new token per hour
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()
      const { data: recentTokens } = await serviceClient
        .from('activation_tokens')
        .select('created_at')
        .eq('contact_email', validatedData.email)
        .gte('created_at', oneHourAgo)

      if (recentTokens && recentTokens.length >= 3) {
        return fail(429, {
          error: 'Too many requests. Please wait at least 1 hour before requesting a new activation link.',
          email: validatedData.email
        })
      }

      // Generate new activation token
      const newToken = crypto.randomBytes(32).toString('hex')
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

      // Create new activation token
      const { error: insertError } = await serviceClient
        .from('activation_tokens')
        .insert({
          company_id: existingToken.company_id,
          token: newToken,
          contact_email: existingToken.contact_email,
          contact_name: existingToken.contact_name,
          expires_at: expiresAt.toISOString(),
          process_permissions: existingToken.process_permissions
        })

      if (insertError) {
        console.error('[REQUEST_ACTIVATION] Failed to create new token:', insertError)
        return fail(500, {
          error: 'Failed to generate new activation link. Please try again.',
          email: validatedData.email
        })
      }

      // Send new activation email
      const activationLink = `${url.origin}/auth/activate?token=${newToken}&company_id=${existingToken.company_id}`

      // Use the renewed activation template for better UX
      const emailContent = renderEmailTemplate('activation_link_renewed', {
        company_name: existingToken.companies?.name || 'Your Company',
        contact_name: existingToken.contact_name,
        contact_email: existingToken.contact_email,
        activation_link: activationLink
      })

      await businessRegistrationEmails.emailService.sendEmail({
        to: existingToken.contact_email,
        template_type: 'user_invitation',
        company_id: existingToken.company_id,
        variables: {
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text,
          company_name: existingToken.companies?.name || 'Your Company',
          contact_name: existingToken.contact_name,
          activation_link: activationLink
        }
      })

      console.log('[REQUEST_ACTIVATION] New activation link sent to:', validatedData.email)

      return {
        success: true,
        message: 'A new activation link has been sent to your email address. Please check your inbox.',
        email: validatedData.email
      }

    } catch (e: any) {
      if (e instanceof z.ZodError) {
        const errors = e.flatten().fieldErrors
        return fail(400, {
          error: Object.values(errors).flat().join(', '),
          email: rawData.email
        })
      }

      console.error('[REQUEST_ACTIVATION] Unexpected error:', e)
      return fail(500, {
        error: 'An unexpected error occurred. Please try again.',
        email: rawData.email
      })
    }
  }
} 