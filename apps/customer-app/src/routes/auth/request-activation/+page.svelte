<script lang="ts">
	import { enhance } from '$app/forms'
	import { Mail, AlertTriangle, CheckCircle, Clock, ArrowLeft } from 'lucide-svelte'
	import Button from '$lib/components/ui/button.svelte'
	import Input from '$lib/components/ui/input.svelte'
	import Label from '$lib/components/ui/label.svelte'
	import Card from '$lib/components/ui/card.svelte'
	import CardContent from '$lib/components/ui/card-content.svelte'
	
	export let data
	export let form
	
	let loading = false
</script>

<svelte:head>
	<title>Request New Activation Link - ProcureServe</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
	<div class="sm:mx-auto sm:w-full sm:max-w-md">
		<div class="text-center">
			<Mail class="mx-auto h-12 w-12 text-primary" />
			<h2 class="mt-6 text-3xl font-bold text-gray-900">Request New Activation Link</h2>
			<p class="mt-2 text-sm text-gray-600">
				{#if data.expired}
					Your activation link has expired. Request a new one below.
				{:else}
					Get a fresh activation link sent to your email.
				{/if}
			</p>
		</div>
	</div>

	<div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
		{#if data.error === 'token_not_found'}
			<div class="mb-6 p-4 rounded-lg bg-yellow-50 border border-yellow-200">
				<div class="flex">
					<AlertTriangle class="h-5 w-5 text-yellow-400 mr-2" />
					<p class="text-yellow-800 text-sm">
						The activation link you used is invalid or has been removed. Please request a new one below.
					</p>
				</div>
			</div>
		{/if}

		{#if form?.error}
			<div class="mb-6 p-4 rounded-lg bg-red-50 border border-red-200">
				<p class="text-red-800 text-sm">{form.error}</p>
			</div>
		{/if}

		{#if form?.success}
			<Card>
				<CardContent class="py-6">
					<div class="text-center">
						<CheckCircle class="mx-auto h-12 w-12 text-green-500 mb-4" />
						<h3 class="text-lg font-medium text-gray-900 mb-2">Request Sent Successfully</h3>
						<p class="text-sm text-gray-600 mb-6">{form.message}</p>
						
						<div class="space-y-4">
							<div class="p-4 bg-blue-50 rounded-lg border border-blue-200">
								<h4 class="text-sm font-medium text-blue-900 mb-2">What's Next?</h4>
								<ul class="text-sm text-blue-800 space-y-1 text-left">
									<li>• Check your email inbox (and spam folder)</li>
									<li>• Click the new activation link</li>
									<li>• Set your password to complete activation</li>
									<li>• The new link will expire in 7 days</li>
								</ul>
							</div>
							
							<Button href="/login" variant="outline" class="w-full">
								<ArrowLeft class="w-4 h-4 mr-2" />
								Back to Login
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		{:else}
			<Card>
				<CardContent class="py-6">
					{#if data.companyInfo}
						<!-- Show company info if available -->
						<div class="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
							<h3 class="text-sm font-medium text-blue-900 mb-2">Account Details</h3>
							<div class="text-sm text-blue-800 space-y-1">
								<p><strong>Company:</strong> {data.companyInfo.name}</p>
								<p><strong>Contact:</strong> {data.companyInfo.contactName}</p>
							</div>
						</div>
					{/if}

					<form 
						method="POST" 
						action="?/request"
						use:enhance={() => {
							loading = true
							return async ({ update }) => {
								await update()
								loading = false
							}
						}}
						class="space-y-6"
					>
						<div>
							<Label for="email">Email Address *</Label>
							<div class="mt-1">
								<Input
									id="email"
									name="email"
									type="email"
									value={data.contactEmail || form?.email || ''}
									required
									placeholder="Enter your email address"
									class="block w-full"
								/>
							</div>
							<p class="mt-1 text-xs text-gray-600">
								Enter the email address associated with your business registration.
							</p>
						</div>

						<Button
							type="submit"
							disabled={loading}
							class="w-full"
						>
							{#if loading}
								<div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
								Sending Request...
							{:else}
								<Mail class="w-4 h-4 mr-2" />
								Send New Activation Link
							{/if}
						</Button>
					</form>

					<!-- Rate Limiting Notice -->
					<div class="mt-6 p-4 bg-gray-50 rounded-lg border">
						<div class="flex items-start">
							<Clock class="h-4 w-4 text-gray-400 mr-2 mt-0.5" />
							<div>
								<h4 class="text-sm font-medium text-gray-900">Rate Limiting</h4>
								<p class="text-xs text-gray-600 mt-1">
									For security, you can only request up to 3 new activation links per hour. 
									Each link is valid for 7 days.
								</p>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		{/if}

		<!-- Footer -->
		<div class="mt-6 text-center space-y-2">
			<p class="text-xs text-gray-600">
				Need help? Contact us at 
				<a href="mailto:<EMAIL>" class="text-primary hover:underline">
					<EMAIL>
				</a>
			</p>
			
			{#if !form?.success}
				<p class="text-xs text-gray-600">
					<a href="/login" class="text-primary hover:underline">
						← Back to Login
					</a>
				</p>
			{/if}
		</div>
	</div>
</div> 