<script lang="ts">
	import { enhance } from '$app/forms'
	import { goto } from '$app/navigation'
	import { toastSuccess, toastError } from '$lib'
	import Card from '$lib/components/ui/card.svelte'
	import CardHeader from '$lib/components/ui/card-header.svelte'
	import CardTitle from '$lib/components/ui/card-title.svelte'
	import CardDescription from '$lib/components/ui/card-description.svelte'
	import CardContent from '$lib/components/ui/card-content.svelte'
	import Button from '$lib/components/ui/button.svelte'
	import Input from '$lib/components/ui/input.svelte'
	import Label from '$lib/components/ui/label.svelte'
	import { LogIn, AlertCircle, Briefcase } from 'lucide-svelte'
	
	export let form
	export let data
	
	let loading = false
	let email = ''
	let password = ''
	
	async function handleLogin() {
		loading = true
		// Form will be handled by the +page.server.ts action
	}
</script>

<svelte:head>
	<title>Login - ProcureServe</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
	<div class="max-w-md w-full space-y-8">
		<div class="text-center">
			<div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
				<Briefcase class="w-8 h-8 text-primary" />
			</div>
			<h2 class="text-3xl font-bold text-gray-900">Business Portal</h2>
			<p class="mt-2 text-sm text-gray-600">
				Sign in to your staffing and recruitment dashboard
			</p>
		</div>

		<Card>
			<CardHeader class="text-center">
				<CardTitle class="flex items-center justify-center gap-2">
					<LogIn class="w-5 h-5" />
					Sign In
				</CardTitle>
				<CardDescription>
					Enter your business account credentials
				</CardDescription>
			</CardHeader>
			
			<CardContent>
				<form method="POST" action="?/login" use:enhance={({ formData }) => {
					// Always set userType to business for this app
					formData.set('userType', 'business')
					loading = true
					return async ({ result, update }) => {
						loading = false
						if (result.type === 'redirect') {
							toastSuccess('Welcome back!', 'You have been successfully signed in.')
							goto(result.location)
						} else if (result.type === 'failure') {
							toastError('Login failed', result.data?.error || 'Please check your credentials and try again.')
							await update()
						} else {
							await update()
						}
					}
				}}>
					<div class="space-y-4">
						<div>
							<Label for="email">Business Email Address</Label>
							<Input
								id="email"
								name="email"
								type="email"
								bind:value={email}
								required
								placeholder="<EMAIL>"
							/>
						</div>
						
						<div>
							<Label for="password">Password</Label>
							<Input
								id="password"
								name="password"
								type="password"
								bind:value={password}
								required
								placeholder="Enter your password"
							/>
						</div>
						
						{#if data?.message}
							<div class="p-3 bg-green-50 border border-green-200 rounded-lg">
								<div class="flex items-center gap-2 text-green-800">
									<span class="text-sm">{data.message}</span>
								</div>
							</div>
						{/if}
						
						{#if data?.error}
							<div class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
								<div class="flex items-center gap-2 text-yellow-800">
									<AlertCircle class="w-4 h-4" />
									<span class="text-sm">{data.error}</span>
								</div>
							</div>
						{/if}

						{#if form?.error}
							<div class="p-3 bg-red-50 border border-red-200 rounded-lg">
								<div class="flex items-center gap-2 text-red-800">
									<AlertCircle class="w-4 h-4" />
									<span class="text-sm">{form.error}</span>
								</div>
							</div>
						{/if}
						
						<Button type="submit" class="w-full" disabled={loading}>
							{loading ? 'Signing in...' : 'Sign In'}
						</Button>
					</div>
				</form>
				
				<div class="mt-6 text-center space-y-2">
					<a href="/reset-password" class="text-sm text-primary hover:underline">
						Forgot your password?
					</a>
					
					<div class="text-sm text-gray-600">
						Don't have a business account?
						<a href="/register" class="text-primary hover:underline">Register your business</a>
					</div>
				</div>
			</CardContent>
		</Card>
		
		<!-- Job Seeker Portal Reference -->
		<div class="text-center">
			<div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
				<p class="text-sm text-blue-700">
					<strong>Job seekers:</strong> Access the candidate portal at 
					<a href="http://localhost:3007" class="underline font-medium">candidates.procureserve.com</a>
				</p>
			</div>
		</div>
		
		<div class="text-center">
			<p class="text-xs text-gray-500">
				By signing in, you agree to our Terms of Service and Privacy Policy
			</p>
		</div>
	</div>
</div>
