<!-- Job Detail View - ProcureServe II -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { supabase } from '$lib/supabase';
	import type { Job, Application, User } from '@procureserve/shared-types';
	
	// Import components
	import JobDetails from '$lib/components/jobs/JobDetails.svelte';
	import ApplicationsList from '$lib/components/jobs/ApplicationsList.svelte';
	import JobMetrics from '$lib/components/jobs/JobMetrics.svelte';
	import JobInfo from '$lib/components/jobs/JobInfo.svelte';
	import JobQuickActions from '$lib/components/jobs/JobQuickActions.svelte';
	import DeleteJobModal from '$lib/components/jobs/DeleteJobModal.svelte';

	// Component state
	let loading = true;
	let error: string | null = null;
	let job: Job | null = null;
	let applications: Application[] = [];
	let applicationsCount = 0;
	let createdByUser: User | null = null;
	let deleteModalOpen = false;

	// Get job ID from URL parameters
	$: jobId = $page.params.id;
	$: companyId = $page.data.customerUser?.company_id;
	$: userId = $page.data.user?.id;
	$: isRecruiter = $page.data.customerUser?.role === 'recruiter' || $page.data.customerUser?.role === 'admin';
	$: statusOptions = getEnumValues('job_statuses');
	$: remoteOptions = getEnumValues('remote_types');

	// Get configurable enum values
	function getEnumValues(category: string): { key: string; label: string; color: string }[] {
		const enums = $page.data.configurableEnums || {};
		return enums[category] || [];
	}

	// Load job data
	async function loadJobData() {
		if (!jobId || !companyId) {
			error = 'Invalid job ID or company ID.';
			loading = false;
			return;
		}

		loading = true;
		error = null;

		try {
			// Fetch job details
			const { data: jobData, error: jobError } = await supabase
				.from('jobs')
				.select('*')
				.eq('id', jobId)
				.eq('company_id', companyId)
				.single();

			if (jobError) throw jobError;
			if (!jobData) throw new Error('Job not found');
			
			job = jobData;

			// Fetch created by user info
			if (job.created_by) {
				const { data: userData, error: userError } = await supabase
					.from('users')
					.select('*')
					.eq('id', job.created_by)
					.single();

				if (!userError && userData) {
					createdByUser = userData;
				}
			}

			// Fetch applications count
			const { count, error: countError } = await supabase
				.from('applications')
				.select('*', { count: 'exact' })
				.eq('job_id', jobId);

			if (!countError) {
				applicationsCount = count || 0;
			}

			// Fetch recent applications
			const { data: applicationsData, error: applicationsError } = await supabase
				.from('applications')
				.select('*, candidates(*)')
				.eq('job_id', jobId)
				.order('created_at', { ascending: false })
				.limit(5);

			if (!applicationsError && applicationsData) {
				applications = applicationsData;
			}
		} catch (err: any) {
			console.error('Error loading job data:', err);
			error = 'Failed to load job data. Please try again later.';
		} finally {
			loading = false;
		}
	}

	// Update job status
	async function updateJobStatus(newStatus: string) {
		if (!job || !jobId || !companyId) return;

		try {
			const { error: updateError } = await supabase
				.from('jobs')
				.update({ status: newStatus, updated_at: new Date().toISOString() })
				.eq('id', jobId)
				.eq('company_id', companyId);

			if (updateError) throw updateError;

			// Update local job data
			job.status = newStatus;
			job.updated_at = new Date().toISOString();

			// Log activity
			await supabase.from('activity_logs').insert({
				company_id: companyId,
				user_id: userId,
				entity_type: 'job',
				entity_id: jobId,
				action: 'updated_status',
				details: {
					job_title: job.title,
					previous_status: job.status,
					new_status: newStatus,
					timestamp: new Date().toISOString()
				}
			});
		} catch (err) {
			console.error('Error updating job status:', err);
			error = 'Failed to update job status. Please try again.';
		}
	}

	// Delete job
	async function deleteJob() {
		if (!job || !jobId || !companyId) return;

		try {
			const { error: deleteError } = await supabase
				.from('jobs')
				.delete()
				.eq('id', jobId)
				.eq('company_id', companyId);

			if (deleteError) throw deleteError;

			// Log activity
			await supabase.from('activity_logs').insert({
				company_id: companyId,
				user_id: userId,
				entity_type: 'job',
				entity_id: jobId,
				action: 'deleted',
				details: {
					job_title: job.title,
					timestamp: new Date().toISOString()
				}
			});

			// Close modal and redirect
			deleteModalOpen = false;
			goto('/jobs');
		} catch (err) {
			console.error('Error deleting job:', err);
			error = 'Failed to delete job. Please try again.';
			deleteModalOpen = false;
		}
	}

	// Load data on mount
	onMount(() => {
		loadJobData();
	});
</script>

<DeleteJobModal 
	isOpen={deleteModalOpen} 
	onCancel={() => deleteModalOpen = false} 
	onConfirm={deleteJob} 
/>

<div class="p-4 sm:p-6">
	<div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
		<div>
			<a
				href="/jobs"
				class="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-900"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="h-5 w-5 mr-1"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M15 19l-7-7 7-7"
					/>
				</svg>
				Back to Jobs
			</a>
			<h1 class="text-2xl font-bold mt-2">
				{#if job}
					{job.title}
				{:else if loading}
					Loading...
				{:else}
					Job Details
				{/if}
			</h1>
		</div>
		{#if isRecruiter && job}
			<div class="mt-4 sm:mt-0 flex space-x-3">
				<a
					href={`/jobs/${jobId}/edit`}
					class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
				>
					<svg
						xmlns="http://www.w3.org/2000/svg"
						class="h-5 w-5 mr-2 text-gray-500"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
						/>
					</svg>
					Edit
				</a>
				<button
					type="button"
					on:click={() => (deleteModalOpen = true)}
					class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
				>
					<svg
						xmlns="http://www.w3.org/2000/svg"
						class="h-5 w-5 mr-2 text-red-500"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
						/>
					</svg>
					Delete
				</button>
			</div>
		{/if}
	</div>

	{#if error}
		<div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded relative mb-6">
			<strong class="font-bold">Error:</strong>
			<span class="block sm:inline">{error}</span>
		</div>
	{/if}

	{#if loading}
		<div class="flex justify-center items-center h-64">
			<div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
		</div>
	{:else if job}
		<div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
			<!-- Job Details -->
			<div class="lg:col-span-2">
				<JobDetails 
					{job} 
					{isRecruiter} 
					{statusOptions} 
					{remoteOptions} 
					onStatusChange={updateJobStatus} 
				/>
				
				<ApplicationsList 
					{applications} 
					{applicationsCount} 
					{jobId} 
				/>
			</div>

			<!-- Sidebar -->
			<div class="lg:col-span-1">
				<!-- Job Metrics -->
				<JobMetrics 
					viewCount={job.view_count || 0} 
					{applicationsCount} 
					createdAt={job.created_at} 
				/>

				<!-- Job Info -->
				<JobInfo 
					{jobId} 
					createdBy={job.created_by} 
					createdByName={createdByUser ? `${createdByUser.first_name} ${createdByUser.last_name}` : ''} 
					createdAt={job.created_at} 
					updatedAt={job.updated_at} 
				/>

				<!-- Quick Actions -->
				<JobQuickActions 
					{jobId} 
					{isRecruiter} 
				/>
			</div>
		</div>
	{:else}
		<div class="bg-white rounded-lg shadow p-6 text-center">
			<p class="text-gray-500">Job not found or you don't have permission to view it.</p>
			<a
				href="/jobs"
				class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
			>
				Back to Jobs
			</a>
		</div>
	{/if}
</div>
