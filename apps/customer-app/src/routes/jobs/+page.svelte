<!-- Jobs Listing Page - ProcureServe II -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { supabase } from '$lib/supabase';
	import { formatDate } from '@procureserve/shared-utils';
	import type { Job } from '@procureserve/shared-types';

	// Component state
	let loading = true;
	let error: string | null = null;
	let jobs: Job[] = [];
	let totalJobs = 0;
	let pageSize = 10;
	let currentPage = 1;
	let filters = {
		status: '',
		search: ''
	};

	// Derived properties
	$: companyId = $page.data.customerUser?.company_id;
	$: isRecruiter = $page.data.customerUser?.role === 'recruiter' || $page.data.customerUser?.role === 'admin';
	$: statusOptions = getEnumValues('job_statuses');
	$: totalPages = Math.ceil(totalJobs / pageSize);
	$: paginationArray = Array.from({ length: totalPages }, (_, i) => i + 1);

	// Get configurable enum values
	function getEnumValues(category: string): { key: string; label: string; color: string }[] {
		const enums = $page.data.configurableEnums || {};
		return enums[category] || [];
	}

	// Get status color from configurable enums
	function getStatusColor(status: string): string {
		const statusOption = statusOptions.find(opt => opt.key === status);
		return statusOption?.color || '#6B7280'; // Default gray if not found
	}

	// Load jobs with filters and pagination
	async function loadJobs() {
		if (!companyId) {
			error = 'No company ID found. Please contact support.';
			loading = false;
			return;
		}

		loading = true;
		error = null;

		try {
			// Build query
			let query = supabase
				.from('jobs')
				.select('*', { count: 'exact' })
				.eq('company_id', companyId)
				.order('created_at', { ascending: false })
				.range((currentPage - 1) * pageSize, currentPage * pageSize - 1);

			// Apply filters
			if (filters.status) {
				query = query.eq('status', filters.status);
			}

			if (filters.search) {
				query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
			}

			// Execute query
			const { data, count, error: queryError } = await query;

			if (queryError) throw queryError;
			
			jobs = data || [];
			totalJobs = count || 0;
		} catch (err: any) {
			console.error('Error loading jobs:', err);
			error = 'Failed to load jobs. Please try again later.';
		} finally {
			loading = false;
		}
	}

	// Apply filters and reset to first page
	function applyFilters() {
		currentPage = 1;
		loadJobs();
	}

	// Reset filters
	function resetFilters() {
		filters = {
			status: '',
			search: ''
		};
		currentPage = 1;
		loadJobs();
	}

	// Go to specific page
	function goToPage(page: number) {
		if (page < 1 || page > totalPages) return;
		currentPage = page;
		loadJobs();
	}

	// Initial load
	onMount(() => {
		loadJobs();
	});
</script>

<div class="p-4 sm:p-6">
	<div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
		<h1 class="text-2xl font-bold">Jobs</h1>
		{#if isRecruiter}
			<a
				href="/jobs/create"
				class="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="h-5 w-5 mr-2"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M12 4v16m8-8H4"
					/>
				</svg>
				Create Job
			</a>
		{/if}
	</div>

	<!-- Filters -->
	<div class="bg-white p-4 rounded-lg shadow mb-6">
		<div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
			<div class="flex-1">
				<label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
				<input
					type="text"
					id="search"
					bind:value={filters.search}
					placeholder="Search jobs..."
					class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
				/>
			</div>
			<div class="w-full sm:w-48">
				<label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
				<select
					id="status"
					bind:value={filters.status}
					class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
				>
					<option value="">All Statuses</option>
					{#each statusOptions as status}
						<option value={status.key}>{status.label}</option>
					{/each}
				</select>
			</div>
			<div class="flex items-end space-x-2">
				<button
					type="button"
					on:click={applyFilters}
					class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
				>
					Apply Filters
				</button>
				<button
					type="button"
					on:click={resetFilters}
					class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
				>
					Reset
				</button>
			</div>
		</div>
	</div>

	{#if loading && jobs.length === 0}
		<div class="flex justify-center items-center h-64">
			<div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
		</div>
	{:else if error}
		<div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded relative mb-6">
			<strong class="font-bold">Error:</strong>
			<span class="block sm:inline">{error}</span>
		</div>
	{:else if jobs.length === 0}
		<div class="bg-white rounded-lg shadow p-6 text-center">
			<p class="text-gray-500 mb-4">No jobs found matching your criteria.</p>
			{#if isRecruiter}
				<a
					href="/jobs/create"
					class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
				>
					Create Your First Job
				</a>
			{/if}
		</div>
	{:else}
		<!-- Jobs List -->
		<div class="bg-white rounded-lg shadow overflow-hidden">
			<div class="overflow-x-auto">
				<table class="min-w-full divide-y divide-gray-200">
					<thead class="bg-gray-50">
						<tr>
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
							>
								Job Title
							</th>
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
							>
								Status
							</th>
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
							>
								Created Date
							</th>
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
							>
								Applications
							</th>
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
							>
								Actions
							</th>
						</tr>
					</thead>
					<tbody class="bg-white divide-y divide-gray-200">
						{#each jobs as job (job.id)}
							<tr class="hover:bg-gray-50">
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="text-sm font-medium text-gray-900">{job.title}</div>
									<div class="text-sm text-gray-500 truncate max-w-xs">
										{job.description?.substring(0, 100) || 'No description'}
										{job.description?.length > 100 ? '...' : ''}
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<span
										class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
										style="background-color: {getStatusColor(job.status)}25; color: {getStatusColor(job.status)};"
									>
										{statusOptions.find(s => s.key === job.status)?.label || job.status}
									</span>
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
									{formatDate(job.created_at)}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
									{job.application_count || 0}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
									<div class="flex space-x-3">
										<a href={`/jobs/${job.id}`} class="text-indigo-600 hover:text-indigo-900">
											View
										</a>
										{#if isRecruiter}
											<a href={`/jobs/${job.id}/edit`} class="text-blue-600 hover:text-blue-900">
												Edit
											</a>
										{/if}
									</div>
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>

			<!-- Pagination -->
			{#if totalPages > 1}
				<div class="px-6 py-3 flex items-center justify-between border-t border-gray-200">
					<div class="flex-1 flex justify-between sm:hidden">
						<button
							on:click={() => goToPage(currentPage - 1)}
							disabled={currentPage === 1}
							class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							Previous
						</button>
						<button
							on:click={() => goToPage(currentPage + 1)}
							disabled={currentPage === totalPages}
							class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							Next
						</button>
					</div>
					<div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
						<div>
							<p class="text-sm text-gray-700">
								Showing <span class="font-medium">{(currentPage - 1) * pageSize + 1}</span> to
								<span class="font-medium">
									{Math.min(currentPage * pageSize, totalJobs)}
								</span> of <span class="font-medium">{totalJobs}</span> results
							</p>
						</div>
						<div>
							<nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
								<button
									on:click={() => goToPage(currentPage - 1)}
									disabled={currentPage === 1}
									class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
								>
									<span class="sr-only">Previous</span>
									<svg
										class="h-5 w-5"
										xmlns="http://www.w3.org/2000/svg"
										viewBox="0 0 20 20"
										fill="currentColor"
										aria-hidden="true"
									>
										<path
											fill-rule="evenodd"
											d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
											clip-rule="evenodd"
										/>
									</svg>
								</button>
								{#each paginationArray as page}
									{#if totalPages <= 7 || page === 1 || page === totalPages || (page >= currentPage - 1 && page <= currentPage + 1)}
										<button
											on:click={() => goToPage(page)}
											class={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
												currentPage === page
													? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
													: 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
											}`}
										>
											{page}
										</button>
									{:else if page === currentPage - 2 || page === currentPage + 2}
										<span
											class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
										>
											...
										</span>
									{/if}
								{/each}
								<button
									on:click={() => goToPage(currentPage + 1)}
									disabled={currentPage === totalPages}
									class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
								>
									<span class="sr-only">Next</span>
									<svg
										class="h-5 w-5"
										xmlns="http://www.w3.org/2000/svg"
										viewBox="0 0 20 20"
										fill="currentColor"
										aria-hidden="true"
									>
										<path
											fill-rule="evenodd"
											d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
											clip-rule="evenodd"
										/>
									</svg>
								</button>
							</nav>
						</div>
					</div>
				</div>
			{/if}
		</div>
	{/if}
</div>
