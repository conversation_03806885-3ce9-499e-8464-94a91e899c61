// Job creation page server load function
import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, url }) => {
	// Validate session and user data
	const { user, customerUser, supabase } = locals;

	// Security check - if no valid user or customer user, redirect to login
	if (!user || !customerUser) {
		throw redirect(303, '/login');
	}

	// Check if user has the recruitment process permission
	const processPermissions = customerUser?.profile?.process_permissions || customerUser?.process_permissions || [];
	if (!processPermissions.includes('recruitment')) {
		throw redirect(303, '/access-denied?reason=no_recruitment_access');
	}

	// Check if user has the recruiter or admin role
	if (!['recruiter', 'admin'].includes(customerUser.role)) {
		throw redirect(303, '/access-denied?reason=insufficient_role&required=recruiter');
	}

	// Get configurable enums for the company
	const { data: enums } = await supabase
		.from('configurable_enums')
		.select('*')
		.eq('company_id', customerUser.company_id);
	
	// Format enums into a more usable structure
	const configurableEnums = {};
	if (enums) {
		enums.forEach((item) => {
			configurableEnums[item.category] = item.values;
		});
	}

	// Return the data needed for the page
	return {
		user,
		customerUser,
		configurableEnums,
		activeProcess: 'recruitment'
	};
};
