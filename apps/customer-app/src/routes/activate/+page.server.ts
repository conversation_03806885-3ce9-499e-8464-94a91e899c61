import type { PageServerLoad, Actions } from './$types'
import { redirect, fail } from '@sveltejs/kit'
import { createClient } from '@supabase/supabase-js'
import { PUBLIC_SUPABASE_URL } from '$env/static/public'
import { getService<PERSON><PERSON><PERSON>ey } from '$lib/env-config'
import { createHash } from 'crypto'

function createSupabaseAdminClient() {
  const serviceRoleKey = getServiceRoleKey()
  return createClient(PUBLIC_SUPABASE_URL, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

export const load: PageServerLoad = async ({ url }) => {
  const token = url.searchParams.get('token')
  
  if (!token) {
    return {
      error: 'Missing activation token',
      registration: null
    }
  }

  // Basic token format validation  
  if (token.length !== 64) { // 32 bytes hex = 64 characters
    return {
      error: 'Invalid activation token format',
      registration: null
    }
  }

  console.log('[ACTIVATE] Processing activation token:', token.substring(0, 8) + '...')

  try {
    const adminClient = createSupabaseAdminClient()
    
    // Hash the token to match stored hash
    const hashedToken = createHash('sha256').update(token).digest('hex')
    
    // Find the activation token in database
    const { data: activationToken, error: tokenError } = await adminClient
      .from('activation_tokens')
      .select(`
        *,
        companies:company_id (
          id,
          name,
          domain
        ),
        business_registrations:registration_id (
          id,
          company_name,
          contact_person_name,
          contact_person_email
        )
      `)
      .eq('token_hash', hashedToken)
      .eq('used', false)
      .gt('expires_at', new Date().toISOString())
      .single()

    if (tokenError || !activationToken) {
      console.error('[ACTIVATE] Token not found or expired:', tokenError)
      return {
        error: 'Activation token is invalid or has expired',
        registration: null
      }
    }

    console.log('[ACTIVATE] Valid token found for:', activationToken.contact_email)

    return {
      error: null,
      registration: {
        id: activationToken.registration_id,
        company_name: activationToken.business_registrations?.company_name || activationToken.companies?.name,
        contact_name: activationToken.contact_name,
        contact_email: activationToken.contact_email,
        company_id: activationToken.company_id,
        token_id: activationToken.id,
        process_permissions: activationToken.process_permissions
      }
    }

  } catch (error) {
    console.error('[ACTIVATE] Error validating token:', error)
    return {
      error: 'Failed to validate activation token',
      registration: null
    }
  }
}

export const actions: Actions = {
  activate: async ({ request, url }) => {
    const token = url.searchParams.get('token')
    const formData = await request.formData()
    const password = formData.get('password') as string
    const confirmPassword = formData.get('confirmPassword') as string

    if (!token) {
      return fail(400, { error: 'Missing activation token' })
    }

    if (!password || password.length < 8) {
      return fail(400, { error: 'Password must be at least 8 characters long' })
    }

    if (password !== confirmPassword) {
      return fail(400, { error: 'Passwords do not match' })
    }

    console.log('[ACTIVATE] Starting account activation process...')

    try {
      const adminClient = createSupabaseAdminClient()
      
      // Hash token and find activation record
      const hashedToken = createHash('sha256').update(token).digest('hex')
      
      const { data: activationToken, error: tokenError } = await adminClient
        .from('activation_tokens')
        .select('*')
        .eq('token_hash', hashedToken)
        .eq('used', false)
        .gt('expires_at', new Date().toISOString())
        .single()

      if (tokenError || !activationToken) {
        console.error('[ACTIVATE] Token validation failed:', tokenError)
        return fail(400, { error: 'Activation token is invalid or has expired' })
      }

      console.log('[ACTIVATE] Creating Supabase Auth user...')

      // Create user in Supabase Auth
      const { data: authUser, error: authError } = await adminClient.auth.admin.createUser({
        email: activationToken.contact_email,
        password: password,
        email_confirm: true, // Skip email verification since we already verified via token
        user_metadata: {
          name: activationToken.contact_name,
          company_id: activationToken.company_id,
          process_permissions: activationToken.process_permissions
        }
      })

      if (authError || !authUser.user) {
        console.error('[ACTIVATE] Auth user creation failed:', authError)
        return fail(500, { error: 'Failed to create user account' })
      }

      console.log('[ACTIVATE] Auth user created:', authUser.user.id)

      // Create user record in our users table
      const { error: userError } = await adminClient
        .from('users')
        .insert({
          id: authUser.user.id,
          email: activationToken.contact_email,
          name: activationToken.contact_name,
          company_id: activationToken.company_id,
          role: 'admin', // First user becomes admin
          process_permissions: activationToken.process_permissions,
          email_verified: true,
          profile: {
            activation_date: new Date().toISOString(),
            activation_token_id: activationToken.id
          }
        })

      if (userError) {
        console.error('[ACTIVATE] User record creation failed:', userError)
        // Try to clean up auth user if possible
        await adminClient.auth.admin.deleteUser(authUser.user.id)
        return fail(500, { error: 'Failed to create user profile' })
      }

      console.log('[ACTIVATE] User profile created successfully')

      // Mark activation token as used
      const { error: tokenUpdateError } = await adminClient
        .from('activation_tokens')
        .update({
          used: true,
          used_at: new Date().toISOString(),
          activated_user_id: authUser.user.id
        })
        .eq('id', activationToken.id)

      if (tokenUpdateError) {
        console.error('[ACTIVATE] Failed to mark token as used:', tokenUpdateError)
        // This is not critical, continue with activation
      }

      console.log('[ACTIVATE] ✅ Account activation completed successfully!')

      // Redirect to login page with success message
      throw redirect(302, `/login?activated=true&email=${encodeURIComponent(activationToken.contact_email)}`)

    } catch (error) {
      if (error.status === 302) {
        // This is our redirect, re-throw it
        throw error
      }
      
      console.error('[ACTIVATE] Activation process failed:', error)
      return fail(500, { error: 'Account activation failed. Please try again.' })
    }
  }
}
