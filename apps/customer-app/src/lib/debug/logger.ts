// Enhanced debugging utilities for registration flow
export class RegistrationLogger {
  private static enabled = true // Set to false in production

  static log(step: string, data?: any, error?: any) {
    if (!this.enabled) return
    
    const timestamp = new Date().toISOString()
    const logData = {
      timestamp,
      step,
      data: data ? JSON.stringify(data, null, 2) : undefined,
      error: error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : undefined
    }
    
    console.log(`[REGISTRATION ${step}]`, logData)
    
    // Store in memory for debugging (last 100 entries)
    if (typeof globalThis !== 'undefined') {
      if (!globalThis.registrationLogs) {
        globalThis.registrationLogs = []
      }
      globalThis.registrationLogs.push(logData)
      if (globalThis.registrationLogs.length > 100) {
        globalThis.registrationLogs.shift()
      }
    }
  }

  static error(step: string, error: any, data?: any) {
    this.log(step, data, error)
  }

  static getLogs() {
    return typeof globalThis !== 'undefined' ? globalThis.registrationLogs || [] : []
  }
}

export function validateEnvironment() {
  const required = [
    'PUBLIC_SUPABASE_URL',
    'PUBLIC_SUPABASE_ANON_KEY', 
    'SUPABASE_SERVICE_ROLE_KEY'
  ]
  
  const missing = required.filter(key => {
    const value = process.env[key] || (globalThis as any)?.[key]
    return !value
  })
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
  
  return true
}
