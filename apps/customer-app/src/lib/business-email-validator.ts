// Business email validation to prevent personal email domains

const PERSONAL_EMAIL_DOMAINS = [
  // Major personal email providers
  'gmail.com',
  'yahoo.com',
  'hotmail.com',
  'outlook.com',
  'live.com',
  'msn.com',
  'aol.com',
  'icloud.com',
  'me.com',
  'mac.com',
  
  // International personal email providers
  'yandex.com',
  'mail.ru',
  'qq.com',
  '163.com',
  '126.com',
  'sina.com',
  'sohu.com',
  'rediffmail.com',
  'yahoo.co.uk',
  'yahoo.ca',
  'yahoo.com.au',
  'gmx.com',
  'gmx.de',
  'web.de',
  't-online.de',
  
  // Temporary/disposable email providers
  '10minutemail.com',
  'tempmail.org',
  'guerrillamail.com',
  'mailinator.com',
  'throwaway.email',
  'temp-mail.org'
]

export interface EmailValidationResult {
  isValid: boolean
  isBusinessEmail: boolean
  domain: string
  reason?: string
}

export function validateBusinessEmail(email: string): EmailValidationResult {
  // Basic email format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    return {
      isValid: false,
      isBusinessEmail: false,
      domain: '',
      reason: 'Invalid email format'
    }
  }

  const domain = email.split('@')[1].toLowerCase()
  
  // Check if it's a personal email domain
  if (PERSONAL_EMAIL_DOMAINS.includes(domain)) {
    return {
      isValid: true,
      isBusinessEmail: false,
      domain,
      reason: 'Personal email domains are not allowed for business registration'
    }
  }

  // Additional checks for suspicious patterns
  if (domain.includes('temp') || domain.includes('disposable') || domain.includes('fake')) {
    return {
      isValid: true,
      isBusinessEmail: false,
      domain,
      reason: 'Temporary or disposable email addresses are not allowed'
    }
  }

  // If it passes all checks, it's likely a business email
  return {
    isValid: true,
    isBusinessEmail: true,
    domain
  }
}

export function getBusinessEmailValidationMessage(result: EmailValidationResult): string {
  if (!result.isValid) {
    return result.reason || 'Invalid email address'
  }
  
  if (!result.isBusinessEmail) {
    return result.reason || 'Please use a business email address'
  }
  
  return ''
}

// Helper function for form validation
export function isValidBusinessEmail(email: string): boolean {
  const result = validateBusinessEmail(email)
  return result.isValid && result.isBusinessEmail
} 