// Notification Service Core - Local Copy
// Handles creating, managing, and delivering notifications

import type { SupabaseClient } from '@supabase/supabase-js'

// Local types (simplified for now)
export interface CreateNotificationRequest {
  user_id: string
  type_key: string
  title: string
  message: string
  action_url?: string
  action_label?: string
  entity_type?: string
  entity_id?: string
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  metadata?: Record<string, any>
  send_email?: boolean
  send_push?: boolean
}

export interface NotificationType {
  id: string
  company_id: string
  type_key: string
  display_name: string
  description: string
  category: 'user_management' | 'jobs' | 'applications' | 'interviews' | 'system'
  default_enabled: boolean
  supports_email: boolean
  supports_in_app: boolean
  supports_push: boolean
  icon_name?: string
  color: string
  created_at: string
  updated_at: string
}

export interface UserNotificationPreference {
  id: string
  user_id: string
  company_id: string
  notification_type_id: string
  email_enabled: boolean
  in_app_enabled: boolean
  push_enabled: boolean
  quiet_hours_start?: string
  quiet_hours_end?: string
  created_at: string
  updated_at: string
  notification_type?: NotificationType
}

export interface Notification {
  id: string
  company_id: string
  user_id: string
  notification_type_id: string
  title: string
  message: string
  action_url?: string
  action_label?: string
  is_read: boolean
  read_at?: string
  is_dismissed: boolean
  dismissed_at?: string
  entity_type?: string
  entity_id?: string
  email_sent: boolean
  email_sent_at?: string
  push_sent: boolean
  push_sent_at?: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  expires_at?: string
  metadata: Record<string, any>
  created_at: string
  updated_at: string
  notification_type?: any
}

export interface NotificationFilters {
  is_read?: boolean
  category?: string[]
  priority?: ('low' | 'normal' | 'high' | 'urgent')[]
  entity_type?: string
  start_date?: string
  end_date?: string
  limit?: number
  offset?: number
}

export type NotificationEvent = {
  type: 'notification_created'
  payload: Notification
} | {
  type: 'notification_updated'
  payload: Notification
} | {
  type: 'notification_deleted'
  payload: { id: string }
} | {
  type: 'notifications_read'
  payload: { user_id: string; count: number }
}

export class NotificationService {
  private supabase: SupabaseClient
  private realtimeChannel: any

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase
  }

  // Create a new notification
  async createNotification(request: CreateNotificationRequest): Promise<{ data: Notification | null; error: string | null }> {
    try {
      // Get user's company_id
      const { data: user } = await this.supabase
        .from('users')
        .select('company_id')
        .eq('id', request.user_id)
        .single()

      if (!user) {
        return { data: null, error: 'User not found' }
      }

      // Get notification type
      const { data: notificationType } = await this.supabase
        .from('notification_types')
        .select('*')
        .eq('company_id', user.company_id)
        .eq('type_key', request.type_key)
        .single()

      if (!notificationType) {
        return { data: null, error: 'Notification type not found' }
      }

      // Create notification
      const { data: notification, error } = await this.supabase
        .from('notifications')
        .insert({
          company_id: user.company_id,
          user_id: request.user_id,
          notification_type_id: notificationType.id,
          title: request.title,
          message: request.message,
          action_url: request.action_url,
          action_label: request.action_label,
          entity_type: request.entity_type,
          entity_id: request.entity_id,
          priority: request.priority || 'normal',
          metadata: request.metadata || {}
        })
        .select('*, notification_type(*)')
        .single()

      if (error) {
        return { data: null, error: error.message }
      }

      // Send real-time update
      this.broadcastNotification('notification_created', notification)

      return { data: notification, error: null }
    } catch (err) {
      return { data: null, error: err instanceof Error ? err.message : 'Unknown error' }
    }
  }

  // Get notifications for a user
  async getUserNotifications(
    userId: string, 
    filters: NotificationFilters = {}
  ): Promise<{ data: Notification[]; error: string | null }> {
    try {
      let query = this.supabase
        .from('notifications')
        .select('*, notification_type(*)')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (filters.is_read !== undefined) {
        query = query.eq('is_read', filters.is_read)
      }

      if (filters.limit) {
        query = query.limit(filters.limit)
      }

      const { data, error } = await query

      if (error) {
        return { data: [], error: error.message }
      }

      return { data: data || [], error: null }
    } catch (err) {
      return { data: [], error: err instanceof Error ? err.message : 'Unknown error' }
    }
  }

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<{ success: boolean; error: string | null }> {
    try {
      const { error } = await this.supabase.rpc('mark_notification_read', {
        notification_id: notificationId
      })

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, error: null }
    } catch (err) {
      return { success: false, error: err instanceof Error ? err.message : 'Unknown error' }
    }
  }

  // Mark all notifications as read for user
  async markAllAsRead(userId?: string): Promise<{ success: boolean; count: number; error: string | null }> {
    try {
      const { data: count, error } = await this.supabase.rpc('mark_all_notifications_read', {
        user_uuid: userId
      })

      if (error) {
        return { success: false, count: 0, error: error.message }
      }

      return { success: true, count: count || 0, error: null }
    } catch (err) {
      return { success: false, count: 0, error: err instanceof Error ? err.message : 'Unknown error' }
    }
  }

  // Get unread count for user
  async getUnreadCount(userId: string): Promise<{ count: number; error: string | null }> {
    try {
      const { count, error } = await this.supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('is_read', false)

      if (error) {
        return { count: 0, error: error.message }
      }

      return { count: count || 0, error: null }
    } catch (err) {
      return { count: 0, error: err instanceof Error ? err.message : 'Unknown error' }
    }
  }

  // Subscribe to real-time notifications
  subscribeToNotifications(userId: string, callback: (event: NotificationEvent) => void) {
    this.realtimeChannel = this.supabase
      .channel(`notifications:${userId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          callback({
            type: 'notification_created',
            payload: payload.new as Notification
          })
        }
      )
      .subscribe()

    return this.realtimeChannel
  }

  // Unsubscribe from real-time notifications
  unsubscribeFromNotifications() {
    if (this.realtimeChannel) {
      this.supabase.removeChannel(this.realtimeChannel)
      this.realtimeChannel = null
    }
  }

  private broadcastNotification(type: string, payload: any) {
    // Broadcast to real-time subscribers
    if (this.realtimeChannel) {
      this.realtimeChannel.send({
        type: 'broadcast',
        event: type,
        payload
      })
    }
  }
}

// Helper class for common notifications
export class NotificationHelpers {
  private service: NotificationService

  constructor(service: NotificationService) {
    this.service = service
  }

  async notifyUserInvited(adminUserId: string, inviterName: string, invitedEmail: string, role: string) {
    return this.service.createNotification({
      user_id: adminUserId,
      type_key: 'user_invited',
      title: 'User Invitation Sent',
      message: `${inviterName} invited ${invitedEmail} as ${role}`,
      action_url: '/settings/users',
      action_label: 'View Users',
      send_email: true
    })
  }
}

// Factory function
export function createNotificationService(supabase: SupabaseClient) {
  const service = new NotificationService(supabase)
  const helpers = new NotificationHelpers(service)
  
  return {
    service,
    helpers
  }
}
