export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          role: string
          created_at: string
          updated_at: string | null
          last_sign_in_at: string | null
          is_active: boolean
          process_permissions: string[]
          current_process: string | null
        }
        Insert: {
          id: string
          email: string
          role?: string
          created_at?: string
          updated_at?: string | null
          last_sign_in_at?: string | null
          is_active?: boolean
          process_permissions?: string[]
          current_process?: string | null
        }
        Update: {
          id?: string
          email?: string
          role?: string
          created_at?: string
          updated_at?: string | null
          last_sign_in_at?: string | null
          is_active?: boolean
          process_permissions?: string[]
          current_process?: string | null
        }
      }
      profiles: {
        Row: {
          id: string
          first_name: string | null
          last_name: string | null
          avatar_url: string | null
          company_id: string | null
          user_id: string
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          company_id?: string | null
          user_id: string
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          company_id?: string | null
          user_id?: string
          created_at?: string
          updated_at?: string | null
        }
      }
      // Add other tables as needed
    }
    Views: {
      // Add views if any
    }
    Functions: {
      // Add functions if any
      [_ in never]: never
    }
    Enums: {
      // Add enums if any
      [_ in never]: never
    }
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T]
