// Business registration email notifications
import { createEmailService, renderEmailTemplate } from '@psii/email-service'
import { createSupabaseServiceClient } from './supabase-service'
import type { BusinessRegistrationEmailData, BusinessApprovalEmailData, BusinessRejectionEmailData } from '@psii/email-service'

export class BusinessRegistrationEmailService {
  private emailService
  private supabaseClient

  constructor() {
    this.supabaseClient = createSupabaseServiceClient()
    this.emailService = createEmailService(this.supabaseClient)
  }

  // Expose emailService for direct access
  get emailServiceInstance() {
    return this.emailService
  }

  async sendRegistrationSubmittedEmail(data: BusinessRegistrationEmailData): Promise<void> {
    try {
      const emailContent = renderEmailTemplate('registration_submitted', {
        company_name: data.company_name,
        contact_name: data.contact_name,
        contact_email: data.contact_email,
        submission_date: data.submission_date,
        registration_id: data.registration_id
      })

      await this.emailService.sendEmail({
        to: data.contact_email,
        template_type: 'notification',
        company_id: 'system', // System email, no specific company
        variables: {
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text,
          company_name: data.company_name,
          contact_name: data.contact_name,
          registration_id: data.registration_id,
          submission_date: data.submission_date
        }
      })

      console.log(`[EMAIL] Registration submitted email sent to ${data.contact_email}`)
    } catch (error) {
      console.error('[EMAIL] Failed to send registration submitted email:', error)
      // Don't throw error - email failure shouldn't break registration
    }
  }

  async sendRegistrationApprovedEmail(data: BusinessApprovalEmailData): Promise<void> {
    try {
      const emailContent = renderEmailTemplate('registration_approved', {
        company_name: data.company_name,
        contact_name: data.contact_name,
        contact_email: data.contact_email,
        submission_date: data.submission_date,
        registration_id: data.registration_id,
        activation_link: data.activation_link,
        company_id: data.company_id
      })

      await this.emailService.sendEmail({
        to: data.contact_email,
        template_type: 'user_invitation',
        company_id: data.company_id,
        variables: {
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text,
          company_name: data.company_name,
          contact_name: data.contact_name,
          activation_link: data.activation_link
        }
      })

      console.log(`[EMAIL] Registration approved email sent to ${data.contact_email}`)
    } catch (error) {
      console.error('[EMAIL] Failed to send registration approved email:', error)
      throw error // Approval email is critical
    }
  }

  async sendRegistrationRejectedEmail(data: BusinessRejectionEmailData): Promise<void> {
    try {
      const emailContent = renderEmailTemplate('registration_rejected', {
        company_name: data.company_name,
        contact_name: data.contact_name,
        contact_email: data.contact_email,
        submission_date: data.submission_date,
        registration_id: data.registration_id,
        rejection_reason: data.rejection_reason,
        support_email: data.support_email
      })

      await this.emailService.sendEmail({
        to: data.contact_email,
        template_type: 'notification',
        company_id: 'system', // System email, no specific company  
        variables: {
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text,
          company_name: data.company_name,
          contact_name: data.contact_name,
          rejection_reason: data.rejection_reason,
          support_email: data.support_email
        }
      })

      console.log(`[EMAIL] Registration rejected email sent to ${data.contact_email}`)
    } catch (error) {
      console.error('[EMAIL] Failed to send registration rejected email:', error)
      // Don't throw error - email failure shouldn't break rejection process
    }
  }
}

// Singleton instance
export const businessRegistrationEmails = new BusinessRegistrationEmailService() 