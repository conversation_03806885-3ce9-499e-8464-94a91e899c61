// Centralized authentication utilities for ProcureServe II
// Implements security best practices and consistent auth patterns

import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '$lib/types/supabase'
import { error, redirect } from '@sveltejs/kit'

export interface AuthUser {
  id: string
  email: string
  company_id: string
  role: string
  profile: any
  process_permissions: string[]
  current_process: string | null
  created_at: string
  updated_at: string
}

export interface AuthValidationResult {
  user: AuthUser
  isValid: boolean
  redirectTo?: string
  error?: string
}

/**
 * SECURITY BEST PRACTICE: Server-side user validation
 * Uses getUser() instead of getSession() for secure server-side validation
 */
export async function validateServerAuth(
  supabase: SupabaseClient<Database>
): Promise<{ user: any; error?: string }> {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError) {
      console.error('Auth validation error:', userError.message)
      return { user: null, error: userError.message }
    }
    
    return { user, error: undefined }
  } catch (err) {
    console.error('Auth validation exception:', err)
    return { user: null, error: 'Authentication failed' }
  }
}

/**
 * SECURITY BEST PRACTICE: Comprehensive user profile validation
 * Ensures user exists in users table with proper permissions
 */
export async function getUserProfile(
  supabase: SupabaseClient<Database>,
  userId: string
): Promise<AuthValidationResult> {
  try {
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        company_id,
        role,
        profile,
        process_permissions,
        current_process,
        created_at,
        updated_at
      `)
      .eq('id', userId)
      .single()
    
    if (userError) {
      console.error('User profile lookup error:', userError.message)
      return {
        user: null as any,
        isValid: false,
        error: 'User profile not found'
      }
    }
    
    if (!userData) {
      console.error('User not found in users table:', userId)
      return {
        user: null as any,
        isValid: false,
        error: 'User profile not found'
      }
    }
    
    // SECURITY FIX: Normalize permissions access pattern
    const normalizedUser: AuthUser = {
      ...userData,
      process_permissions: userData.profile?.process_permissions || userData.process_permissions || []
    }
    
    return {
      user: normalizedUser,
      isValid: true
    }
  } catch (err) {
    console.error('User profile validation exception:', err)
    return {
      user: null as any,
      isValid: false,
      error: 'Profile validation failed'
    }
  }
}

/**
 * SECURITY BEST PRACTICE: Process permission validation
 * Validates user access to specific business processes
 */
export function validateProcessPermission(
  user: AuthUser,
  processName: string
): { isValid: boolean; error?: string } {
  // Validate process name
  if (!['recruitment', 'bench-sales'].includes(processName)) {
    return {
      isValid: false,
      error: `Invalid process name: ${processName}`
    }
  }
  
  // Convert kebab-case to snake_case for permission check
  const permissionName = processName.replace('-', '_')
  
  if (!user.process_permissions.includes(permissionName)) {
    return {
      isValid: false,
      error: `User lacks permission for process: ${permissionName}`
    }
  }
  
  return { isValid: true }
}

/**
 * SECURITY BEST PRACTICE: Route-based access control
 * Determines appropriate redirect based on user permissions and current state
 */
export function determineUserRedirect(user: AuthUser, requestedPath: string): string | null {
  const permissions = user.process_permissions
  
  // No permissions - access denied
  if (permissions.length === 0) {
    return '/access-denied'
  }
  
  // Multiple processes but no current process - need selection
  if (permissions.length > 1 && !user.current_process) {
    return '/select-process'
  }
  
  // Single process - direct to dashboard
  if (permissions.length === 1 && !user.current_process) {
    const process = permissions[0].replace('_', '-') // Convert to kebab-case
    return `/${process}/dashboard`
  }
  
  // User has current process - validate access to requested path
  if (user.current_process && requestedPath.startsWith('/')) {
    const pathSegments = requestedPath.split('/')
    const requestedProcess = pathSegments[1]
    
    if (['recruitment', 'bench-sales'].includes(requestedProcess)) {
      const permissionName = requestedProcess.replace('-', '_')
      if (!permissions.includes(permissionName)) {
        return '/access-denied'
      }
    }
  }
  
  return null // No redirect needed
}

/**
 * SECURITY BEST PRACTICE: Secure session cleanup
 * Properly clears authentication state on logout
 */
export async function secureLogout(supabase: SupabaseClient<Database>): Promise<void> {
  try {
    await supabase.auth.signOut()
  } catch (err) {
    console.error('Logout error:', err)
    // Continue with logout even if there's an error
  }
}

/**
 * SECURITY BEST PRACTICE: Auth error handling
 * Provides consistent error handling for authentication failures
 */
export function handleAuthError(err: any, context: string): never {
  console.error(`Auth error in ${context}:`, err)
  
  if (err instanceof Error) {
    if (err.message.includes('JWT expired') || err.message.includes('Invalid token')) {
      throw redirect(303, '/login?session=expired')
    }
  }
  
  throw error(500, 'Authentication error')
}

/**
 * UTILITY: Check if route requires authentication
 */
export function isProtectedRoute(pathname: string): boolean {
  const publicRoutes = [
    '/',
    '/about',
    '/contact',
    '/privacy',
    '/terms',
    '/login',
    '/register',
    '/reset-password',
    '/forgot-password',
    '/auth/confirm',
    '/auth/callback'
  ]
  
  const isApiRoute = pathname.startsWith('/api/')
  const isPublicRoute = publicRoutes.includes(pathname)
  
  return !isApiRoute && !isPublicRoute
}

/**
 * UTILITY: Extract process name from route
 */
export function extractProcessFromRoute(pathname: string): string | null {
  const segments = pathname.split('/')
  const processSegment = segments[1]
  
  if (['recruitment', 'bench-sales'].includes(processSegment)) {
    return processSegment
  }
  
  return null
}
