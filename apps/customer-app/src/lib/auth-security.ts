// Session cleanup utility for customer app security
import { supabase } from '$lib/supabase'

/**
 * Clears all customer app sessions and local storage
 * Should be called when switching between apps or on security violations
 */
export async function clearCustomerSession() {
  try {
    // Sign out from Supabase
    await supabase.auth.signOut()
    
    // Clear app-specific localStorage items
    if (typeof window !== 'undefined') {
      const keysToRemove = []
      
      // Find all customer app specific keys
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith('psii-customer.')) {
          keysToRemove.push(key)
        }
      }
      
      // Remove all customer app keys
      keysToRemove.forEach(key => localStorage.removeItem(key))
      
      // Also clear any generic Supabase keys that might cause conflicts
      const genericKeys = ['sb-localhost-auth-token', 'supabase.auth.token']
      genericKeys.forEach(key => localStorage.removeItem(key))
    }
    
    console.log('[SECURITY] Customer app session cleared successfully')
    return true
  } catch (error) {
    console.error('[SECURITY] Error clearing customer session:', error)
    return false
  }
}

/**
 * Validates if current session belongs to a customer app user
 */
export async function validateCustomerSession() {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return { valid: false, reason: 'No authenticated user' }
    }
    
    // Check if user exists in customer app users table
    const { data: customerUser, error } = await supabase
      .from('users')
      .select('id, role, company_id')
      .eq('id', user.id)
      .single()
    
    if (error || !customerUser) {
      return { valid: false, reason: 'User not found in customer app' }
    }
    
    if (!customerUser.company_id || !customerUser.role) {
      return { valid: false, reason: 'Invalid business account' }
    }
    
    return { valid: true, user: customerUser }
  } catch (error) {
    console.error('[SECURITY] Session validation error:', error)
    return { valid: false, reason: 'Validation error' }
  }
}

/**
 * Secure logout that clears session and redirects
 */
export async function secureLogout() {
  await clearCustomerSession()
  
  // Redirect to login with clear cache
  if (typeof window !== 'undefined') {
    window.location.href = '/login?t=' + Date.now()
  }
}
