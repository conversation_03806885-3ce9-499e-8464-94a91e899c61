<!-- NotificationBell.svelte - Notification bell with unread count -->
<script lang="ts">
  import { onMount, onDestroy } from 'svelte'
  import { Bell } from 'lucide-svelte'
  import { createNotificationService } from '$lib/notification-service'
  import { supabase } from '$lib/supabase'
  import type { NotificationEvent } from '$lib/notification-service'

  export let userId: string

  let unreadCount = 0
  let showDropdown = false
  let notificationService: any
  let realtimeChannel: any

  onMount(async () => {
    // Initialize notification service
    const { service } = createNotificationService(supabase)
    notificationService = service

    // Get initial unread count
    await loadUnreadCount()

    // Subscribe to real-time updates
    realtimeChannel = notificationService.subscribeToNotifications(
      userId,
      handleRealtimeEvent
    )
  })

  onDestroy(() => {
    if (notificationService) {
      notificationService.unsubscribeFromNotifications()
    }
  })

  async function loadUnreadCount() {
    if (!notificationService) return
    
    const { count } = await notificationService.getUnreadCount(userId)
    unreadCount = count
  }

  function handleRealtimeEvent(event: NotificationEvent) {
    if (event.type === 'notification_created') {
      unreadCount += 1
    } else if (event.type === 'notifications_read') {
      unreadCount = 0
    }
  }

  function toggleDropdown() {
    showDropdown = !showDropdown
  }

  function handleClick() {
    toggleDropdown()
    // Navigate to notifications page or show dropdown
    window.location.href = '/notifications'
  }
</script>

<div class="relative">
  <button 
    on:click={handleClick}
    class="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors"
    aria-label="Notifications"
  >
    <Bell size={20} />
    
    {#if unreadCount > 0}
      <span 
        class="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-red-500 rounded-full"
      >
        {unreadCount > 99 ? '99+' : unreadCount}
      </span>
    {/if}
  </button>
</div>
