<!-- NotificationToast.svelte - Toast notification for real-time alerts -->
<script lang="ts">
  import { onMount } from 'svelte'
  import { fly } from 'svelte/transition'
  import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-svelte'
  import type { Notification } from '$lib/notification-service'

  export let notification: Notification
  export let onDismiss: () => void
  export let autoClose = true
  export let duration = 5000

  let visible = true

  onMount(() => {
    if (autoClose) {
      setTimeout(() => {
        visible = false
        setTimeout(onDismiss, 300)
      }, duration)
    }
  })

  function handleClose() {
    visible = false
    setTimeout(onDismiss, 300)
  }

  function handleClick() {
    if (notification.action_url) {
      window.location.href = notification.action_url
    }
    handleClose()
  }

  $: toastConfig = {
    low: { icon: Info, bgColor: 'bg-blue-500', textColor: 'text-blue-50' },
    normal: { icon: CheckCircle, bgColor: 'bg-green-500', textColor: 'text-green-50' },
    high: { icon: AlertTriangle, bgColor: 'bg-orange-500', textColor: 'text-orange-50' },
    urgent: { icon: AlertCircle, bgColor: 'bg-red-500', textColor: 'text-red-50' }
  }[notification.priority]
</script>

{#if visible}
  <div
    class="fixed top-4 right-4 max-w-sm w-full z-50"
    transition:fly={{ x: 300, duration: 300 }}
  >
    <div 
      class="rounded-lg shadow-lg overflow-hidden cursor-pointer {toastConfig.bgColor}"
      on:click={handleClick}
      role="button"
      tabindex="0"
    >
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svelte:component this={toastConfig.icon} size={20} class={toastConfig.textColor} />
          </div>
          
          <div class="ml-3 w-0 flex-1">
            <p class="text-sm font-medium {toastConfig.textColor}">
              {notification.title}
            </p>
            <p class="mt-1 text-sm {toastConfig.textColor} opacity-90">
              {notification.message}
            </p>
            
            {#if notification.action_label}
              <div class="mt-2">
                <span class="text-xs {toastConfig.textColor} underline">
                  {notification.action_label} →
                </span>
              </div>
            {/if}
          </div>
          
          <div class="ml-4 flex-shrink-0 flex">
            <button
              class="rounded-md inline-flex {toastConfig.textColor} hover:opacity-75 focus:outline-none"
              on:click|stopPropagation={handleClose}
            >
              <X size={16} />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
{/if}
