<!-- NotificationItem.svelte - Individual notification display -->
<script lang="ts">
  import { formatDistanceToNow } from 'date-fns'
  import type { Notification } from '$lib/notification-service'
  import { 
    User, 
    Briefcase, 
    FileText, 
    Calendar, 
    Shield, 
    Settings,
    Clock,
    ExternalLink
  } from 'lucide-svelte'

  export let notification: Notification
  export let onMarkAsRead: (id: string) => void
  export let compact = false

  const iconMap = {
    'user-plus': User,
    'user-check': User,
    'shield': Shield,
    'briefcase': Briefcase,
    'file-text': FileText,
    'calendar': Calendar,
    'settings': Settings,
    'clock': Clock
  }

  function getIcon(iconName?: string) {
    return iconMap[iconName as keyof typeof iconMap] || FileText
  }

  function handleClick() {
    if (!notification.is_read) {
      onMarkAsRead(notification.id)
    }

    if (notification.action_url) {
      window.location.href = notification.action_url
    }
  }

  function formatTime(dateString: string) {
    return formatDistanceToNow(new Date(dateString), { addSuffix: true })
  }

  $: Icon = getIcon(notification.notification_type?.icon_name)
  $: priorityColor = {
    low: 'text-gray-400',
    normal: 'text-blue-500',
    high: 'text-orange-500',
    urgent: 'text-red-500'
  }[notification.priority]
</script>

<div 
  class="flex items-start space-x-3 p-3 hover:bg-gray-50 cursor-pointer transition-colors border-l-4 {!notification.is_read ? 'border-blue-500 bg-blue-50' : 'border-transparent'}"
  on:click={handleClick}
  role="button"
  tabindex="0"
>
  <!-- Icon -->
  <div class="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center {priorityColor} bg-gray-100">
    <svelte:component this={Icon} size={16} />
  </div>

  <!-- Content -->
  <div class="flex-1 min-w-0">
    <div class="flex items-start justify-between">
      <div class="flex-1">
        <p class="text-sm font-medium text-gray-900 {!notification.is_read ? 'font-semibold' : ''}">
          {notification.title}
        </p>
        
        {#if !compact}
          <p class="text-sm text-gray-600 mt-1">
            {notification.message}
          </p>
        {/if}
      </div>

      <div class="flex items-center space-x-2 ml-2">
        {#if notification.action_url}
          <ExternalLink size={12} class="text-gray-400" />
        {/if}
        
        {#if !notification.is_read}
          <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
        {/if}
      </div>
    </div>

    <div class="flex items-center justify-between mt-2">
      <span class="text-xs text-gray-500">
        {formatTime(notification.created_at)}
      </span>

      {#if notification.action_label && notification.action_url}
        <button class="text-xs text-blue-600 hover:text-blue-800 font-medium">
          {notification.action_label}
        </button>
      {/if}
    </div>
  </div>
</div>
