<!-- Job Metrics Component -->
<script lang="ts">
	export let viewCount: number = 0;
	export let applicationsCount: number = 0;
	export let createdAt: string;
</script>

<div class="bg-white rounded-lg shadow overflow-hidden mb-6">
	<div class="px-6 py-4 border-b border-gray-200">
		<h2 class="text-lg font-medium">Job Metrics</h2>
	</div>
	<div class="p-6">
		<dl class="grid grid-cols-1 gap-4">
			<div class="p-4 bg-gray-50 rounded-lg">
				<dt class="text-sm font-medium text-gray-500">Total Views</dt>
				<dd class="mt-1 text-3xl font-semibold text-gray-900">
					{viewCount}
				</dd>
			</div>
			<div class="p-4 bg-gray-50 rounded-lg">
				<dt class="text-sm font-medium text-gray-500">Applications</dt>
				<dd class="mt-1 text-3xl font-semibold text-gray-900">
					{applicationsCount}
				</dd>
			</div>
			<div class="p-4 bg-gray-50 rounded-lg">
				<dt class="text-sm font-medium text-gray-500">Days Active</dt>
				<dd class="mt-1 text-3xl font-semibold text-gray-900">
					{Math.ceil(
						(new Date().getTime() - new Date(createdAt).getTime()) /
							(1000 * 60 * 60 * 24)
					)}
				</dd>
			</div>
		</dl>
	</div>
</div>
