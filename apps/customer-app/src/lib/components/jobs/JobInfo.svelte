<!-- Job Info Component -->
<script lang="ts">
	import { formatDate } from '@procureserve/shared-utils';
	
	export let jobId: string;
	export let createdBy: string;
	export let createdByName: string = '';
	export let createdAt: string;
	export let updatedAt: string;
</script>

<div class="bg-white rounded-lg shadow overflow-hidden mb-6">
	<div class="px-6 py-4 border-b border-gray-200">
		<h2 class="text-lg font-medium">Job Info</h2>
	</div>
	<div class="p-6">
		<dl class="divide-y divide-gray-200">
			<div class="py-3 flex justify-between">
				<dt class="text-sm font-medium text-gray-500">ID</dt>
				<dd class="text-sm text-gray-900">{jobId}</dd>
			</div>
			<div class="py-3 flex justify-between">
				<dt class="text-sm font-medium text-gray-500">Created By</dt>
				<dd class="text-sm text-gray-900">
					{createdByName || createdBy}
				</dd>
			</div>
			<div class="py-3 flex justify-between">
				<dt class="text-sm font-medium text-gray-500">Created On</dt>
				<dd class="text-sm text-gray-900">{formatDate(createdAt, 'long')}</dd>
			</div>
			<div class="py-3 flex justify-between">
				<dt class="text-sm font-medium text-gray-500">Last Updated</dt>
				<dd class="text-sm text-gray-900">{formatDate(updatedAt, 'long')}</dd>
			</div>
		</dl>
	</div>
</div>
