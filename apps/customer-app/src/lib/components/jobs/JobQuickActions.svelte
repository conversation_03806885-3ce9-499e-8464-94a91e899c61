<!-- Job Quick Actions Component -->
<script lang="ts">
	export let jobId: string;
	export let isRecruiter: boolean = false;
</script>

<div class="bg-white rounded-lg shadow overflow-hidden">
	<div class="px-6 py-4 border-b border-gray-200">
		<h2 class="text-lg font-medium">Quick Actions</h2>
	</div>
	<div class="p-6">
		<div class="space-y-3">
			<a
				href={`/applications/create?job_id=${jobId}`}
				class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
			>
				Add Candidate
			</a>
			<a
				href={`/jobs/${jobId}/share`}
				class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
			>
				Share Job
			</a>
			{#if isRecruiter}
				<a
					href={`/jobs/${jobId}/duplicate`}
					class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
				>
					Duplicate Job
				</a>
			{/if}
		</div>
	</div>
</div>
