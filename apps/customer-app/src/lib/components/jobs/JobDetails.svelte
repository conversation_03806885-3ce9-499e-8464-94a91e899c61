<!-- Job Details Component -->
<script lang="ts">
	import { formatDate, formatSalaryRange } from '@procureserve/shared-utils';
	import type { Job } from '@procureserve/shared-types';
	import JobStatusBadge from './JobStatusBadge.svelte';
	
	export let job: Job;
	export let isRecruiter: boolean = false;
	export let statusOptions: any[] = [];
	export let remoteOptions: any[] = [];
	export let onStatusChange: (newStatus: string) => void;
	
	// Get status details
	function getStatusDetails(status: string): { label: string; color: string } {
		const statusOption = statusOptions.find(opt => opt.key === status);
		return {
			label: statusOption?.label || status,
			color: statusOption?.color || '#6B7280' // Default gray if not found
		};
	}
</script>

<div class="bg-white rounded-lg shadow overflow-hidden mb-6">
	<div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
		<h2 class="text-lg font-medium">Job Details</h2>
		{#if isRecruiter}
			<div class="flex items-center">
				<span class="text-sm text-gray-500 mr-2">Status:</span>
				<div class="relative">
					<select
						value={job.status}
						on:change={(e) => onStatusChange((e.target as HTMLSelectElement).value)}
						class="rounded-full py-1 pl-3 pr-8 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 border-0"
						style="background-color: {getStatusDetails(job.status).color}25; color: {getStatusDetails(job.status).color};"
					>
						{#each statusOptions as option}
							<option value={option.key}>{option.label}</option>
						{/each}
					</select>
				</div>
			</div>
		{:else}
			<JobStatusBadge 
				status={job.status} 
				color={getStatusDetails(job.status).color} 
				label={getStatusDetails(job.status).label} 
			/>
		{/if}
	</div>
	<div class="p-6">
		<div class="mb-6">
			<h3 class="text-lg font-medium mb-2">{job.title}</h3>
			<div class="flex flex-wrap gap-4 text-sm text-gray-500 mb-4">
				{#if job.location}
					<div class="flex items-center">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-5 w-5 mr-1 text-gray-400"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
							/>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
							/>
						</svg>
						{job.location}
					</div>
				{/if}
				{#if job.remote_type}
					<div class="flex items-center">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-5 w-5 mr-1 text-gray-400"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
							/>
						</svg>
						{remoteOptions.find(opt => opt.key === job.remote_type)?.label || job.remote_type}
					</div>
				{/if}
				{#if job.salary_min || job.salary_max}
					<div class="flex items-center">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-5 w-5 mr-1 text-gray-400"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
							/>
						</svg>
						{formatSalaryRange(
							job.salary_min,
							job.salary_max,
							job.salary_currency,
							job.salary_period
						)}
					</div>
				{/if}
				<div class="flex items-center">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						class="h-5 w-5 mr-1 text-gray-400"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
						/>
					</svg>
					Posted on {formatDate(job.created_at, 'long')}
				</div>
			</div>

			<div class="prose max-w-none mt-4">
				<h4 class="text-base font-medium mb-2">Description</h4>
				<div class="whitespace-pre-line">{job.description || 'No description provided.'}</div>
			</div>

			{#if job.requirements && Object.keys(job.requirements).length > 0}
				<div class="mt-6">
					<h4 class="text-base font-medium mb-2">Requirements</h4>
					<ul class="list-disc pl-5 space-y-1">
						{#each Object.entries(job.requirements) as [key, value]}
							<li>{key}: {value}</li>
						{/each}
					</ul>
				</div>
			{/if}
		</div>
	</div>
</div>
