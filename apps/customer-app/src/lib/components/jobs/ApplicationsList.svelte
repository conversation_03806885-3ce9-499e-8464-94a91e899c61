<!-- Applications List Component -->
<script lang="ts">
	import type { Application } from '@procureserve/shared-types';
	
	export let applications: Application[] = [];
	export let applicationsCount: number = 0;
	export let jobId: string;
</script>

<div class="bg-white rounded-lg shadow overflow-hidden">
	<div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
		<h2 class="text-lg font-medium">Applications</h2>
		<span class="text-sm text-gray-500">{applicationsCount} total</span>
	</div>
	<div class="p-6">
		{#if applications.length === 0}
			<div class="text-center py-6">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="h-12 w-12 mx-auto text-gray-400"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
					/>
				</svg>
				<p class="mt-2 text-sm text-gray-500">
					No applications yet for this job.
				</p>
			</div>
		{:else}
			<ul class="divide-y divide-gray-200">
				{#each applications as application}
					<li class="py-4">
						<div class="flex items-center space-x-4">
							<div class="flex-shrink-0">
								<div
									class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center"
								>
									<span class="font-medium text-blue-800">
										{application.candidates?.name?.charAt(0) || '?'}
									</span>
								</div>
							</div>
							<div class="flex-1 min-w-0">
								<p class="text-sm font-medium text-gray-900 truncate">
									{application.candidates?.name || 'Candidate'}
								</p>
								<p class="text-sm text-gray-500 truncate">
									{application.candidates?.email || 'No email provided'}
								</p>
							</div>
							<div>
								<span
									class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800"
								>
									{application.status}
								</span>
							</div>
							<div class="flex-shrink-0">
								<a
									href={`/applications/${application.id}`}
									class="text-sm font-medium text-blue-600 hover:text-blue-900"
								>
									View
								</a>
							</div>
						</div>
					</li>
				{/each}
			</ul>
			{#if applicationsCount > 5}
				<div class="mt-4 text-center">
					<a
						href={`/applications?job_id=${jobId}`}
						class="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-900"
					>
						View all {applicationsCount} applications
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-5 w-5 ml-1"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M9 5l7 7-7 7"
							/>
						</svg>
					</a>
				</div>
			{/if}
		{/if}
	</div>
</div>
