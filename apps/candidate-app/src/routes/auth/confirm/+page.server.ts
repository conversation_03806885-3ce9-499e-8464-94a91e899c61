import { redirect } from '@sveltejs/kit'
import { createSupabaseServerClient } from '$lib/supabase'
import type { PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ url, cookies }) => {
  const supabase = createSupabaseServerClient(cookies)
  
  const token_hash = url.searchParams.get('token_hash')
  const type = url.searchParams.get('type')
  const next = url.searchParams.get('next') ?? '/dashboard'

  if (token_hash && type) {
    const { data, error } = await supabase.auth.verifyOtp({
      type: type as any,
      token_hash,
    })

    if (!error && data.user) {
      // Check if user has a candidate profile
      const { data: candidateData, error: candidateError } = await supabase
        .from('candidates')
        .select('id, first_name, last_name, email')
        .eq('auth_user_id', data.user.id)
        .single()

      if (candidateError || !candidateData) {
        // User exists but no candidate profile - create one from auth metadata
        const userData = data.user.user_metadata
        const firstName = userData.first_name || ''
        const lastName = userData.last_name || ''
        const fullName = `${firstName} ${lastName}`.trim() || data.user.email?.split('@')[0] || 'User'

        const { error: createError } = await supabase
          .from('candidates')
          .insert({
            auth_user_id: data.user.id,
            first_name: firstName,
            last_name: lastName,
            name: fullName,
            email: data.user.email,
            phone: null, // Phone is now optional
            status: 'active',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })

        if (createError) {
          console.error('Failed to create candidate profile after email confirmation:', createError)
          // For critical errors, we still redirect to complete-profile as fallback
          throw redirect(303, '/complete-profile')
        }
      }

      // Success - redirect to dashboard
      throw redirect(303, next)
    } else {
      console.error('Email confirmation error:', error)
      // Redirect to login with error message
      throw redirect(303, '/login?error=confirmation_failed')
    }
  }

  // No token or type provided - redirect to login
  throw redirect(303, '/login')
} 