<script lang="ts">
  import { onMount } from 'svelte'
  import { UserCheck } from 'lucide-svelte'

  let message = 'Confirming your email...'
  
  onMount(() => {
    // This page should quickly redirect via the server load function
    // If we're still here after a few seconds, something might be wrong
    const timeout = setTimeout(() => {
      message = 'Taking longer than expected. You may need to try the link again.'
    }, 5000)
    
    return () => clearTimeout(timeout)
  })
</script>

<svelte:head>
  <title>Confirming Email - Candidate Portal</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background px-4">
  <div class="max-w-md w-full text-center space-y-8">
    <!-- Header -->
    <div class="text-center">
      <div class="mx-auto w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-4">
        <UserCheck class="w-6 h-6 text-primary-foreground" />
      </div>
      <h2 class="text-3xl font-bold text-foreground">Email Confirmation</h2>
    </div>

    <!-- Loading State -->
    <div class="bg-white rounded-lg border border-gray-200 p-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
      <p class="text-muted-foreground">{message}</p>
    </div>

    <!-- Help Text -->
    <div class="text-center space-y-4">
      <p class="text-sm text-muted-foreground">
        If you continue to have issues, please try 
        <a href="/login" class="text-primary hover:text-primary/80 font-medium">
          signing in
        </a>
        or 
        <a href="/register" class="text-primary hover:text-primary/80 font-medium">
          registering again
        </a>.
      </p>
    </div>
  </div>
</div> 