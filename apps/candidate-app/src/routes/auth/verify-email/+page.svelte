<script lang="ts">
  import { <PERSON>Circle, AlertTriangle, Mail, ArrowRight } from 'lucide-svelte'
  import Button from '$lib/components/ui/button.svelte'
  import Card from '$lib/components/ui/card.svelte'
  import CardHeader from '$lib/components/ui/card-header.svelte'
  import CardContent from '$lib/components/ui/card-content.svelte'
  import CardTitle from '$lib/components/ui/card-title.svelte'
  import type { PageData } from './$types'

  let { data }: { data: PageData } = $props()
  
  let resendingEmail = $state(false)

  async function resendVerificationEmail() {
    if (!data.email || !data.user_id) return
    
    resendingEmail = true
    
    try {
      const formData = new FormData()
      formData.append('email', data.email)
      formData.append('user_id', data.user_id)
      formData.append('first_name', 'User') // We don't have first name here
      
      const response = await fetch('/register?/resend_verification', {
        method: 'POST',
        body: formData
      })
      
      if (response.ok) {
        // Show success message
        window.location.reload()
      }
    } catch (error) {
      console.error('Failed to resend email:', error)
    } finally {
      resendingEmail = false
    }
  }

  // Auto-redirect to login after successful verification
  $effect(() => {
    if (data.redirect_to_login && data.success) {
      setTimeout(() => {
        window.location.href = '/login'
      }, 3000)
    }
  })
</script>

<svelte:head>
  <title>Email Verification - Candidate Portal</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <div class={`mx-auto w-12 h-12 rounded-lg flex items-center justify-center mb-4 ${
        data.success ? 'bg-green-600' : 'bg-orange-600'
      }`}>
        {#if data.success}
          <CheckCircle class="w-6 h-6 text-white" />
        {:else}
          <Mail class="w-6 h-6 text-white" />
        {/if}
      </div>
      <h2 class="text-3xl font-bold text-foreground">
        {data.success ? 'Email Verified!' : 'Email Verification'}
      </h2>
    </div>

    <!-- Verification Result -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center">
          {#if data.success}
            <CheckCircle class="w-5 h-5 text-green-600 mr-2" />
            Verification Successful
          {:else}
            <AlertTriangle class="w-5 h-5 text-orange-600 mr-2" />
            Verification Issue
          {/if}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class={`p-4 rounded-lg ${data.success ? 'bg-green-50 border border-green-200' : 'bg-orange-50 border border-orange-200'}`}>
          <p class={`text-sm ${data.success ? 'text-green-800' : 'text-orange-800'}`}>
            {data.message}
          </p>
          
          {#if data.email}
            <p class={`text-xs mt-2 ${data.success ? 'text-green-600' : 'text-orange-600'}`}>
              Email: <strong>{data.email}</strong>
            </p>
          {/if}
        </div>

        {#if data.success && data.redirect_to_login}
          <div class="mt-6 space-y-4">
            <div class="text-center">
              <p class="text-sm text-muted-foreground mb-4">
                Your account is now verified and ready to use!
              </p>
              
              <a href="/login" class="inline-flex items-center justify-center w-full bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors">
                Sign In to Your Account
                <ArrowRight class="w-4 h-4 ml-2" />
              </a>
              
              <p class="text-xs text-muted-foreground mt-3">
                Redirecting automatically in 3 seconds...
              </p>
            </div>
          </div>
        {/if}

        {#if !data.success && data.show_resend}
          <div class="mt-6 space-y-4">
            <div class="border-t pt-4">
              <p class="text-sm text-muted-foreground mb-3">
                {#if data.token_expired}
                  The verification link has expired. Request a new one below.
                {:else if data.token_used}
                  This link has already been used. You can request a new one if needed.
                {:else}
                  Having trouble? You can request a new verification email.
                {/if}
              </p>
              
              <Button
                onclick={resendVerificationEmail}
                disabled={resendingEmail}
                variant="outline"
                class="w-full"
              >
                {#if resendingEmail}
                  <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                  Sending...
                {:else}
                  <Mail class="w-4 h-4 mr-2" />
                  Send New Verification Email
                {/if}
              </Button>
            </div>
          </div>
        {/if}

        {#if !data.success && !data.show_resend}
          <div class="mt-6">
            <div class="text-center">
              <a href="/register" class="text-sm text-primary hover:text-primary/80">
                Go back to registration
              </a>
            </div>
          </div>
        {/if}
      </CardContent>
    </Card>

    <!-- Footer Links -->
    <div class="text-center space-y-4">
      <p class="text-sm text-muted-foreground">
        Need help?
        <a href="mailto:<EMAIL>" class="font-medium text-primary hover:text-primary/80">
          Contact Support
        </a>
      </p>

      <p class="text-xs text-muted-foreground">
        Looking for business portal?
        <a href="https://app.procureserve.com" class="text-primary hover:text-primary/80">
          Click here
        </a>
      </p>
    </div>
  </div>
</div>
