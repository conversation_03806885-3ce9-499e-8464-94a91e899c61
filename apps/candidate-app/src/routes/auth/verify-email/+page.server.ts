import { redirect } from '@sveltejs/kit'
import { createSupabaseAdminClient } from '$lib/server/supabase-admin'
import { checkEmailVerificationLimit, logSecurityEvent } from '$lib/server/rate-limiter'
import type { PageServerLoad } from './$types'

// Helper to get client IP address
function getClientIP(request: Request): string {
  const xForwardedFor = request.headers.get('x-forwarded-for')
  const xRealIP = request.headers.get('x-real-ip')
  const cfConnectingIP = request.headers.get('cf-connecting-ip')
  
  return (
    cfConnectingIP ||
    xRealIP ||
    (xForwardedFor ? xForwardedFor.split(',')[0].trim() : '') ||
    '127.0.0.1'
  )
}

// Helper to sanitize email for logging
function sanitizeEmailForLogging(email: string): string {
  const [local, domain] = email.split('@')
  if (!local || !domain) return 'invalid-email'
  
  const sanitizedLocal = local.length > 2 
    ? local.substring(0, 2) + '*'.repeat(local.length - 2)
    : '*'.repeat(local.length)
  
  return `${sanitizedLocal}@${domain}`
}

// Production-ready email verification function with enhanced security
async function verifyEmailToken(token: string, ip: string) {
  // Check rate limits first
  const rateLimitResult = checkEmailVerificationLimit(ip, token)
  
  if (!rateLimitResult.allowed) {
    const waitMinutes = Math.ceil((rateLimitResult.resetTime - Date.now()) / (60 * 1000))
    
    logSecurityEvent('email_verification_rate_limited', {
      token: token.substring(0, 8) + '...',
      waitMinutes,
      blockedBy: rateLimitResult.blockedBy
    }, ip)

    return {
      success: false,
      message: `Too many verification attempts. Please wait ${waitMinutes} minutes before trying again.`,
      rate_limited: true,
      retry_after: rateLimitResult.resetTime
    }
  }

  const supabase = createSupabaseAdminClient()

  try {
    const { data, error } = await supabase.rpc('verify_email_token', {
      p_token: token
    })

    if (error) {
      console.error('[SECURITY] RPC verify_email_token error:', error)
      logSecurityEvent('email_verification_rpc_error', {
        token: token.substring(0, 8) + '...',
        error: error.message
      }, ip)
      
      return {
        success: false,
        message: 'Verification failed due to a technical issue. Please try again.'
      }
    }

    // The RPC function returns a JSON object, handle both cases
    const result = Array.isArray(data) ? data[0] : data
    
    if (result.success) {
      logSecurityEvent('email_verified_successfully', {
        userId: result.user_id,
        email: result.email ? sanitizeEmailForLogging(result.email) : 'unknown'
      }, ip)
    } else {
      logSecurityEvent('email_verification_failed', {
        token: token.substring(0, 8) + '...',
        reason: result.message,
        userId: result.user_id || 'unknown',
        email: result.email ? sanitizeEmailForLogging(result.email) : 'unknown'
      }, ip)
    }
    
    return {
      success: result.success,
      user_id: result.user_id,
      email: result.email,
      message: result.message
    }
  } catch (error) {
    console.error('[SECURITY] Email verification error:', error)
    logSecurityEvent('email_verification_unexpected_error', {
      token: token.substring(0, 8) + '...',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, ip)
    
    return {
      success: false,
      message: 'An error occurred during verification. Please try again.'
    }
  }
}

export const load: PageServerLoad = async ({ url, request }) => {
  const ip = getClientIP(request)
  const token = url.searchParams.get('token')
  
  if (!token) {
    logSecurityEvent('email_verification_no_token', {}, ip)
    return {
      success: false,
      message: 'Invalid verification link. Missing token.',
      show_resend: false
    }
  }

  // Basic token validation
  if (token.length < 10 || token.length > 100) {
    logSecurityEvent('email_verification_invalid_token_format', {
      tokenLength: token.length
    }, ip)
    
    return {
      success: false,
      message: 'Invalid verification link format.',
      show_resend: false
    }
  }

  try {
    const result = await verifyEmailToken(token, ip)
    
    // Handle rate limiting
    if (result.rate_limited) {
      return {
        success: false,
        message: result.message,
        show_resend: false,
        rate_limited: true,
        retry_after: result.retry_after
      }
    }
    
    if (result.success) {
      // Email verified successfully
      return {
        success: true,
        message: result.message,
        email: result.email,
        verified: true,
        redirect_to_login: true
      }
    } else {
      // Verification failed
      const isExpired = result.message?.includes('expired')
      const isUsed = result.message?.includes('already been used')
      const isInvalid = result.message?.includes('Invalid verification')
      
      return {
        success: false,
        message: result.message,
        email: result.email,
        user_id: result.user_id,
        show_resend: !isInvalid && !!result.user_id && !!result.email,
        token_expired: isExpired,
        token_used: isUsed,
        token_invalid: isInvalid
      }
    }
  } catch (error) {
    console.error('[SECURITY] Email verification load error:', error)
    logSecurityEvent('email_verification_load_error', {
      token: token.substring(0, 8) + '...',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, ip)
    
    return {
      success: false,
      message: 'An error occurred during verification. Please try again.',
      show_resend: false
    }
  }
}
