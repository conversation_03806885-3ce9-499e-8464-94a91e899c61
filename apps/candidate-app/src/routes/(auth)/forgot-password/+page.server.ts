import { fail } from '@sveltejs/kit'
import { createSupabaseAdminClient } from '$lib/server/supabase-admin'
import { sendPasswordResetEmail } from '@psii/email-service/verification'
import { checkPasswordResetLimit, logSecurityEvent } from '$lib/server/rate-limiter'
import { RESEND_API_KEY, EMAIL_FROM_ADDRESS, EMAIL_FROM_NAME } from '$env/static/private'
import type { Actions } from './$types'

// Email configuration from environment variables
const emailConfig = {
  apiKey: RESEND_API_KEY,
  fromName: EMAIL_FROM_NAME || 'ProcureServe',
  fromAddress: EMAIL_FROM_ADDRESS || '<EMAIL>'
}

// Helper to get client IP address
function getClientIP(request: Request): string {
  const xForwardedFor = request.headers.get('x-forwarded-for')
  const xRealIP = request.headers.get('x-real-ip')
  const cfConnectingIP = request.headers.get('cf-connecting-ip')
  
  return (
    cfConnectingIP ||
    xRealIP ||
    (xForwardedFor ? xForwardedFor.split(',')[0].trim() : '') ||
    '127.0.0.1'
  )
}

// Helper to sanitize email for logging
function sanitizeEmailForLogging(email: string): string {
  const [local, domain] = email.split('@')
  if (!local || !domain) return 'invalid-email'
  
  const sanitizedLocal = local.length > 2 
    ? local.substring(0, 2) + '*'.repeat(local.length - 2)
    : '*'.repeat(local.length)
  
  return `${sanitizedLocal}@${domain}`
}

export const actions: Actions = {
  reset: async ({ request, url }) => {
    const ip = getClientIP(request)
    const formData = await request.formData()
    const email = formData.get('email') as string

    if (!email) {
      logSecurityEvent('password_reset_no_email', {}, ip)
      return fail(400, { error: 'Email address is required', email })
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      logSecurityEvent('password_reset_invalid_email', {
        email: sanitizeEmailForLogging(email)
      }, ip)
      return fail(400, { error: 'Please enter a valid email address', email })
    }

    // Check rate limits
    const rateLimitResult = checkPasswordResetLimit(ip, email)
    
    if (!rateLimitResult.allowed) {
      const waitMinutes = Math.ceil((rateLimitResult.resetTime - Date.now()) / (60 * 1000))
      
      logSecurityEvent('password_reset_rate_limited', {
        email: sanitizeEmailForLogging(email),
        waitMinutes,
        blockedBy: rateLimitResult.blockedBy
      }, ip)

      return fail(429, {
        error: `Too many password reset attempts. Please wait ${waitMinutes} minutes before trying again.`,
        email,
        retry_after: rateLimitResult.resetTime,
        rate_limited: true
      })
    }
    
    // Always return a generic success-like message to prevent email enumeration.
    const genericSuccessResponse = {
      success: true,
      message: 'If an account with that email exists, you will receive reset instructions.'
    }

    try {
      const supabaseAdmin = createSupabaseAdminClient()

      // We need to check if the user exists to get their name for the email template.
      // Query auth.users table directly using admin client
      const { data: { users }, error: userError } = await supabaseAdmin.auth.admin.listUsers()

      if (userError) {
        console.error('[SECURITY] Failed to query users during password reset:', userError)
        logSecurityEvent('password_reset_user_query_failed', {
          email: sanitizeEmailForLogging(email),
          error: userError.message
        }, ip)
        
        // Don't leak error information
        return genericSuccessResponse
      }

      const userData = users?.find(user => user.email?.toLowerCase() === email.toLowerCase())
      
      if (!userData) {
        // Don't leak that the user does not exist.
        logSecurityEvent('password_reset_user_not_found', {
          email: sanitizeEmailForLogging(email)
        }, ip)
        return genericSuccessResponse
      }

      // Only send reset email for verified users
      if (!userData.email_confirmed_at) {
        logSecurityEvent('password_reset_unverified_user', {
          email: sanitizeEmailForLogging(email),
          userId: userData.id
        }, ip)
        // Don't reveal that the user exists but is unverified
        return genericSuccessResponse
      }

      const { data, error } = await supabaseAdmin.auth.admin.generateLink({
        type: 'recovery',
        email: email,
        options: {
          redirectTo: `${url.origin}/reset-password/update`
        }
      })

      if (error) {
        console.error('[SECURITY] Error generating recovery link:', error)
        logSecurityEvent('password_reset_link_generation_failed', {
          email: sanitizeEmailForLogging(email),
          userId: userData.id,
          error: error.message
        }, ip)
        
        return fail(500, { 
          error: 'Could not generate a password reset link. Please try again later.',
          retry_suggested: true
        })
      }
      
      const resetLink = data.properties.action_link

      // Send the email using our custom service
      const emailResult = await sendPasswordResetEmail({
        to: email,
        firstName: userData.user_metadata?.first_name || 'there',
        resetLink: resetLink,
        config: emailConfig
      })

      if (!emailResult.success) {
        console.error('[SECURITY] Password reset email sending failed:', emailResult.error)
        logSecurityEvent('password_reset_email_failed', {
          email: sanitizeEmailForLogging(email),
          userId: userData.id,
          error: emailResult.error
        }, ip)
        
        return fail(500, {
          error: 'Could not send password reset email. Please try again later.',
          retry_suggested: true
        })
      }

      logSecurityEvent('password_reset_email_sent', {
        email: sanitizeEmailForLogging(email),
        userId: userData.id
      }, ip)

      return genericSuccessResponse
      
    } catch (error) {
      console.error('[SECURITY] Unexpected password reset error:', error)
      logSecurityEvent('password_reset_unexpected_error', {
        email: sanitizeEmailForLogging(email),
        error: error instanceof Error ? error.message : 'Unknown error'
      }, ip)
      
      return fail(500, {
        error: 'An unexpected server error occurred. Please try again.',
        retry_suggested: true
      })
    }
  }
}
