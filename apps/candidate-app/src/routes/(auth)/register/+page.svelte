<script lang="ts">
  import { enhance } from '$app/forms'
  import { UserCheck, Eye, EyeOff, AlertCircle, CheckCircle, Mail, Clock } from 'lucide-svelte'
  import Button from '$lib/components/ui/button.svelte'
  import Input from '$lib/components/ui/input.svelte'
  import Label from '$lib/components/ui/label.svelte'
  import Card from '$lib/components/ui/card.svelte'
  import CardHeader from '$lib/components/ui/card-header.svelte'
  import CardContent from '$lib/components/ui/card-content.svelte'
  import CardTitle from '$lib/components/ui/card-title.svelte'
  import type { ActionData } from './$types'

  let { form }: { form: ActionData } = $props()

  let loading = $state(false)
  let showPassword = $state(false)
  let showConfirmPassword = $state(false)
  let resendingEmail = $state(false)

  let formData = $state({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    password: '',
    confirm_password: ''
  })

  let passwordsMatch = $derived(formData.password === formData.confirm_password)
  let formValid = $derived(
    formData.first_name &&
    formData.last_name &&
    formData.email &&
    formData.password &&
    formData.confirm_password &&
    passwordsMatch
  )

  // Calculate retry time for rate limiting
  let retryMinutes = $derived(() => {
    if (form?.retry_after) {
      const waitTime = Math.ceil((form.retry_after - Date.now()) / (60 * 1000))
      return Math.max(0, waitTime)
    }
    return 0
  })

  function togglePasswordVisibility() {
    showPassword = !showPassword
  }

  function toggleConfirmPasswordVisibility() {
    showConfirmPassword = !showConfirmPassword
  }

  async function resendVerificationEmail() {
    if (!form?.email) return
    
    resendingEmail = true
    
    try {
      const resendFormData = new FormData()
      resendFormData.append('email', form.email)
      
      const response = await fetch('?/resend_verification', {
        method: 'POST',
        body: resendFormData
      })
      
      if (response.ok) {
        // Reload the page to show the updated form state
        window.location.reload()
      }
    } catch (error) {
      console.error('Failed to resend email:', error)
    } finally {
      resendingEmail = false
    }
  }
</script>

<svelte:head>
  <title>Register - Candidate Portal</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <div class="mx-auto w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-4">
        <UserCheck class="w-6 h-6 text-primary-foreground" />
      </div>
      <h2 class="text-3xl font-bold text-foreground">Create Your Profile</h2>
      <p class="mt-2 text-sm text-muted-foreground">
        Join our talent network and discover opportunities
      </p>
    </div>

    <!-- Success Message -->
    {#if form?.success}
      <div class="rounded-md bg-green-50 border border-green-200 p-4">
        <div class="flex">
          <CheckCircle class="h-5 w-5 text-green-400" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">
              {#if form.new_user}
                Registration Successful!
              {:else if form.existing_unverified}
                Verification Email Sent
              {:else}
                Email Sent
              {/if}
            </h3>
            <div class="mt-2 text-sm text-green-700">
              {form.message}
            </div>
            {#if form.email && !form.generic_response}
              <div class="mt-3">
                <p class="text-xs text-green-600">
                  <Mail class="w-3 h-3 inline mr-1" />
                  Check your inbox at <strong>{form.email}</strong>
                </p>
              </div>
            {/if}
          </div>
        </div>
      </div>
    {/if}

    <!-- Rate Limit Error -->
    {#if form?.rate_limited}
      <div class="rounded-md bg-orange-50 border border-orange-200 p-4">
        <div class="flex">
          <Clock class="h-5 w-5 text-orange-400" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-orange-800">Rate Limit Exceeded</h3>
            <div class="mt-2 text-sm text-orange-700">
              {form.error}
            </div>
            {#if retryMinutes() > 0}
              <p class="text-xs text-orange-600 mt-2">
                You can try again in {retryMinutes()} minute{retryMinutes() === 1 ? '' : 's'}.
              </p>
            {/if}
          </div>
        </div>
      </div>
    {/if}

    <!-- User Already Exists - Helpful Actions -->
    {#if form?.user_exists && form?.suggestions}
      <div class="rounded-md bg-blue-50 border border-blue-200 p-4">
        <div class="flex">
          <AlertCircle class="h-5 w-5 text-blue-400" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Account Already Exists</h3>
            <div class="mt-2 text-sm text-blue-700">
              {form.error}
            </div>
            <p class="text-xs text-blue-600 mt-2">
              {form.message}
            </p>
            
            <!-- Action Buttons -->
            <div class="mt-4 flex flex-col sm:flex-row gap-2">
              {#if form.suggestions.login}
                <a 
                  href="/login?email={encodeURIComponent(form.email || '')}"
                  class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Sign In
                </a>
              {/if}
              {#if form.suggestions.forgot_password}
                <a 
                  href="/forgot-password?email={encodeURIComponent(form.email || '')}"
                  class="inline-flex items-center px-3 py-2 border border-blue-300 text-sm leading-4 font-medium rounded-md text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Reset Password
                </a>
              {/if}
            </div>
          </div>
        </div>
      </div>
    {/if}

    <!-- General Error Message -->
    {#if form?.error && !form?.rate_limited && !form?.user_exists}
      <div class="rounded-md bg-red-50 border border-red-200 p-4">
        <div class="flex">
          <AlertCircle class="h-5 w-5 text-red-400" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Registration Error</h3>
            <div class="mt-2 text-sm text-red-700">
              {form.error}
            </div>
            {#if form.retry_suggested}
              <p class="text-xs text-red-600 mt-2">
                Please try again in a few moments.
              </p>
            {/if}
          </div>
        </div>
      </div>
    {/if}

    <!-- Field Errors -->
    {#if form?.field_errors}
      <div class="rounded-md bg-red-50 border border-red-200 p-4">
        <div class="flex">
          <AlertCircle class="h-5 w-5 text-red-400" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
            <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
              {#each Object.entries(form.field_errors || {}) as [field, errors]}
                {#each (errors || []) as error}
                  <li>{error}</li>
                {/each}
              {/each}
            </ul>
          </div>
        </div>
      </div>
    {/if}

    <!-- Registration Form - Hide if successful or user exists -->
    {#if !form?.success && !form?.user_exists}
      <Card class="mt-8">
        <CardHeader>
          <CardTitle>Create Your Account</CardTitle>
        </CardHeader>
        <CardContent>
          <form
            method="POST"
            action="?/register"
            use:enhance={() => {
              loading = true
              return async ({ update }) => {
                loading = false
                await update()
              }
            }}
            class="space-y-6"
          >
            <div class="space-y-4">
              <!-- Name Fields -->
              <div class="grid grid-cols-2 gap-4">
                <div class="space-y-2">
                  <Label for="first_name">First Name</Label>
                  <Input
                    id="first_name"
                    name="first_name"
                    type="text"
                    required
                    bind:value={formData.first_name}
                    placeholder="John"
                    disabled={loading || form?.rate_limited}
                    class={form?.field_errors?.first_name ? 'border-destructive' : ''}
                  />
                </div>
                <div class="space-y-2">
                  <Label for="last_name">Last Name</Label>
                  <Input
                    id="last_name"
                    name="last_name"
                    type="text"
                    required
                    bind:value={formData.last_name}
                    placeholder="Doe"
                    disabled={loading || form?.rate_limited}
                    class={form?.field_errors?.last_name ? 'border-destructive' : ''}
                  />
                </div>
              </div>

              <!-- Email -->
              <div class="space-y-2">
                <Label for="email">Email Address</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  autocomplete="email"
                  required
                  bind:value={formData.email}
                  placeholder="<EMAIL>"
                  disabled={loading || form?.rate_limited}
                  class={form?.field_errors?.email ? 'border-destructive' : ''}
                />
              </div>

              <!-- Phone -->
              <div class="space-y-2">
                <Label for="phone">Phone Number (Optional)</Label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  bind:value={formData.phone}
                  placeholder="(*************"
                  disabled={loading || form?.rate_limited}
                  class={form?.field_errors?.phone ? 'border-destructive' : ''}
                />
              </div>

              <!-- Password -->
              <div class="space-y-2">
                <Label for="password">Password</Label>
                <div class="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autocomplete="new-password"
                    required
                    bind:value={formData.password}
                    placeholder="Create a strong password"
                    disabled={loading || form?.rate_limited}
                    class="pr-10 {form?.field_errors?.password ? 'border-destructive' : ''}"
                  />
                  <button
                    type="button"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onclick={togglePasswordVisibility}
                    disabled={loading || form?.rate_limited}
                  >
                    {#if showPassword}
                      <EyeOff class="h-4 w-4 text-muted-foreground" />
                    {:else}
                      <Eye class="h-4 w-4 text-muted-foreground" />
                    {/if}
                  </button>
                </div>
              </div>

              <!-- Confirm Password -->
              <div class="space-y-2">
                <Label for="confirm_password">Confirm Password</Label>
                <div class="relative">
                  <Input
                    id="confirm_password"
                    name="confirm_password"
                    type={showConfirmPassword ? 'text' : 'password'}
                    autocomplete="new-password"
                    required
                    bind:value={formData.confirm_password}
                    placeholder="Confirm your password"
                    disabled={loading || form?.rate_limited}
                    class="pr-10 {(!passwordsMatch && formData.confirm_password) || form?.field_errors?.confirm_password ? 'border-destructive' : ''}"
                  />
                  <button
                    type="button"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onclick={toggleConfirmPasswordVisibility}
                    disabled={loading || form?.rate_limited}
                  >
                    {#if showConfirmPassword}
                      <EyeOff class="h-4 w-4 text-muted-foreground" />
                    {:else}
                      <Eye class="h-4 w-4 text-muted-foreground" />
                    {/if}
                  </button>
                </div>
                {#if !passwordsMatch && formData.confirm_password}
                  <p class="text-sm text-destructive">Passwords do not match</p>
                {/if}
              </div>
            </div>

            <!-- Submit Button -->
            <Button
              type="submit"
              disabled={loading || !formValid || form?.rate_limited}
              class="w-full"
            >
              {#if loading}
                <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                Creating Account...
              {:else if form?.rate_limited}
                <Clock class="w-4 h-4 mr-2" />
                Try Again Later
              {:else}
                Create Account
              {/if}
            </Button>
          </form>
        </CardContent>
      </Card>
    {/if}

    <!-- Success Actions -->
    {#if form?.success && form?.email && !form?.generic_response}
      <Card>
        <CardContent class="pt-6">
          <div class="text-center space-y-4">
            <h3 class="text-lg font-medium">What's Next?</h3>
            <div class="space-y-3 text-sm text-muted-foreground">
              <p>1. Check your email inbox for the verification link</p>
              <p>2. Click the verification link to activate your account</p>
              <p>3. Sign in and complete your profile</p>
            </div>
            
            <!-- Resend Email Option -->
            <div class="pt-4 border-t">
              <p class="text-xs text-muted-foreground mb-3">
                Didn't receive the email? Check your spam folder first.
              </p>
              <Button
                variant="outline"
                size="sm"
                onclick={resendVerificationEmail}
                disabled={resendingEmail}
                class="w-full"
              >
                {#if resendingEmail}
                  <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                  Sending...
                {:else}
                  <Mail class="w-4 h-4 mr-2" />
                  Resend Verification Email
                {/if}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    {/if}

    <!-- Footer Links -->
    <div class="text-center space-y-4">
      <p class="text-sm text-muted-foreground">
        Already have an account?
        <a href="/login" class="font-medium text-primary hover:text-primary/80">
          Sign in here
        </a>
      </p>

      <p class="text-xs text-muted-foreground">
        Looking for business portal?
        <a href="https://app.procureserve.com" class="text-primary hover:text-primary/80">
          Click here
        </a>
      </p>
    </div>
  </div>
</div>
