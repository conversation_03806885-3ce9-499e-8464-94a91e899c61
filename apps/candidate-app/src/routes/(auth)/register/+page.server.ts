import { fail, error as svelte<PERSON>rror } from '@sveltejs/kit'
import { ZodError } from 'zod'
import { sendVerificationEmail } from '@psii/email-service/verification'
import { createSupabaseAdminClient } from '$lib/server/supabase-admin'
import { registrationSchema } from '$lib/schemas/auth'
import { checkRegistrationLimit, logSecurityEvent } from '$lib/server/rate-limiter'
import { RESEND_API_KEY, EMAIL_FROM_ADDRESS, EMAIL_FROM_NAME } from '$env/static/private'
import type { Actions } from './$types'

// Email configuration from environment variables
const emailConfig = {
  apiKey: RESEND_API_KEY,
  fromName: EMAIL_FROM_NAME || 'ProcureServe',
  fromAddress: EMAIL_FROM_ADDRESS || '<EMAIL>'
}

// Helper to get client IP address
function getClientIP(request: Request): string {
  const xForwardedFor = request.headers.get('x-forwarded-for')
  const xRealIP = request.headers.get('x-real-ip')
  const cfConnectingIP = request.headers.get('cf-connecting-ip')
  
  return (
    cfConnectingIP ||
    xRealIP ||
    (xForwardedFor ? xForwardedFor.split(',')[0].trim() : '') ||
    '127.0.0.1'
  )
}

// Helper to sanitize email for logging (remove sensitive parts)
function sanitizeEmailForLogging(email: string): string {
  const [local, domain] = email.split('@')
  if (!local || !domain) return 'invalid-email'
  
  const sanitizedLocal = local.length > 2 
    ? local.substring(0, 2) + '*'.repeat(local.length - 2)
    : '*'.repeat(local.length)
  
  return `${sanitizedLocal}@${domain}`
}

// Production-ready user existence checker with security
async function checkUserStatus(email: string): Promise<{
  exists: boolean
  isVerified: boolean
  user?: any
  error?: string
}> {
  try {
    const supabaseAdmin = createSupabaseAdminClient()
    
    // Query auth.users table using admin client
    const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers()
    
    if (error) {
      console.error('[SECURITY] Failed to query users during registration:', error)
      return { exists: false, isVerified: false, error: 'Database query failed' }
    }

    const existingUser = users?.find(user => user.email?.toLowerCase() === email.toLowerCase())
    
    if (!existingUser) {
      return { exists: false, isVerified: false }
    }

    return {
      exists: true,
      isVerified: !!existingUser.email_confirmed_at,
      user: existingUser
    }
  } catch (error) {
    console.error('[SECURITY] Unexpected error during user status check:', error)
    return { exists: false, isVerified: false, error: 'Unexpected error' }
  }
}

// Secure registration handler for existing unverified users
async function handleExistingUnverifiedUser(user: any, email: string, firstName: string, baseUrl: string, ip: string): Promise<{
  success: boolean
  error?: string
}> {
  try {
    const supabaseAdmin = createSupabaseAdminClient()

    // Create new verification token
    const { data: tokenData, error: tokenError } = await supabaseAdmin.rpc('create_verification_token' as any, {
      p_user_id: user.id,
      p_email: email,
      p_first_name: firstName
    })

    if (tokenError) {
      console.error('[SECURITY] Token creation failed for existing user:', tokenError)
      logSecurityEvent('token_creation_failed', {
        email: sanitizeEmailForLogging(email),
        error: tokenError.message
      }, ip)
      return { success: false, error: 'Token creation failed' }
    }

    const verificationToken = tokenData as string

    // Send verification email
    const emailResult = await sendVerificationEmail({
      to: email,
      firstName: firstName,
      token: verificationToken,
      baseUrl: baseUrl,
      config: emailConfig
    })

    if (!emailResult.success) {
      console.error('[SECURITY] Email sending failed for existing user:', emailResult.error)
      logSecurityEvent('email_send_failed', {
        email: sanitizeEmailForLogging(email),
        error: emailResult.error
      }, ip)
      return { success: false, error: 'Email sending failed' }
    }

    logSecurityEvent('verification_resent', {
      email: sanitizeEmailForLogging(email),
      userId: user.id
    }, ip)

    return { success: true }
  } catch (error) {
    console.error('[SECURITY] Unexpected error handling existing unverified user:', error)
    logSecurityEvent('unexpected_error', {
      email: sanitizeEmailForLogging(email),
      error: error instanceof Error ? error.message : 'Unknown error'
    }, ip)
    return { success: false, error: 'Unexpected error' }
  }
}

// Secure new user creation handler
async function createNewUser(
  email: string, 
  password: string, 
  firstName: string, 
  lastName: string, 
  phone: string | undefined,
  baseUrl: string,
  ip: string
): Promise<{
  success: boolean
  error?: string
  user?: any
}> {
  try {
    const supabaseAdmin = createSupabaseAdminClient()

    // Create user with email confirmation required but don't send Supabase's email
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      user_metadata: {
        first_name: firstName,
        last_name: lastName,
        phone: phone || null,
        user_type: 'candidate'
      },
      email_confirm: false // We'll handle email verification ourselves
    })

    if (authError) {
      console.error('[SECURITY] User creation failed:', authError)
      logSecurityEvent('user_creation_failed', {
        email: sanitizeEmailForLogging(email),
        error: authError.message,
        code: authError.status
      }, ip)
      return { success: false, error: authError.message }
    }

    const user = authData.user
    if (!user) {
      logSecurityEvent('user_creation_no_user', {
        email: sanitizeEmailForLogging(email)
      }, ip)
      return { success: false, error: 'User creation returned no user' }
    }

    // Create verification token
    const { data: tokenData, error: tokenError } = await supabaseAdmin.rpc('create_verification_token' as any, {
      p_user_id: user.id,
      p_email: email,
      p_first_name: firstName
    })

    if (tokenError) {
      console.error('[SECURITY] Token creation failed for new user:', tokenError)
      logSecurityEvent('token_creation_failed_new_user', {
        email: sanitizeEmailForLogging(email),
        userId: user.id,
        error: tokenError.message
      }, ip)
      return { success: false, error: 'Token creation failed' }
    }

    const verificationToken = tokenData as string

    // Send verification email
    const emailResult = await sendVerificationEmail({
      to: email,
      firstName: firstName,
      token: verificationToken,
      baseUrl: baseUrl,
      config: emailConfig
    })

    if (!emailResult.success) {
      console.error('[SECURITY] Email sending failed for new user:', emailResult.error)
      logSecurityEvent('email_send_failed_new_user', {
        email: sanitizeEmailForLogging(email),
        userId: user.id,
        error: emailResult.error
      }, ip)
      return { success: false, error: 'Email sending failed' }
    }

    logSecurityEvent('user_registered', {
      email: sanitizeEmailForLogging(email),
      userId: user.id
    }, ip)

    return { success: true, user }
  } catch (error) {
    console.error('[SECURITY] Unexpected error creating new user:', error)
    logSecurityEvent('unexpected_error_new_user', {
      email: sanitizeEmailForLogging(email),
      error: error instanceof Error ? error.message : 'Unknown error'
    }, ip)
    return { success: false, error: 'Unexpected error occurred' }
  }
}

export const actions: Actions = {
  register: async ({ request, url }) => {
    const ip = getClientIP(request)
    const formData = await request.formData()
    const rawData = Object.fromEntries(formData)

    try {
      // Validate input data
      const { email, password, first_name, last_name, phone } = registrationSchema.parse(rawData)
      
      // Check rate limits BEFORE any processing
      const rateLimitResult = checkRegistrationLimit(ip, email)
      
      if (!rateLimitResult.allowed) {
        const waitMinutes = Math.ceil((rateLimitResult.resetTime - Date.now()) / (60 * 1000))
        
        logSecurityEvent('rate_limit_exceeded', {
          email: sanitizeEmailForLogging(email),
          blockedBy: rateLimitResult.blockedBy,
          waitMinutes
        }, ip)

        return fail(429, {
          success: false,
          error: `Too many registration attempts. Please wait ${waitMinutes} minutes before trying again.`,
          retry_after: rateLimitResult.resetTime,
          rate_limited: true
        })
      }

      // Check if user already exists
      const userStatus = await checkUserStatus(email)
      
      if (userStatus.error) {
        logSecurityEvent('user_status_check_failed', {
          email: sanitizeEmailForLogging(email),
          error: userStatus.error
        }, ip)
        
        return fail(500, {
          success: false,
          error: 'A technical issue occurred. Please try again later.',
          retry_suggested: true
        })
      }

      // Handle existing verified user - provide helpful UX
      if (userStatus.exists && userStatus.isVerified) {
        logSecurityEvent('registration_attempt_verified_user', {
          email: sanitizeEmailForLogging(email)
        }, ip)

        // Provide helpful guidance for verified users
        return {
          success: false,
          email: email,
          error: 'An account with this email already exists and is verified.',
          user_exists: true,
          suggestions: {
            login: true,
            forgot_password: true
          },
          message: 'You can sign in with your existing account or reset your password if you forgot it.'
        }
      }

      // Handle existing unverified user
      if (userStatus.exists && !userStatus.isVerified && userStatus.user) {
        const result = await handleExistingUnverifiedUser(
          userStatus.user, 
          email, 
          first_name, 
          url.origin, 
          ip
        )

        if (!result.success) {
          return fail(500, {
            success: false,
            error: 'Could not process your registration. Please try again later.',
            retry_suggested: true
          })
        }

        return {
          success: true,
          email: email,
          message: 'A new verification email has been sent to your email address.',
          existing_unverified: true
        }
      }

      // Create new user
      const createResult = await createNewUser(
        email, 
        password, 
        first_name, 
        last_name, 
        phone, 
        url.origin, 
        ip
      )

      if (!createResult.success) {
        // Handle specific errors
        if (createResult.error?.includes('email_exists') || createResult.error?.includes('already been registered')) {
          // This shouldn't happen due to our check above, but handle it gracefully
          logSecurityEvent('race_condition_user_exists', {
            email: sanitizeEmailForLogging(email),
            error: createResult.error
          }, ip)

          return {
            success: true,
            email: email,
            message: 'If this email is not already registered, you will receive a verification email shortly.',
            generic_response: true
          }
        }

        return fail(500, {
          success: false,
          error: 'Registration failed due to a technical issue. Please try again later.',
          retry_suggested: true
        })
      }

      return {
        success: true,
        email: email,
        message: 'Registration successful! Please check your email for verification instructions.',
        new_user: true
      }

    } catch (error) {
      if (error instanceof ZodError) {
        const fieldErrors = error.flatten().fieldErrors
        
        logSecurityEvent('validation_error', {
          email: rawData.email ? sanitizeEmailForLogging(rawData.email as string) : 'no-email',
          errors: Object.keys(fieldErrors)
        }, ip)

        return fail(400, {
          success: false,
          error: 'Please correct the errors below.',
          field_errors: fieldErrors
        })
      }

      console.error('[SECURITY] Unexpected registration error:', error)
      logSecurityEvent('unexpected_registration_error', {
        email: rawData.email ? sanitizeEmailForLogging(rawData.email as string) : 'no-email',
        error: error instanceof Error ? error.message : 'Unknown error'
      }, ip)

      return fail(500, {
        success: false,
        error: 'An unexpected error occurred. Please try again later.',
        retry_suggested: true
      })
    }
  },

  resend_verification: async ({ request, url }) => {
    const ip = getClientIP(request)
    const formData = await request.formData()
    const email = formData.get('email') as string

    if (!email) {
      logSecurityEvent('resend_verification_no_email', {}, ip)
      return fail(400, { 
        success: false, 
        error: 'Email is required to resend verification.',
        email 
      })
    }

    // Check rate limits
    const rateLimitResult = checkRegistrationLimit(ip, email)
    
    if (!rateLimitResult.allowed) {
      const waitMinutes = Math.ceil((rateLimitResult.resetTime - Date.now()) / (60 * 1000))
      
      logSecurityEvent('resend_rate_limit_exceeded', {
        email: sanitizeEmailForLogging(email),
        waitMinutes
      }, ip)

      return fail(429, {
        success: false,
        error: `Too many attempts. Please wait ${waitMinutes} minutes before trying again.`,
        email,
        retry_after: rateLimitResult.resetTime
      })
    }
    
    // Always return a generic success message to prevent email enumeration
    const genericSuccessResponse = { 
      success: true, 
      email, 
      message: 'If a matching unverified account exists, a new verification link has been sent.' 
    }

    try {
      const userStatus = await checkUserStatus(email)
      
      // Only process if user exists and is unverified
      if (userStatus.exists && !userStatus.isVerified && userStatus.user) {
        const result = await handleExistingUnverifiedUser(
          userStatus.user,
          email,
          userStatus.user.user_metadata?.first_name || 'there',
          url.origin,
          ip
        )

        if (result.success) {
          logSecurityEvent('verification_email_resent', {
            email: sanitizeEmailForLogging(email)
          }, ip)
        }
      } else {
        logSecurityEvent('resend_verification_no_action', {
          email: sanitizeEmailForLogging(email),
          reason: userStatus.exists ? (userStatus.isVerified ? 'already_verified' : 'no_user') : 'no_user'
        }, ip)
      }

      return genericSuccessResponse

    } catch (error) {
      console.error('[SECURITY] Unexpected error during resend:', error)
      logSecurityEvent('resend_verification_error', {
        email: sanitizeEmailForLogging(email),
        error: error instanceof Error ? error.message : 'Unknown error'
      }, ip)

      return genericSuccessResponse // Don't leak error info
    }
  }
}
