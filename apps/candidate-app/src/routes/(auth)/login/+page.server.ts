import { fail, redirect } from '@sveltejs/kit'
import { createSupabaseServerClient } from '$lib/supabase'

export const load = async ({ url }) => {
  const error = url.searchParams.get('error')
  let errorMessage = null
  
  if (error === 'confirmation_failed') {
    errorMessage = 'Email confirmation failed. Please try registering again or contact support.'
  }
  
  return {
    error: errorMessage
  }
}

export const actions = {
  login: async ({ request, cookies }) => {
    const supabase = createSupabaseServerClient({
      get: (name: string) => cookies.get(name),
      set: (name: string, value: string, options: any) => cookies.set(name, value, options),
      remove: (name: string, options: any) => cookies.delete(name, options)
    })

    const formData = await request.formData()
    const email = formData.get('email') as string
    const password = formData.get('password') as string

    if (!email || !password) {
      return fail(400, {
        error: 'Email and password are required',
        email
      })
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) {
      console.error('Auth error:', error)
      return fail(400, {
        error: 'Invalid email or password',
        email
      })
    }

    // Check if user has a candidate profile
    const { data: candidateData, error: candidateError } = await supabase
      .from('candidates')
      .select('id, first_name, last_name, email')
      .eq('auth_user_id', data.user.id)
      .single()

    if (candidateError && candidateError.code === 'PGRST116') {
      // No candidate profile exists - create a basic one to allow dashboard access
      const userData = data.user.user_metadata
      const firstName = userData.first_name || ''
      const lastName = userData.last_name || ''
      const fullName = `${firstName} ${lastName}`.trim() || data.user.email?.split('@')[0] || 'User'

      const { error: createError } = await supabase
        .from('candidates')
        .insert({
          auth_user_id: data.user.id,
          first_name: firstName,
          last_name: lastName,
          name: fullName,
          email: data.user.email,
          phone: null, // Phone is optional - can be completed later in profile settings
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (createError) {
        console.error('Failed to create candidate profile during login:', createError)
        // Only redirect to complete-profile if we truly can't create a basic profile
        throw redirect(302, '/complete-profile')
      }
    }

    // Always redirect to dashboard - profile completion can happen later in settings
    throw redirect(302, '/dashboard')
  }
}
