<script lang="ts">
	import '../app.css'
	import { invalidateAll } from '$app/navigation'
	import { onMount } from 'svelte'
	import type { LayoutData } from './$types'

	let { data, children }: { data: LayoutData; children: any } = $props()

	let { supabase } = $derived(data)

	onMount(() => {
		const {
			data: { subscription }
		} = supabase.auth.onAuthStateChange((event) => {
			if (event === 'SIGNED_IN' || event === 'SIGNED_OUT') {
				invalidateAll()
			}
		})

		return () => subscription.unsubscribe()
	})
</script>

<svelte:head>
	<title>Candidate Portal - ProcureServe</title>
</svelte:head>

<main class="min-h-screen bg-background">
	{@render children()}
</main>
