import { fail, redirect } from '@sveltejs/kit'
import { createSupabaseServerClient } from '$lib/supabase'
import type { Actions } from './$types'

export const actions: Actions = {
  default: async ({ request, cookies }) => {
    const formData = await request.formData()
    const password = formData.get('password') as string
    const confirmPassword = formData.get('confirm_password') as string

    if (!password || !confirmPassword) {
      return fail(400, {
        error: 'Both password fields are required.'
      })
    }

    if (password !== confirmPassword) {
      return fail(400, {
        error: 'Passwords do not match.'
      })
    }

    if (password.length < 8) {
        return fail(400, {
            error: 'Password must be at least 8 characters long.'
        })
    }

    const supabase = createSupabaseServerClient(cookies)

    const { error } = await supabase.auth.updateUser({ password })

    if (error) {
      return fail(500, {
        error: 'Failed to update password. Please try again.'
      })
    }
    
    // Invalidate the password recovery session by signing out
    await supabase.auth.signOut()
    
    return { success: true }
  }
} 