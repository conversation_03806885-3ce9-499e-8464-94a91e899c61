<script lang="ts">
  import { onMount } from 'svelte'
  import { enhance } from '$app/forms'
  import { goto } from '$app/navigation'
  import { createSupabaseBrowserClient } from '$lib/supabase'
  import Button from '$lib/components/ui/button.svelte'
  import Input from '$lib/components/ui/input.svelte'
  import Label from '$lib/components/ui/label.svelte'
  import Card from '$lib/components/ui/card.svelte'
  import CardHeader from '$lib/components/ui/card-header.svelte'
  import CardContent from '$lib/components/ui/card-content.svelte'
  import CardTitle from '$lib/components/ui/card-title.svelte'
  import { KeyRound, AlertCircle, CheckCircle, RotateCcw } from 'lucide-svelte'
  import type { ActionData } from './$types'

  let { form }: { form: ActionData } = $props()

  let loading = $state(false)
  let sessionActive = $state(false)
  let message = $state('')

  const supabase = createSupabaseBrowserClient()

  onMount(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'PASSWORD_RECOVERY') {
        sessionActive = true
      } else if (session) {
        sessionActive = true
      }
    })

    return () => {
      authListener.subscription.unsubscribe()
    }
  })

  $effect(() => {
    if (form?.success) {
      message = 'Password updated successfully! Redirecting to login...'
      setTimeout(() => {
        goto('/login')
      }, 3000)
    }
  })
</script>

<svelte:head>
  <title>Update Password - Candidate Portal</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full">
    {#if sessionActive}
      <Card>
        <CardHeader class="text-center">
          <div
            class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-primary mb-4"
          >
            <KeyRound class="h-6 w-6 text-primary-foreground" />
          </div>
          <CardTitle>Set a New Password</CardTitle>
        </CardHeader>
        <CardContent>
          {#if form?.success}
            <div class="rounded-lg bg-green-50 border border-green-200 p-4 text-center">
              <CheckCircle class="h-8 w-8 text-green-500 mx-auto mb-2" />
              <p class="text-green-800">{message}</p>
            </div>
          {:else}
            <form
              use:enhance={() => {
                loading = true
                return async ({ update }) => {
                  await update()
                  loading = false
                }
              }}
              method="POST"
              class="space-y-6"
            >
              {#if form?.error}
                <div class="rounded-md bg-red-50 p-4 border border-red-200">
                  <div class="flex items-center">
                    <AlertCircle class="h-5 w-5 text-red-400" />
                    <div class="ml-3">
                      <p class="text-sm text-red-800">{form.error}</p>
                    </div>
                  </div>
                </div>
              {/if}

              <div class="space-y-2">
                <Label for="password">New Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  required
                  placeholder="Enter your new password"
                />
              </div>
              <div class="space-y-2">
                <Label for="confirm_password">Confirm New Password</Label>
                <Input
                  id="confirm_password"
                  name="confirm_password"
                  type="password"
                  required
                  placeholder="Confirm your new password"
                />
              </div>
              <Button type="submit" class="w-full" disabled={loading}>
                {#if loading}
                  <RotateCcw class="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                {:else}
                  Update Password
                {/if}
              </Button>
            </form>
          {/if}
        </CardContent>
      </Card>
    {:else}
      <Card class="text-center">
        <CardContent class="p-8">
          <AlertCircle class="h-10 w-10 text-yellow-500 mx-auto mb-4" />
          <h3 class="text-lg font-semibold mb-2">Invalid or Expired Link</h3>
          <p class="text-muted-foreground">
            This password reset link is either invalid or has expired. Please request a new one.
          </p>
          <Button href="/forgot-password" class="mt-6 w-full">Request New Link</Button>
        </CardContent>
      </Card>
    {/if}
  </div>
</div> 