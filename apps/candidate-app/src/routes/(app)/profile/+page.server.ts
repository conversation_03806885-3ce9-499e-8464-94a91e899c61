import { redirect } from '@sveltejs/kit'
import type { PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ parent }) => {
  // Get data from parent layout (which already handles authentication)
  const { user, candidateProfile } = await parent()

  // If no profile exists, redirect to complete profile
  if (!candidateProfile) {
    throw redirect(303, '/complete-profile')
  }

  return {
    candidateProfile,
    user
  }
}
