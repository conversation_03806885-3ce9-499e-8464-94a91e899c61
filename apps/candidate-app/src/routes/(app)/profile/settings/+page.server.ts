import { fail, redirect } from '@sveltejs/kit'
import type { Actions, PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ parent }) => {
  // Get data from parent layout (which already handles authentication)
  const { candidateProfile } = await parent()

  return {
    candidateProfile
  }
}

export const actions: Actions = {
  updateProfile: async ({ request, locals }) => {
    const { supabase, user } = locals
    
    if (!user) {
      return fail(401, { error: 'Unauthorized' })
    }

    const formData = await request.formData()
    const firstName = formData.get('first_name') as string
    const lastName = formData.get('last_name') as string
    const email = formData.get('email') as string
    const phone = formData.get('phone') as string
    const location = formData.get('location') as string
    const bio = formData.get('bio') as string
    const skills = formData.get('skills') as string
    const experienceYears = formData.get('experience_years') as string

    try {
      const fullName = `${firstName} ${lastName}`.trim()
      const { error } = await supabase
        .from('candidates')
        .update({
          first_name: firstName,
          last_name: lastName,
          name: fullName, // Update the computed name field
          email,
          phone: phone || null,
          location: location || null,
          bio: bio || null,
          skills: skills || null,
          experience_years: experienceYears || null,
          updated_at: new Date().toISOString()
        })
        .eq('auth_user_id', user.id)

      if (error) {
        console.error('Profile update error:', error)
        return fail(400, { error: 'Failed to update profile' })
      }

      return { success: true, message: 'Profile updated successfully' }
    } catch (error) {
      console.error('Profile update error:', error)
      return fail(500, { error: 'An unexpected error occurred' })
    }
  },

  updateWorkAuth: async ({ request, locals }) => {
    const { supabase, user } = locals
    
    if (!user) {
      return fail(401, { error: 'Unauthorized' })
    }

    const formData = await request.formData()
    const workAuthorization = formData.get('work_authorization') as string
    const visaExpiry = formData.get('visa_expiry') as string
    const canRelocate = formData.get('can_relocate') === 'on'
    const preferredLocations = formData.get('preferred_locations') as string

    try {
      const { error } = await supabase
        .from('candidates')
        .update({
          work_authorization: workAuthorization,
          visa_expiry: visaExpiry || null,
          can_relocate: canRelocate,
          preferred_locations: preferredLocations || null,
          updated_at: new Date().toISOString()
        })
        .eq('auth_user_id', user.id)

      if (error) {
        console.error('Work auth update error:', error)
        return fail(400, { error: 'Failed to update work authorization' })
      }

      return { success: true, message: 'Work authorization updated successfully' }
    } catch (error) {
      console.error('Work auth update error:', error)
      return fail(500, { error: 'An unexpected error occurred' })
    }
  },

  updateAgency: async ({ request, locals }) => {
    const { supabase, user } = locals
    
    if (!user) {
      return fail(401, { error: 'Unauthorized' })
    }

    const formData = await request.formData()
    const isSelfRepresented = formData.get('is_self_represented') === 'on'
    const agencyContactEmail = formData.get('agency_contact_email') as string

    try {
      const { error } = await supabase
        .from('candidates')
        .update({
          is_self_represented: isSelfRepresented,
          agency_contact_email: isSelfRepresented ? null : agencyContactEmail,
          updated_at: new Date().toISOString()
        })
        .eq('auth_user_id', user.id)

      if (error) {
        console.error('Agency update error:', error)
        return fail(400, { error: 'Failed to update agency information' })
      }

      return { success: true, message: 'Agency information updated successfully' }
    } catch (error) {
      console.error('Agency update error:', error)
      return fail(500, { error: 'An unexpected error occurred' })
    }
  },

  updatePassword: async ({ request, locals }) => {
    const { supabase, user } = locals
    
    if (!user) {
      return fail(401, { error: 'Unauthorized' })
    }

    const formData = await request.formData()
    const currentPassword = formData.get('current_password') as string
    const newPassword = formData.get('new_password') as string
    const confirmPassword = formData.get('confirm_password') as string

    if (!currentPassword || !newPassword || !confirmPassword) {
      return fail(400, { error: 'All password fields are required' })
    }

    if (newPassword !== confirmPassword) {
      return fail(400, { error: 'New passwords do not match' })
    }

    if (newPassword.length < 6) {
      return fail(400, { error: 'Password must be at least 6 characters long' })
    }

    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      })

      if (error) {
        console.error('Password update error:', error)
        return fail(400, { error: 'Failed to update password' })
      }

      return { success: true, message: 'Password updated successfully' }
    } catch (error) {
      console.error('Password update error:', error)
      return fail(500, { error: 'An unexpected error occurred' })
    }
  }
}
