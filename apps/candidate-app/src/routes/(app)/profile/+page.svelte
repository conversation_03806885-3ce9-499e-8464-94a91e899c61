<script lang="ts">
  import { User, Mail, Phone, MapPin, Briefcase, Calendar, Edit } from 'lucide-svelte'
  import type { PageData } from './$types'
  
  let { data }: { data: PageData } = $props()
  let { candidateProfile } = $derived(data)
  
  let isEditing = $state(false)
  let editForm = $state({
    first_name: candidateProfile?.first_name || '',
    last_name: candidateProfile?.last_name || '',
    email: candidateProfile?.email || '',
    phone: candidateProfile?.phone || '',
    location: candidateProfile?.location || '',
    bio: candidateProfile?.bio || '',
    skills: candidateProfile?.skills || '',
    experience_years: candidateProfile?.experience_years || '',
    work_authorization: candidateProfile?.work_authorization || 'us_citizen'
  })
  
  function startEditing() {
    isEditing = true
    editForm = {
      first_name: candidateProfile?.first_name || '',
      last_name: candidateProfile?.last_name || '',
      email: candidateProfile?.email || '',
      phone: candidateProfile?.phone || '',
      location: candidateProfile?.location || '',
      bio: candidateProfile?.bio || '',
      skills: candidateProfile?.skills || '',
      experience_years: candidateProfile?.experience_years || '',
      work_authorization: candidateProfile?.work_authorization || 'us_citizen'
    }
  }
  
  function cancelEditing() {
    isEditing = false
  }
  
  function saveProfile(event: Event) {
    event.preventDefault()
    // TODO: Implement save functionality
    console.log('Saving profile:', editForm)
    isEditing = false
  }
</script>

<svelte:head>
  <title>My Profile - Candidate Portal</title>
</svelte:head>

<div class="space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">My Profile</h1>
      <p class="text-gray-600">Manage your professional information</p>
    </div>
    {#if !isEditing}
      <button
        onclick={startEditing}
        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <Edit class="w-4 h-4 mr-2" />
        Edit Profile
      </button>
    {/if}
  </div>

  {#if isEditing}
    <!-- Edit Form -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <form onsubmit={saveProfile} class="space-y-6">
        <!-- Basic Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">
                First Name
              </label>
              <input
                id="first_name"
                type="text"
                bind:value={editForm.first_name}
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">
                Last Name
              </label>
              <input
                id="last_name"
                type="text"
                bind:value={editForm.last_name}
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                id="email"
                type="email"
                bind:value={editForm.email}
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">
                Phone
              </label>
              <input
                id="phone"
                type="tel"
                bind:value={editForm.phone}
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div class="md:col-span-2">
              <label for="location" class="block text-sm font-medium text-gray-700 mb-1">
                Location
              </label>
              <input
                id="location"
                type="text"
                bind:value={editForm.location}
                placeholder="City, State"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        <!-- Professional Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Professional Information</h3>
          <div class="space-y-4">
            <div>
              <label for="bio" class="block text-sm font-medium text-gray-700 mb-1">
                Bio
              </label>
              <textarea
                id="bio"
                rows="4"
                bind:value={editForm.bio}
                placeholder="Tell us about yourself and your professional background..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              ></textarea>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="skills" class="block text-sm font-medium text-gray-700 mb-1">
                  Skills (comma-separated)
                </label>
                <input
                  id="skills"
                  type="text"
                  bind:value={editForm.skills}
                  placeholder="JavaScript, React, Node.js, Python..."
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label for="experience_years" class="block text-sm font-medium text-gray-700 mb-1">
                  Years of Experience
                </label>
                <select
                  id="experience_years"
                  bind:value={editForm.experience_years}
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select experience</option>
                  <option value="0-1">0-1 years</option>
                  <option value="2-3">2-3 years</option>
                  <option value="4-5">4-5 years</option>
                  <option value="6-10">6-10 years</option>
                  <option value="10+">10+ years</option>
                </select>
              </div>
            </div>
            <div>
              <label for="work_authorization" class="block text-sm font-medium text-gray-700 mb-1">
                Work Authorization
              </label>
              <select
                id="work_authorization"
                bind:value={editForm.work_authorization}
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="us_citizen">US Citizen</option>
                <option value="permanent_resident">Permanent Resident</option>
                <option value="h1b">H1B Visa</option>
                <option value="opt">OPT</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-3">
          <button
            type="button"
            onclick={cancelEditing}
            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Save Changes
          </button>
        </div>
      </form>
    </div>
  {:else}
    <!-- View Mode -->
    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <!-- Profile Header -->
      <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-8">
        <div class="flex items-center">
          <div class="w-20 h-20 bg-white rounded-full flex items-center justify-center">
            <User class="w-10 h-10 text-blue-600" />
          </div>
          <div class="ml-6 text-white">
            <h2 class="text-2xl font-bold">
              {candidateProfile?.first_name} {candidateProfile?.last_name}
            </h2>
            <p class="text-blue-100">
              {candidateProfile?.bio || 'Professional seeking new opportunities'}
            </p>
          </div>
        </div>
      </div>

      <!-- Profile Details -->
      <div class="p-6 space-y-6">
        <!-- Contact Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center">
              <Mail class="w-5 h-5 text-gray-400 mr-3" />
              <span class="text-gray-900">{candidateProfile?.email || 'Not provided'}</span>
            </div>
            <div class="flex items-center">
              <Phone class="w-5 h-5 text-gray-400 mr-3" />
              <span class="text-gray-900">{candidateProfile?.phone || 'Not provided'}</span>
            </div>
            <div class="flex items-center">
              <MapPin class="w-5 h-5 text-gray-400 mr-3" />
              <span class="text-gray-900">{candidateProfile?.location || 'Not provided'}</span>
            </div>
            <div class="flex items-center">
              <Briefcase class="w-5 h-5 text-gray-400 mr-3" />
              <span class="text-gray-900">{candidateProfile?.work_authorization || 'Not specified'}</span>
            </div>
          </div>
        </div>

        <!-- Professional Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Professional Information</h3>
          <div class="space-y-4">
            {#if candidateProfile?.bio}
              <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">About</h4>
                <p class="text-gray-900">{candidateProfile.bio}</p>
              </div>
            {/if}
            
            {#if candidateProfile?.skills}
              <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">Skills</h4>
                <div class="flex flex-wrap gap-2">
                  {#each candidateProfile.skills.split(',') as skill}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {skill.trim()}
                    </span>
                  {/each}
                </div>
              </div>
            {/if}
            
            {#if candidateProfile?.experience_years}
              <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">Experience</h4>
                <div class="flex items-center">
                  <Calendar class="w-4 h-4 text-gray-400 mr-2" />
                  <span class="text-gray-900">{candidateProfile.experience_years} years</span>
                </div>
              </div>
            {/if}
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>
