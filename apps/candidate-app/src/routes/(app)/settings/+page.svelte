<script lang="ts">
  import { User, <PERSON>, <PERSON>, Pa<PERSON>, Eye } from 'lucide-svelte'
  import Card from '$lib/components/ui/card.svelte'
  import CardHeader from '$lib/components/ui/card-header.svelte'
  import CardContent from '$lib/components/ui/card-content.svelte'
  import CardTitle from '$lib/components/ui/card-title.svelte'
  import Button from '$lib/components/ui/button.svelte'
  import Input from '$lib/components/ui/input.svelte'
  import Label from '$lib/components/ui/label.svelte'
  
  let notificationSettings = {
    emailNotifications: true,
    jobAlerts: true,
    applicationUpdates: true,
    marketingEmails: false
  }
  
  let privacySettings = {
    profileVisibility: 'public',
    showEmail: false,
    showPhone: false
  }
  
  let appearanceSettings = {
    theme: 'light',
    compactMode: false
  }
  
  function saveSettings() {
    // Handle settings save
    console.log('Settings saved')
  }
</script>

<svelte:head>
  <title>Settings - Candidate Portal</title>
</svelte:head>

<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="border-b border-gray-200 pb-4">
    <h1 class="text-2xl font-bold text-gray-900">Settings</h1>
    <p class="text-gray-600 mt-1">Manage your account preferences and privacy settings</p>
  </div>

  <div class="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
    <!-- Notification Settings -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Bell class="w-5 h-5" />
          Notifications
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <div>
              <Label for="email-notifications" class="text-sm font-medium">Email Notifications</Label>
              <p class="text-xs text-gray-500">Receive notifications via email</p>
            </div>
            <input
              id="email-notifications"
              type="checkbox"
              bind:checked={notificationSettings.emailNotifications}
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <Label for="job-alerts" class="text-sm font-medium">Job Alerts</Label>
              <p class="text-xs text-gray-500">Get notified about new job opportunities</p>
            </div>
            <input
              id="job-alerts"
              type="checkbox"
              bind:checked={notificationSettings.jobAlerts}
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <Label for="application-updates" class="text-sm font-medium">Application Updates</Label>
              <p class="text-xs text-gray-500">Status changes on your applications</p>
            </div>
            <input
              id="application-updates"
              type="checkbox"
              bind:checked={notificationSettings.applicationUpdates}
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <Label for="marketing-emails" class="text-sm font-medium">Marketing Emails</Label>
              <p class="text-xs text-gray-500">Promotional content and updates</p>
            </div>
            <input
              id="marketing-emails"
              type="checkbox"
              bind:checked={notificationSettings.marketingEmails}
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Privacy Settings -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Shield class="w-5 h-5" />
          Privacy
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-3">
          <div>
            <Label for="profile-visibility" class="text-sm font-medium">Profile Visibility</Label>
            <select
              id="profile-visibility"
              bind:value={privacySettings.profileVisibility}
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="public">Public</option>
              <option value="limited">Limited</option>
              <option value="private">Private</option>
            </select>
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <Label for="show-email" class="text-sm font-medium">Show Email Address</Label>
              <p class="text-xs text-gray-500">Display email on public profile</p>
            </div>
            <input
              id="show-email"
              type="checkbox"
              bind:checked={privacySettings.showEmail}
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <Label for="show-phone" class="text-sm font-medium">Show Phone Number</Label>
              <p class="text-xs text-gray-500">Display phone on public profile</p>
            </div>
            <input
              id="show-phone"
              type="checkbox"
              bind:checked={privacySettings.showPhone}
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Appearance Settings -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Palette class="w-5 h-5" />
          Appearance
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-3">
          <div>
            <Label for="theme" class="text-sm font-medium">Theme</Label>
            <select
              id="theme"
              bind:value={appearanceSettings.theme}
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="system">System</option>
            </select>
          </div>
          
          <div class="flex items-center justify-between">
            <div>
              <Label for="compact-mode" class="text-sm font-medium">Compact Mode</Label>
              <p class="text-xs text-gray-500">Use less spacing for better mobile experience</p>
            </div>
            <input
              id="compact-mode"
              type="checkbox"
              bind:checked={appearanceSettings.compactMode}
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Account Settings -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <User class="w-5 h-5" />
          Account
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-3">
          <div>
            <Label for="change-password" class="text-sm font-medium">Change Password</Label>
            <div class="mt-1 space-y-2">
              <Input type="password" placeholder="Current password" />
              <Input type="password" placeholder="New password" />
              <Input type="password" placeholder="Confirm new password" />
            </div>
          </div>
          
          <div class="pt-3 border-t">
            <Button variant="outline" class="w-full text-red-600 border-red-300 hover:bg-red-50">
              Deactivate Account
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>

  <!-- Save Settings -->
  <div class="flex justify-end pt-6 border-t">
    <Button onclick={saveSettings} class="px-6 py-2">
      Save Settings
    </Button>
  </div>
</div> 