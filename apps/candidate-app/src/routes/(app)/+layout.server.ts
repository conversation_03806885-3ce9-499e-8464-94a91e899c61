import { redirect } from '@sveltejs/kit'

export const load = async ({ locals, url }) => {
  const { supabase, user } = locals

  // Redirect unauthenticated users to login
  if (!user) {
    throw redirect(302, '/login')
  }

  // Get candidate profile if user is authenticated
  let candidateProfile = null
  if (user) {
    const { data, error } = await supabase
      .from('candidates')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()

    candidateProfile = data

    // If no profile exists, try to create a basic one instead of blocking access
    if (error && error.code === 'PGRST116') {
      // Only redirect to complete-profile if we're on a page that specifically needs profile data
      // For most pages, we'll create a basic profile on-the-fly
      if (url.pathname !== '/complete-profile' && url.pathname !== '/profile' && url.pathname !== '/settings') {
        // Create basic profile to allow dashboard access
        const userData = user.user_metadata
        const firstName = userData.first_name || ''
        const lastName = userData.last_name || ''
        const fullName = `${firstName} ${lastName}`.trim() || user.email?.split('@')[0] || 'User'

        const { data: newProfile, error: createError } = await supabase
          .from('candidates')
          .insert({
            auth_user_id: user.id,
            first_name: firstName,
            last_name: lastName,
            name: fullName,
            email: user.email,
            phone: null, // Phone is optional
            status: 'active',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single()

        if (!createError) {
          candidateProfile = newProfile
        } else {
          console.error('Failed to create basic candidate profile:', createError)
          // Only redirect to complete-profile as last resort
          throw redirect(302, '/complete-profile')
        }
      } else if (url.pathname === '/profile' || url.pathname === '/settings') {
        // For profile-specific pages, redirect to complete-profile
        throw redirect(302, '/complete-profile')
      }
    }
  }

  return {
    user,
    candidateProfile,
    pathname: url.pathname
  }
}
