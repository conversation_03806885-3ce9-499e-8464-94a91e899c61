<script>
  // Simple test page to verify Tailwind CSS is working
</script>

<svelte:head>
  <title>Style Test - Candidate Portal</title>
</svelte:head>

<div class="min-h-screen bg-gray-100 p-8">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-4xl font-bold text-blue-600 mb-8">Tailwind CSS Test Page</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Test Card 1 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Colors Test</h2>
        <div class="space-y-2">
          <div class="w-full h-4 bg-red-500 rounded"></div>
          <div class="w-full h-4 bg-green-500 rounded"></div>
          <div class="w-full h-4 bg-blue-500 rounded"></div>
          <div class="w-full h-4 bg-yellow-500 rounded"></div>
        </div>
      </div>

      <!-- Test Card 2 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Typography Test</h2>
        <p class="text-sm text-gray-600 mb-2">Small text</p>
        <p class="text-base text-gray-700 mb-2">Base text</p>
        <p class="text-lg text-gray-800 mb-2">Large text</p>
        <p class="text-xl font-bold text-gray-900">Extra large bold</p>
      </div>

      <!-- Test Card 3 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Buttons Test</h2>
        <div class="space-y-3">
          <button class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors">
            Primary Button
          </button>
          <button class="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded hover:bg-gray-300 transition-colors">
            Secondary Button
          </button>
          <button class="w-full border border-blue-600 text-blue-600 py-2 px-4 rounded hover:bg-blue-50 transition-colors">
            Outline Button
          </button>
        </div>
      </div>

      <!-- Test Card 4 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Form Elements</h2>
        <div class="space-y-3">
          <input 
            type="text" 
            placeholder="Text input"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option>Select option</option>
            <option>Option 1</option>
            <option>Option 2</option>
          </select>
          <textarea 
            placeholder="Textarea"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          ></textarea>
        </div>
      </div>

      <!-- Test Card 5 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Spacing Test</h2>
        <div class="space-y-4">
          <div class="p-2 bg-blue-100 rounded">Padding 2</div>
          <div class="p-4 bg-green-100 rounded">Padding 4</div>
          <div class="p-6 bg-yellow-100 rounded">Padding 6</div>
        </div>
      </div>

      <!-- Test Card 6 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Flexbox Test</h2>
        <div class="flex items-center justify-between mb-4">
          <span class="text-sm">Left</span>
          <span class="text-sm">Center</span>
          <span class="text-sm">Right</span>
        </div>
        <div class="flex flex-col space-y-2">
          <div class="bg-blue-200 p-2 rounded">Item 1</div>
          <div class="bg-blue-300 p-2 rounded">Item 2</div>
          <div class="bg-blue-400 p-2 rounded text-white">Item 3</div>
        </div>
      </div>
    </div>

    <div class="mt-8 text-center">
      <a href="/" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
        ← Back to Home
      </a>
    </div>
  </div>
</div>
