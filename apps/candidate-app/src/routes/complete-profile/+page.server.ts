import { fail, redirect } from '@sveltejs/kit'
import type { PageServerLoad, Actions } from './$types'

export const load: PageServerLoad = async ({ locals }) => {
  const { supabase, user } = locals
  
  if (!user) {
    throw redirect(302, '/login')
  }

  // Check if user already has a candidate profile
  const { data: candidateData } = await supabase
    .from('candidates')
    .select('id')
    .eq('auth_user_id', user.id)
    .single()

  if (candidateData) {
    // User already has a profile, redirect to dashboard
    throw redirect(302, '/dashboard')
  }

  // Return user data for pre-filling form
  return {
    user
  }
}

export const actions: Actions = {
  complete: async ({ request, locals }) => {
    const { supabase, user } = locals
    
    if (!user) {
      return fail(401, { error: 'Unauthorized' })
    }

    const formData = await request.formData()
    const firstName = formData.get('first_name') as string
    const lastName = formData.get('last_name') as string
    const phone = formData.get('phone') as string

    // Validation
    if (!firstName || !lastName) {
      return fail(400, {
        error: 'First name and last name are required'
      })
    }

    try {
      const fullName = `${firstName} ${lastName}`.trim()
      
      const { error: profileError } = await supabase
        .from('candidates')
        .insert({
          auth_user_id: user.id,
          first_name: firstName,
          last_name: lastName,
          name: fullName,
          email: user.email,
          phone: phone || null,
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (profileError) {
        console.error('Profile creation error:', profileError)
        return fail(400, {
          error: 'Failed to create candidate profile. Please try again.'
        })
      }

      // Success - redirect to dashboard
      throw redirect(303, '/dashboard')

    } catch (error) {
      console.error('Profile completion error:', error)
      return fail(500, {
        error: 'An unexpected error occurred. Please try again.'
      })
    }
  }
} 