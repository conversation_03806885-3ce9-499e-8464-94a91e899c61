<script lang="ts">
  import { enhance } from '$app/forms'
  import { UserCheck, AlertCircle } from 'lucide-svelte'
  import Button from '$lib/components/ui/button.svelte'
  import Input from '$lib/components/ui/input.svelte'
  import Label from '$lib/components/ui/label.svelte'
  import Card from '$lib/components/ui/card.svelte'
  import CardHeader from '$lib/components/ui/card-header.svelte'
  import CardContent from '$lib/components/ui/card-content.svelte'
  import CardTitle from '$lib/components/ui/card-title.svelte'
  import type { ActionData, PageData } from './$types'

  let { form, data }: { form: ActionData, data: PageData } = $props()

  let loading = $state(false)
  
  // Pre-fill form with user data
  let formData = $state({
    first_name: data.user?.user_metadata?.first_name || '',
    last_name: data.user?.user_metadata?.last_name || '',
    phone: ''
  })
</script>

<svelte:head>
  <title>Complete Profile - Candidate Portal</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <div class="mx-auto w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-4">
        <UserCheck class="w-6 h-6 text-primary-foreground" />
      </div>
      <h2 class="text-3xl font-bold text-foreground">Complete Your Profile</h2>
      <p class="mt-2 text-sm text-muted-foreground">
        Just a few more details to get started
      </p>
    </div>

    <!-- Error Message -->
    {#if form?.error}
      <div class="rounded-md bg-red-50 border border-red-200 p-4">
        <div class="flex">
          <AlertCircle class="h-5 w-5 text-red-400 flex-shrink-0" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Profile Error</h3>
            <div class="mt-2 text-sm text-red-700">
              {form.error}
            </div>
          </div>
        </div>
      </div>
    {/if}

    <!-- Profile Completion Form -->
    <Card>
      <CardHeader>
        <CardTitle>Complete Your Profile</CardTitle>
      </CardHeader>
      <CardContent>
        <form
          method="POST"
          action="?/complete"
          use:enhance={() => {
            loading = true
            return async ({ update }) => {
              loading = false
              await update()
            }
          }}
          class="space-y-6"
        >
          <div class="space-y-4">
            <!-- Name Fields -->
            <div class="grid grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label for="first_name">First Name</Label>
                <Input
                  id="first_name"
                  name="first_name"
                  type="text"
                  required
                  bind:value={formData.first_name}
                  placeholder="John"
                  disabled={loading}
                />
              </div>
              <div class="space-y-2">
                <Label for="last_name">Last Name</Label>
                <Input
                  id="last_name"
                  name="last_name"
                  type="text"
                  required
                  bind:value={formData.last_name}
                  placeholder="Doe"
                  disabled={loading}
                />
              </div>
            </div>

            <!-- Email (read-only) -->
            <div class="space-y-2">
              <Label for="email">Email Address</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={data.user?.email || ''}
                disabled
                class="bg-gray-50 text-gray-500"
              />
              <p class="text-xs text-muted-foreground">Your verified email address</p>
            </div>

            <!-- Phone -->
            <div class="space-y-2">
              <Label for="phone">Phone Number (Optional)</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                bind:value={formData.phone}
                placeholder="(*************"
                disabled={loading}
              />
              <p class="text-xs text-muted-foreground">You can add this later in your profile settings</p>
            </div>
          </div>

          <!-- Submit Button -->
          <Button
            type="submit"
            disabled={loading || !formData.first_name || !formData.last_name}
            class="w-full"
          >
            {#if loading}
              <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
              Creating Profile...
            {:else}
              Complete Profile
            {/if}
          </Button>
        </form>
      </CardContent>
    </Card>

    <!-- Footer Links -->
    <div class="text-center space-y-4">
      <p class="text-sm text-muted-foreground">
        Having trouble?
        <a href="/login" class="text-primary hover:text-primary/80 font-medium">
          Sign in again
        </a>
      </p>
    </div>
  </div>
</div> 