import { createSupabaseServerClient } from '$lib/supabase'
import { redirect, type Handle } from '@sveltejs/kit'

const DEBUG_SECURITY = process.env.NODE_ENV === 'development'

function log(message: string, data?: any) {
  if (DEBUG_SECURITY) {
    console.log(`[CANDIDATE-SECURITY] ${message}`, data ? JSON.stringify(data, null, 2) : '')
  }
}

/**
 * Cross-app session validation to prevent authentication bleeding
 */
async function validateCandidateAppAccess(event: any, user: any): Promise<boolean> {
  if (!user) return false
  
  try {
    const supabase = createSupabaseServerClient({
      get: (name: string) => event.cookies.get(name),
      set: (name: string, value: string, options: any) => event.cookies.set(name, value, options),
      remove: (name: string, options: any) => event.cookies.delete(name, options)
    })

    // Check if user exists in other app tables (security breach detection)  
    const [consoleCheck, customerCheck] = await Promise.all([
      supabase.from('console_users').select('id').eq('id', user.id).single(),
      supabase.from('users').select('id').eq('auth_user_id', user.id).single()
    ])

    // If user is found in console or customer tables, this is a cross-app breach
    if (consoleCheck.data || customerCheck.data) {
      log('SECURITY BREACH DETECTED: Cross-app access attempt', {
        userId: user.id,
        email: user.email,
        foundInConsole: !!consoleCheck.data,
        foundInCustomers: !!customerCheck.data
      })
      
      // Clear all sessions to prevent further breach
      await supabase.auth.signOut()
      return false
    }

    // Check if user exists in candidates table (valid candidate user)
    const { data: candidateUser, error } = await supabase
      .from('candidates')
      .select('id, email')
      .eq('auth_user_id', user.id)
      .single()

    if (error || !candidateUser) {
      log('User not found in candidates table - this is expected for login page', {
        userId: user.id,
        email: user.email,
        error: error?.message
      })
      return false
    }

    return true
  } catch (error) {
    log('Error in cross-app validation:', error)
    return false
  }
}

export const handle: Handle = async ({ event, resolve }) => {
  const url = new URL(event.request.url)
  log(`Processing candidate request to: ${url.pathname}`)

  // Create Supabase client with app-specific session isolation
  event.locals.supabase = createSupabaseServerClient({
    get: (name: string) => event.cookies.get(name),
    set: (name: string, value: string, options: any) => event.cookies.set(name, value, options),
    remove: (name: string, options: any) => event.cookies.delete(name, options)
  })

  // SECURITY FIX: Use getUser() instead of getSession() for server-side validation
  const { data: { user }, error: authError } = await event.locals.supabase.auth.getUser()

  if (authError) {
    log('Auth error:', authError)
  }

  // CRITICAL SECURITY: Validate cross-app access
  let isValidCandidateAccess = true // Temporarily simplified for testing
  let candidateUser = null
  
  if (user) {
    // Get candidate user data 
    const { data: userData, error: userDataError } = await event.locals.supabase
      .from('candidates')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()

    if (!userDataError && userData) {
      candidateUser = userData
      log('Valid candidate user found:', { id: userData.id, email: userData.email })
    }
  }

  // Route classification for candidate app
  const isAuthPage = url.pathname.startsWith('/login') || 
                     url.pathname.startsWith('/register') || 
                     url.pathname.startsWith('/forgot-password') ||
                     url.pathname.startsWith('/reset-password') ||
                     url.pathname.startsWith('/auth/')
  const isPublicPage = url.pathname === '/' || url.pathname.startsWith('/test-styles')
  const isApiRoute = url.pathname.startsWith('/api/')
  const isDashboardRoute = url.pathname.startsWith('/dashboard') ||
                          url.pathname.startsWith('/profile') ||
                          url.pathname.startsWith('/settings') ||
                          url.pathname.startsWith('/jobs') ||
                          url.pathname.startsWith('/applications')

  log('Route analysis:', {
    isAuthPage,
    isPublicPage,
    isApiRoute,
    isDashboardRoute,
    hasUser: !!user,
    hasCandidateUser: !!candidateUser,
    validCandidateAccess: isValidCandidateAccess
  })

  // Set locals
  event.locals.user = user
  event.locals.candidateUser = candidateUser

  // Security middleware for candidate app
  
  // 1. Redirect authenticated users away from auth pages
  if (user && isAuthPage && !isApiRoute) {
    log('Redirecting authenticated user away from auth page')
    throw redirect(303, '/dashboard')
  }

  // 2. Redirect unauthenticated users to login (except public pages and API routes)
  if (!user && !isAuthPage && !isPublicPage && !isApiRoute) {
    log('Redirecting unauthenticated user to login')
    throw redirect(303, '/login')
  }

  // 3. Additional security headers
  const response = await resolve(event)
  
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('X-Candidate-App', 'true') // App identification header
  
  // CSP for candidate app
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co",
    "font-src 'self' https://fonts.gstatic.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ')
  
  response.headers.set('Content-Security-Policy', csp)

  log('Request processing complete')
  return response
}
