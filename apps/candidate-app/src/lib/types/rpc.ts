// RPC function types for custom Supabase functions

export interface CreateVerificationTokenParams {
  p_user_id: string
  p_email: string
  p_first_name: string
}

export interface CreateVerificationTokenResult {
  token: string
  expires_at: string
}

export interface VerifyEmailTokenParams {
  p_token: string
}

export interface VerifyEmailTokenResult {
  success: boolean
  message: string
  user_id?: string
  email?: string
}

// Extend the Supabase client type to include our custom RPC functions
declare module '@supabase/supabase-js' {
  interface SupabaseClient {
    rpc(
      fn: 'create_verification_token',
      args: CreateVerificationTokenParams
    ): Promise<{ data: CreateVerificationTokenResult | null; error: any }>
    
    rpc(
      fn: 'verify_email_token',
      args: VerifyEmailTokenParams
    ): Promise<{ data: VerifyEmailTokenResult | null; error: any }>
  }
}
