// Production-ready rate limiter for candidate app
// Handles 100,000+ users with security and performance in mind

interface RateLimitEntry {
  count: number
  resetTime: number
  lastAttempt: number
}

interface RateLimitConfig {
  windowMs: number      // Time window in milliseconds
  maxAttempts: number   // Max attempts per window
  blockDurationMs: number // How long to block after hitting limit
}

class RateLimiter {
  private cache = new Map<string, RateLimitEntry>()
  private cleanupInterval: NodeJS.Timeout | null = null

  constructor() {
    // Clean up expired entries every 10 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 10 * 60 * 1000)
  }

  private cleanup() {
    const now = Date.now()
    const keysToDelete: string[] = []
    
    for (const [key, entry] of this.cache.entries()) {
      // Remove entries that haven't been used in 24 hours
      if (now - entry.lastAttempt > 24 * 60 * 60 * 1000) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key))
    
    // Log cleanup for monitoring
    if (keysToDelete.length > 0) {
      console.log(`[RateLimiter] Cleaned up ${keysToDelete.length} expired entries`)
    }
  }

  checkLimit(identifier: string, config: RateLimitConfig): { 
    allowed: boolean
    remainingAttempts: number
    resetTime: number
    isBlocked: boolean
  } {
    const now = Date.now()
    const entry = this.cache.get(identifier)

    if (!entry) {
      // First attempt
      this.cache.set(identifier, {
        count: 1,
        resetTime: now + config.windowMs,
        lastAttempt: now
      })
      
      return {
        allowed: true,
        remainingAttempts: config.maxAttempts - 1,
        resetTime: now + config.windowMs,
        isBlocked: false
      }
    }

    // Update last attempt time
    entry.lastAttempt = now

    // Check if we're in a block period
    if (entry.count >= config.maxAttempts && now < entry.resetTime) {
      // Extend block time if they keep trying
      entry.resetTime = now + config.blockDurationMs
      
      return {
        allowed: false,
        remainingAttempts: 0,
        resetTime: entry.resetTime,
        isBlocked: true
      }
    }

    // Reset counter if window has expired
    if (now >= entry.resetTime) {
      entry.count = 1
      entry.resetTime = now + config.windowMs
      
      return {
        allowed: true,
        remainingAttempts: config.maxAttempts - 1,
        resetTime: entry.resetTime,
        isBlocked: false
      }
    }

    // Increment counter
    entry.count++

    if (entry.count > config.maxAttempts) {
      // Start blocking
      entry.resetTime = now + config.blockDurationMs
      
      return {
        allowed: false,
        remainingAttempts: 0,
        resetTime: entry.resetTime,
        isBlocked: true
      }
    }

    return {
      allowed: true,
      remainingAttempts: config.maxAttempts - entry.count,
      resetTime: entry.resetTime,
      isBlocked: false
    }
  }

  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.cache.clear()
  }
}

// Singleton instance
const rateLimiter = new RateLimiter()

// Rate limit configurations for different operations
export const RATE_LIMITS = {
  REGISTRATION: {
    windowMs: 15 * 60 * 1000,      // 15 minutes
    maxAttempts: 5,                 // 5 attempts per 15 minutes
    blockDurationMs: 60 * 60 * 1000 // Block for 1 hour after hitting limit
  },
  EMAIL_VERIFICATION: {
    windowMs: 5 * 60 * 1000,       // 5 minutes
    maxAttempts: 3,                 // 3 attempts per 5 minutes
    blockDurationMs: 30 * 60 * 1000 // Block for 30 minutes
  },
  PASSWORD_RESET: {
    windowMs: 60 * 60 * 1000,      // 1 hour
    maxAttempts: 3,                 // 3 attempts per hour
    blockDurationMs: 2 * 60 * 60 * 1000 // Block for 2 hours
  },
  LOGIN: {
    windowMs: 15 * 60 * 1000,      // 15 minutes
    maxAttempts: 10,                // 10 attempts per 15 minutes
    blockDurationMs: 30 * 60 * 1000 // Block for 30 minutes
  }
}

// Utility functions
export function checkRegistrationLimit(ip: string, email: string) {
  const ipResult = rateLimiter.checkLimit(`reg:ip:${ip}`, RATE_LIMITS.REGISTRATION)
  const emailResult = rateLimiter.checkLimit(`reg:email:${email}`, RATE_LIMITS.REGISTRATION)
  
  // Return the most restrictive result
  return {
    allowed: ipResult.allowed && emailResult.allowed,
    remainingAttempts: Math.min(ipResult.remainingAttempts, emailResult.remainingAttempts),
    resetTime: Math.max(ipResult.resetTime, emailResult.resetTime),
    isBlocked: ipResult.isBlocked || emailResult.isBlocked,
    blockedBy: ipResult.isBlocked ? 'IP' : emailResult.isBlocked ? 'Email' : null
  }
}

export function checkEmailVerificationLimit(ip: string, token: string) {
  const ipResult = rateLimiter.checkLimit(`verify:ip:${ip}`, RATE_LIMITS.EMAIL_VERIFICATION)
  const tokenResult = rateLimiter.checkLimit(`verify:token:${token}`, RATE_LIMITS.EMAIL_VERIFICATION)
  
  return {
    allowed: ipResult.allowed && tokenResult.allowed,
    remainingAttempts: Math.min(ipResult.remainingAttempts, tokenResult.remainingAttempts),
    resetTime: Math.max(ipResult.resetTime, tokenResult.resetTime),
    isBlocked: ipResult.isBlocked || tokenResult.isBlocked,
    blockedBy: ipResult.isBlocked ? 'IP' : tokenResult.isBlocked ? 'Token' : null
  }
}

export function checkPasswordResetLimit(ip: string, email: string) {
  const ipResult = rateLimiter.checkLimit(`reset:ip:${ip}`, RATE_LIMITS.PASSWORD_RESET)
  const emailResult = rateLimiter.checkLimit(`reset:email:${email}`, RATE_LIMITS.PASSWORD_RESET)
  
  return {
    allowed: ipResult.allowed && emailResult.allowed,
    remainingAttempts: Math.min(ipResult.remainingAttempts, emailResult.remainingAttempts),
    resetTime: Math.max(ipResult.resetTime, emailResult.resetTime),
    isBlocked: ipResult.isBlocked || emailResult.isBlocked,
    blockedBy: ipResult.isBlocked ? 'IP' : emailResult.isBlocked ? 'Email' : null
  }
}

export function checkLoginLimit(ip: string, email: string) {
  const ipResult = rateLimiter.checkLimit(`login:ip:${ip}`, RATE_LIMITS.LOGIN)
  const emailResult = rateLimiter.checkLimit(`login:email:${email}`, RATE_LIMITS.LOGIN)
  
  return {
    allowed: ipResult.allowed && emailResult.allowed,
    remainingAttempts: Math.min(ipResult.remainingAttempts, emailResult.remainingAttempts),
    resetTime: Math.max(ipResult.resetTime, emailResult.resetTime),
    isBlocked: ipResult.isBlocked || emailResult.isBlocked,
    blockedBy: ipResult.isBlocked ? 'IP' : emailResult.isBlocked ? 'Email' : null
  }
}

// Security logging utility
export function logSecurityEvent(event: string, details: any, ip?: string) {
  const timestamp = new Date().toISOString()
  const logEntry = {
    timestamp,
    event,
    ip,
    ...details
  }
  
  // In production, this should send to a proper logging service
  console.log(`[SECURITY] ${JSON.stringify(logEntry)}`)
}

export default rateLimiter 