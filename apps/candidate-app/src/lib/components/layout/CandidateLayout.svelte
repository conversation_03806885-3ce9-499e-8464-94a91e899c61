<script lang="ts">
  import { page } from '$app/stores'
  import { goto } from '$app/navigation'
  import { 
    Home, 
    Search, 
    FileText, 
    User,
    Settings,
    Bell,
    Menu,
    X,
    UserCheck
  } from 'lucide-svelte'
  
  export let user: any = null
  export let candidateProfile: any = null
  
  let sidebarOpen = false
  
  // Navigation items for candidates
  const navigationItems = [
    {
      section: 'Main',
      items: [
        { name: 'Dashboard', href: '/dashboard', icon: Home, active: $page.url.pathname === '/dashboard' },
        { name: 'Job Search', href: '/jobs', icon: Search, active: $page.url.pathname.startsWith('/jobs') },
        { name: 'Applications', href: '/applications', icon: FileText, active: $page.url.pathname.startsWith('/applications') },
      ]
    },
    {
      section: 'Profile',
      items: [
        { name: 'My Profile', href: '/profile', icon: User, active: $page.url.pathname.startsWith('/profile') },
        { name: 'Settings', href: '/settings', icon: Settings, active: $page.url.pathname.startsWith('/settings') },
      ]
    }
  ]
  
  function toggleSidebar() {
    sidebarOpen = !sidebarOpen
  }
  
  function closeSidebar() {
    sidebarOpen = false
  }
  
  function navigateTo(href: string) {
    goto(href)
    closeSidebar()
  }
  
  async function signOut() {
    const response = await fetch('/api/auth/signout', { method: 'POST' })
    if (response.ok) {
      goto('/')
    }
  }
  
  // Generate page title from route
  $: pageTitle = generatePageTitle($page.url.pathname)
  
  function generatePageTitle(pathname: string): string {
    const segments = pathname.split('/').filter(Boolean)
    if (segments.length === 0) return 'Dashboard'
    
    const titleMap: Record<string, string> = {
      'dashboard': 'Dashboard',
      'jobs': 'Job Search',
      'applications': 'My Applications',
      'profile': 'My Profile',
      'settings': 'Settings'
    }
    
    return titleMap[segments[segments.length - 1]] || segments[segments.length - 1]
  }
</script>

<div class="min-h-screen bg-gray-50">
  <!-- Sidebar -->
  <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform {sidebarOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0">
    <!-- Header -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200">
      <div class="flex items-center gap-3">
        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
          <UserCheck class="w-5 h-5 text-white" />
        </div>
        <div>
          <h1 class="font-semibold text-gray-900">ProcureServe</h1>
          <p class="text-xs text-gray-500">Candidate Portal</p>
        </div>
      </div>
      
      <button
        on:click={toggleSidebar}
        class="lg:hidden p-1.5 rounded-md hover:bg-gray-100"
      >
        <X class="w-4 h-4" />
      </button>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 overflow-y-auto p-4 space-y-6">
      {#each navigationItems as section}
        <div>
          <h3 class="px-2 mb-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
            {section.section}
          </h3>
          
          <ul class="space-y-1">
            {#each section.items as item}
              <li>
                <button
                  on:click={() => navigateTo(item.href)}
                  class="w-full flex items-center gap-3 px-2 py-2 text-sm font-medium rounded-lg transition-colors
                    {item.active 
                      ? 'bg-blue-100 text-blue-700' 
                      : 'text-gray-700 hover:bg-gray-100'
                    }"
                >
                  <svelte:component this={item.icon} class="w-5 h-5 flex-shrink-0" />
                  <span>{item.name}</span>
                </button>
              </li>
            {/each}
          </ul>
        </div>
      {/each}
    </nav>

    <!-- User Profile Footer -->
    <div class="border-t border-gray-200 p-4">
      {#if user && candidateProfile}
        <div class="flex items-center gap-3 mb-3">
          <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <span class="text-sm font-medium text-gray-700">
              {candidateProfile.first_name?.charAt(0) || user.email.charAt(0).toUpperCase()}
            </span>
          </div>
          
          <div class="min-w-0 flex-1">
            <p class="text-sm font-medium text-gray-900 truncate">
              {candidateProfile.first_name ? `${candidateProfile.first_name} ${candidateProfile.last_name || ''}`.trim() : user.email}
            </p>
            <p class="text-xs text-gray-500 truncate">Candidate</p>
          </div>
        </div>
        
        <button
          on:click={signOut}
          class="w-full text-left px-2 py-1.5 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
        >
          Sign Out
        </button>
      {/if}
    </div>
  </div>

  <!-- Sidebar overlay for mobile -->
  {#if sidebarOpen}
    <div 
      class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
      on:click={closeSidebar}
    ></div>
  {/if}

  <!-- Main Content -->
  <div class="lg:ml-64">
    <!-- Top Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 px-4 lg:px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <!-- Mobile menu button -->
          <button
            on:click={toggleSidebar}
            class="lg:hidden p-2 rounded-md hover:bg-gray-100"
          >
            <Menu class="w-5 h-5" />
          </button>
          
          <h1 class="text-xl font-semibold text-gray-900">{pageTitle}</h1>
        </div>
        
        <!-- Header actions -->
        <div class="flex items-center gap-3">
          <button class="p-2 rounded-md hover:bg-gray-100" title="Notifications">
            <Bell class="w-5 h-5 text-gray-600" />
          </button>
        </div>
      </div>
    </header>
    
    <!-- Main content area -->
    <main class="p-4 lg:p-6">
      <slot />
    </main>
  </div>
</div>
