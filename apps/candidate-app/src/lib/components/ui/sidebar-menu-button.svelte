<script lang="ts">
  import { cn } from '$lib/utils'
  
  let className: string = ''
  export { className as class }
  export let isActive = false
  export let asChild = false
  export let size: 'default' | 'sm' | 'lg' = 'default'
  export let tooltip: string | undefined = undefined
  
  const sizes = {
    default: 'h-10 text-sm',
    sm: 'h-8 text-xs',
    lg: 'h-12 text-sm'
  }
</script>

{#if asChild}
  <slot />
{:else}
  <button
    class={cn(
      'peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground',
      sizes[size],
      isActive && 'bg-sidebar-accent font-medium text-sidebar-accent-foreground',
      className
    )}
    data-active={isActive}
    title={tooltip}
    {...$$restProps}
    on:click
  >
    <slot />
  </button>
{/if}
