<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte'
  import { browser } from '$app/environment'
  import { 
    User, 
    Settings, 
    Moon, 
    Sun, 
    Globe, 
    HelpCircle, 
    LogOut,
    Shield,
    Download,
    UserX,
    ChevronRight,
    X
  } from 'lucide-svelte'
  
  export let candidateProfile: any = null
  export let isOpen = false
  
  const dispatch = createEventDispatcher()
  
  let theme = 'system'
  let language = 'en'
  
  // Get user initials for avatar
  $: userInitials = candidateProfile 
    ? `${candidateProfile.first_name?.[0] || ''}${candidateProfile.last_name?.[0] || ''}`.toUpperCase()
    : 'U'
  
  const themes = [
    { value: 'light', label: 'Light', icon: Sun },
    { value: 'dark', label: 'Dark', icon: Moon },
    { value: 'system', label: 'System', icon: Settings }
  ]
  
  const languages = [
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Español' },
    { value: 'fr', label: 'Français' }
  ]
  
  function closeMenu() {
    isOpen = false
    dispatch('close')
  }
  
  function handleThemeChange(newTheme: string) {
    theme = newTheme
    // TODO: Implement theme switching logic
    dispatch('themeChange', newTheme)
  }
  
  function handleLanguageChange(newLanguage: string) {
    language = newLanguage
    // TODO: Implement language switching logic
    dispatch('languageChange', newLanguage)
  }
  
  function handleProfileSettings() {
    dispatch('profileSettings')
    closeMenu()
  }
  
  function handleDataExport() {
    dispatch('dataExport')
    closeMenu()
  }
  
  function handleAccountDeletion() {
    dispatch('accountDeletion')
    closeMenu()
  }
  
  function handleHelp() {
    dispatch('help')
    closeMenu()
  }
  
  function handleSignOut() {
    dispatch('signOut')
    closeMenu()
  }
  
  // Close menu on outside click - only in browser
  function handleOutsideClick(event: MouseEvent) {
    if (!browser || !event.target) return
    const target = event.target as Element
    if (!target.closest('.user-menu-container')) {
      closeMenu()
    }
  }
  
  // Handle keyboard events for accessibility
  function handleOverlayKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      closeMenu()
    }
  }
  
  onMount(() => {
    if (browser && isOpen) {
      document.addEventListener('click', handleOutsideClick)
      return () => document.removeEventListener('click', handleOutsideClick)
    }
  })
  
  // Reactive statement for browser-safe event listeners
  $: if (browser) {
    if (isOpen) {
      document.addEventListener('click', handleOutsideClick)
    } else {
      document.removeEventListener('click', handleOutsideClick)
    }
  }
</script>

{#if isOpen}
  <!-- Mobile overlay with proper accessibility -->
  <div 
    class="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 md:hidden" 
    role="button"
    tabindex="0"
    aria-label="Close menu"
    on:click={closeMenu}
    on:keydown={handleOverlayKeydown}
  ></div>
  
  <!-- Menu popup -->
  <div class="user-menu-container fixed bottom-4 left-4 right-4 md:absolute md:bottom-full md:left-0 md:right-auto md:mb-2 md:w-80 z-50">
    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-4 border-b border-gray-100 dark:border-gray-800">
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
            {userInitials}
          </div>
          <div class="flex-1 min-w-0">
            <p class="font-semibold text-gray-900 dark:text-white text-sm truncate">
              {candidateProfile?.first_name} {candidateProfile?.last_name}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
              {candidateProfile?.email}
            </p>
          </div>
        </div>
        <button 
          on:click={closeMenu}
          class="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors md:hidden"
          aria-label="Close menu"
        >
          <X class="w-4 h-4 text-gray-500" />
        </button>
      </div>
      
      <!-- Menu Items -->
      <div class="py-2">
        <!-- Profile Settings -->
        <button 
          on:click={handleProfileSettings}
          class="w-full flex items-center gap-3 px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left"
        >
          <User class="w-4 h-4 text-gray-500" />
          <span class="flex-1 text-sm text-gray-700 dark:text-gray-300">Profile Settings</span>
          <ChevronRight class="w-4 h-4 text-gray-400" />
        </button>
        
        <!-- Theme Selector -->
        <div class="px-4 py-2">
          <p class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">Appearance</p>
          <div class="grid grid-cols-3 gap-1">
            {#each themes as themeOption}
              <button
                on:click={() => handleThemeChange(themeOption.value)}
                class="flex flex-col items-center gap-1 p-2 rounded-md text-xs transition-colors {theme === themeOption.value ? 'bg-blue-50 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400' : 'hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400'}"
                aria-label="Set theme to {themeOption.label}"
              >
                <svelte:component this={themeOption.icon} class="w-4 h-4" />
                <span class="text-xs">{themeOption.label}</span>
              </button>
            {/each}
          </div>
        </div>
        
        <!-- Language Selector -->
        <div class="px-4 py-2">
          <p class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">Language</p>
          <select 
            bind:value={language}
            on:change={(e) => handleLanguageChange(e.currentTarget.value)}
            class="w-full px-3 py-2 text-sm bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
            aria-label="Select language"
          >
            {#each languages as lang}
              <option value={lang.value}>{lang.label}</option>
            {/each}
          </select>
        </div>
        
        <div class="border-t border-gray-100 dark:border-gray-800 my-2"></div>
        
        <!-- Help & Support -->
        <button 
          on:click={handleHelp}
          class="w-full flex items-center gap-3 px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left"
        >
          <HelpCircle class="w-4 h-4 text-gray-500" />
          <span class="flex-1 text-sm text-gray-700 dark:text-gray-300">Help & Support</span>
          <ChevronRight class="w-4 h-4 text-gray-400" />
        </button>
        
        <!-- Privacy & Compliance -->
        <div class="px-4 py-2">
          <p class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">Privacy & Data</p>
          
          <button 
            on:click={handleDataExport}
            class="w-full flex items-center gap-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md px-2 transition-colors text-left"
          >
            <Download class="w-4 h-4 text-gray-500" />
            <span class="flex-1 text-sm text-gray-700 dark:text-gray-300">Export My Data</span>
          </button>
          
          <button 
            on:click={handleAccountDeletion}
            class="w-full flex items-center gap-3 py-2 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md px-2 transition-colors text-left mt-1"
          >
            <UserX class="w-4 h-4 text-red-500" />
            <span class="flex-1 text-sm text-red-600 dark:text-red-400">Delete Account</span>
          </button>
        </div>
        
        <div class="border-t border-gray-100 dark:border-gray-800 my-2"></div>
        
        <!-- Sign Out -->
        <button 
          on:click={handleSignOut}
          class="w-full flex items-center gap-3 px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left"
        >
          <LogOut class="w-4 h-4 text-gray-500" />
          <span class="flex-1 text-sm text-gray-700 dark:text-gray-300">Sign Out</span>
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .user-menu-container {
    animation: slideUp 0.2s ease-out;
  }
  
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @media (max-width: 768px) {
    .user-menu-container {
      animation: slideUpMobile 0.3s ease-out;
    }
    
    @keyframes slideUpMobile {
      from {
        opacity: 0;
        transform: translateY(100%);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }
</style> 