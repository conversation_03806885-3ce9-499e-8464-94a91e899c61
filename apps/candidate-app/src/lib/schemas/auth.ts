import { z } from 'zod';

export const registrationSchema = z
  .object({
    first_name: z
      .string({ required_error: 'First name is required' })
      .min(2, { message: 'First name must be at least 2 characters' }),
    last_name: z
      .string({ required_error: 'Last name is required' })
      .min(2, { message: 'Last name must be at least 2 characters' }),
    email: z.string({ required_error: 'Email is required' }).email({ message: 'Invalid email address' }),
    phone: z.string().optional(),
    password: z
      .string({ required_error: 'Password is required' })
      .min(8, { message: 'Password must be at least 8 characters' })
      .regex(/[A-Z]/, { message: 'Password must contain an uppercase letter' })
      .regex(/[a-z]/, { message: 'Password must contain a lowercase letter' })
      .regex(/[0-9]/, { message: 'Password must contain a number' }),
    confirm_password: z.string({ required_error: 'Password confirmation is required' })
  })
  .refine((data) => data.password === data.confirm_password, {
    message: "Passwords don't match",
    path: ['confirm_password']
  });

export type RegistrationSchema = typeof registrationSchema; 