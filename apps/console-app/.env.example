# Console App Environment Configuration  
# Copy this file to .env.local and update with your values

# Supabase Configuration (Local Development)
PUBLIC_SUPABASE_URL=http://127.0.0.1:54331
PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# Console-specific configuration
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# Application Configuration
NODE_ENV=development
CONSOLE_APP_PORT=3005

# Database URL (for server-side operations)
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54332/postgres

# For production, replace with your actual Supabase project values:
# PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
# PUBLIC_SUPABASE_ANON_KEY=your-anon-key
# SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Optional: Console-specific features
# ENABLE_DEBUG_MODE=true
# LOG_LEVEL=debug

# Initial Admin Setup Configuration
# Set these for first-time setup, then remove for security
INITIAL_ADMIN_EMAIL=<EMAIL>
INITIAL_ADMIN_SETUP_TOKEN=PSIIy@231117
ENABLE_INITIAL_SETUP=true
