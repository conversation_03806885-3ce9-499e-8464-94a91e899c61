<!-- Enhanced Admin Dashboard with Escalation Analytics -->
<script lang="ts">
  import { onMount } from 'svelte';
  import { AlertTriangle, Clock, Users, TrendingUp, Mail, Settings } from 'lucide-svelte';

  let escalationAnalytics = {
    total_pending: 0,
    awaiting_company_admin: 0,
    escalated_to_console: 0,
    auto_denied_count: 0,
    average_resolution_days: 0,
    overdue_registrations: []
  };

  let processing = false;
  let lastProcessed: string | null = null;

  onMount(async () => {
    await loadEscalationAnalytics();
  });

  async function loadEscalationAnalytics() {
    try {
      const response = await fetch('/api/escalations');
      const result = await response.json();
      
      if (result.success) {
        escalationAnalytics = result.data;
      }
    } catch (error) {
      console.error('Failed to load escalation analytics:', error);
    }
  }

  async function processEscalations() {
    if (processing) return;
    
    processing = true;
    try {
      const response = await fetch('/api/escalations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'process_escalations' })
      });
      
      const result = await response.json();
      
      if (result.success) {
        lastProcessed = new Date().toLocaleString();
        await loadEscalationAnalytics();
        alert(result.message);
      } else {
        alert('Failed to process escalations');
      }
    } catch (error) {
      console.error('Error processing escalations:', error);
      alert('Error processing escalations');
    } finally {
      processing = false;
    }
  }

  async function manualEscalation(registrationId: string, reason?: string) {
    try {
      const response = await fetch('/api/escalations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          action: 'manual_escalation',
          registration_id: registrationId,
          reason: reason || 'Manual escalation by admin'
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        await loadEscalationAnalytics();
        alert('Registration escalated successfully');
      } else {
        alert('Failed to escalate registration');
      }
    } catch (error) {
      console.error('Error escalating registration:', error);
      alert('Error escalating registration');
    }
  }

  $: urgentCount = escalationAnalytics.overdue_registrations.length;
  $: totalPendingCount = escalationAnalytics.total_pending;
</script>

<svelte:head>
  <title>Escalation Dashboard - ProcureServe Console</title>
</svelte:head>

<div class="space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Escalation Dashboard</h1>
      <p class="text-gray-600 dark:text-gray-400">Manage registration escalations and automation</p>
    </div>
    
    <div class="flex gap-3">
      <button
        on:click={loadEscalationAnalytics}
        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
      >
        <TrendingUp class="w-4 h-4 mr-2" />
        Refresh Analytics
      </button>
      
      <button
        on:click={processEscalations}
        disabled={processing}
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
      >
        <Settings class="w-4 h-4 mr-2" />
        {processing ? 'Processing...' : 'Process Escalations'}
      </button>
    </div>
  </div>

  <!-- Key Metrics Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Total Pending -->
    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Clock class="h-6 w-6 text-gray-400" />
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Total Pending
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {totalPendingCount}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Awaiting Company Admin -->
    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Users class="h-6 w-6 text-yellow-400" />
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Awaiting Company Admin
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {escalationAnalytics.awaiting_company_admin}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Escalated to Console -->
    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <AlertTriangle class="h-6 w-6 text-orange-400" />
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Escalated to Console
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {escalationAnalytics.escalated_to_console}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Average Resolution Time -->
    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <TrendingUp class="h-6 w-6 text-green-400" />
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Avg. Resolution Time
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {escalationAnalytics.average_resolution_days} days
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Urgent Overdue Registrations -->
  {#if urgentCount > 0}
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
      <div class="flex items-center">
        <AlertTriangle class="h-5 w-5 text-red-400 mr-2" />
        <h3 class="text-lg font-medium text-red-800 dark:text-red-200">
          {urgentCount} Overdue Registration{urgentCount > 1 ? 's' : ''} Require Immediate Attention
        </h3>
      </div>
      <div class="mt-4 space-y-3">
        {#each escalationAnalytics.overdue_registrations.slice(0, 5) as registration}
          <div class="flex items-center justify-between bg-white dark:bg-gray-800 p-3 rounded border">
            <div class="flex-1">
              <div class="font-medium text-gray-900 dark:text-white">
                {registration.company_name}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                {registration.contact_person_name} • {registration.contact_person_email}
              </div>
              <div class="text-xs text-red-600 dark:text-red-400 mt-1">
                Submitted {Math.floor((new Date().getTime() - new Date(registration.created_at).getTime()) / (1000 * 60 * 60 * 24))} days ago
              </div>
            </div>
            <div class="flex gap-2">
              <button
                on:click={() => manualEscalation(registration.id)}
                class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700"
              >
                Escalate Now
              </button>
              <a
                href="/companies/pending/{registration.id}"
                class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
              >
                Review
              </a>
            </div>
          </div>
        {/each}
        {#if urgentCount > 5}
          <div class="text-center text-sm text-gray-600 dark:text-gray-400">
            And {urgentCount - 5} more overdue registrations...
          </div>
        {/if}
      </div>
    </div>
  {/if}

  <!-- Escalation Configuration -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white">Escalation Configuration</h3>
      <p class="text-sm text-gray-600 dark:text-gray-400">Current escalation rules and timing</p>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="space-y-2">
          <h4 class="font-medium text-gray-900 dark:text-white">Company Admin Timeout</h4>
          <p class="text-2xl font-bold text-blue-600">3 days</p>
          <p class="text-xs text-gray-600 dark:text-gray-400">
            Time before escalating EXISTING_CUSTOMER registrations to console
          </p>
        </div>
        <div class="space-y-2">
          <h4 class="font-medium text-gray-900 dark:text-white">Auto-Deny Timeout</h4>
          <p class="text-2xl font-bold text-red-600">14 days</p>
          <p class="text-xs text-gray-600 dark:text-gray-400">
            Total time before automatically denying registrations
          </p>
        </div>
        <div class="space-y-2">
          <h4 class="font-medium text-gray-900 dark:text-white">Reminder Schedule</h4>
          <p class="text-2xl font-bold text-green-600">Day 1, 2</p>
          <p class="text-xs text-gray-600 dark:text-gray-400">
            When reminder emails are sent to company admins
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white">Quick Actions</h3>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <a
          href="/companies/pending"
          class="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <Clock class="w-4 h-4 mr-2" />
          View All Pending
        </a>
        
        <a
          href="/email-config"
          class="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <Mail class="w-4 h-4 mr-2" />
          Email Settings
        </a>
        
        <a
          href="/settings"
          class="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <Settings class="w-4 h-4 mr-2" />
          System Settings
        </a>
      </div>
    </div>
  </div>
</div>
