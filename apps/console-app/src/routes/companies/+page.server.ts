import { ConsoleAuthManager } from '$lib/server/auth/console-auth'
import { redirect, error } from '@sveltejs/kit'

export const load = async ({ locals }) => {
  // Check if user is authenticated
  if (!locals.user || !locals.consoleUser) {
    throw redirect(302, '/login')
  }

  const authManager = new ConsoleAuthManager()
  
  // Check if user has permission to view companies
  const hasPermission = await authManager.validatePermission(
    locals.consoleUser.id,
    'companies',
    'read'
  )

  if (!hasPermission) {
    throw error(403, 'Insufficient permissions to access companies')
  }

  // Get companies based on user role and permissions
  let companies = []
  
  if (locals.consoleUser.role === 'super_admin') {
    // Super admins can see all companies
    const { data } = await locals.supabase
      .from('companies')
      .select('*')
      .order('name')
    
    companies = data || []
  } else {
    // Other users see only their assigned companies
    const companyIds = locals.consoleUser.company_ids || []
    if (companyIds.length > 0) {
      const { data } = await locals.supabase
        .from('companies')
        .select('*')
        .in('id', companyIds)
        .order('name')
      
      companies = data || []
    }
  }

  return {
    companies,
    userRole: locals.consoleUser.role,
    permissions: locals.consoleUser.permissions
  }
}
