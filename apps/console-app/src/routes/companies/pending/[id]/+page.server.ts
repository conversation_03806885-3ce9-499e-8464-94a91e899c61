			// 2.5. Generate secure activation token (with dynamic URL for development)
			const crypto = await import('crypto');
			const token = crypto.randomBytes(32).toString('hex');
			const hashedToken = crypto.createHash('sha256').update(token).digest('hex');
			const expiresAt = new Date();
			expiresAt.setDate(expiresAt.getDate() + 7);
			
			// Use environment variable or default to localhost for development
			const baseUrl = process.env.PUBLIC_CUSTOMER_APP_URL || 'http://localhost:3004';
			const activationUrl = `${baseUrl}/activate?token=${token}`;