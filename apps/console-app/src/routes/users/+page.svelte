<script lang="ts">
  import { enhance } from '$app/forms'
  import { goto } from '$app/navigation'
  import { addToast } from '$stores/toast'
  import type { PageData, ActionData } from './$types'
  import { 
    Users, Plus, Search, Filter, UserCheck, UserX, 
    Mail, Shield, Calendar, Building2, MoreVertical,
    Clock, Send, Edit, Eye, EyeOff, X
  } from 'lucide-svelte'

  export let data: PageData
  // svelte-ignore export_let_unused
    export let form: ActionData

  const { users, invitations, userRole, canInvite, canManage, companyMap } = data

  let searchQuery = ''
  let filterRole = 'all'
  let filterStatus = 'all'
  let showInvitations = true

  // Filter users based on search and filters
  $: filteredUsers = users.filter(user => {
    const matchesSearch = searchQuery === '' || 
      user.email.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesRole = filterRole === 'all' || user.role === filterRole
    const matchesStatus = filterStatus === 'all' || 
      (filterStatus === 'active' && user.is_active) ||
      (filterStatus === 'inactive' && !user.is_active)
    
    return matchesSearch && matchesRole && matchesStatus
  })

  function formatDate(dateString: string | null) {
    if (!dateString) return 'Never'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  function getRoleBadgeColor(role: string) {
    switch (role) {
      case 'super_admin': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300'
      case 'company_admin': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300'
      case 'company_manager': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
    }
  }

  let loadingResend = new Set() // Track loading state per invitation

  // Form enhancement handlers
  function handleResendInvitation(invitationId: string) {
    return async ({ result, update }: { result: any; update: any }) => {
      loadingResend.delete(invitationId)
      loadingResend = loadingResend // Trigger reactivity
      console.log('Resend invitation result:', result)
      
      if (result.type === 'success' && result.data?.success) {
        addToast({
          type: 'success',
          title: 'Invitation Resent',
          message: result.data?.message || 'Invitation has been resent successfully'
        })
      } else {
        addToast({
          type: 'error',
          title: 'Resend Failed',
          message: result.data?.error || 'Failed to resend invitation'
        })
      }
      await update()
    }
  }

  function handleCancelInvitation() {
    return async ({ result, update }: { result: any; update: any }) => {
      console.log('Cancel invitation result:', result)
      
      if (result.type === 'success' && result.data?.success) {
        addToast({
          type: 'success',
          title: 'Invitation Cancelled',
          message: result.data?.message || 'Invitation has been cancelled and the link is now invalid'
        })
      } else {
        addToast({
          type: 'error',
          title: 'Cancel Failed',
          message: result.data?.error || 'Failed to cancel invitation'
        })
      }
      
      // Always refresh the page data
      await update()
    }
  }

  function handleToggleStatus() {
    return async ({ result, update }: { result: any; update: any }) => {
      if (result.type === 'success' && result.data?.success) {
        addToast({
          type: 'success',
          title: 'Status Updated',
          message: 'User status has been updated successfully'
        })
      } else {
        addToast({
          type: 'error',
          title: 'Update Failed',
          message: result.data?.error || 'Failed to update user status'
        })
      }
      await update()
    }
  }

  function getStatusBadgeColor(isActive: boolean) {
    return isActive 
      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
      : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'
  }
</script>

<svelte:head>
  <title>ProcureServe Internal Users | Console</title>
</svelte:head>

<div class="space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-3xl font-bold">ProcureServe Internal Users</h1>
      <p class="text-muted-foreground">Manage internal staff access and permissions</p>
    </div>
    {#if canInvite}
      <a
        href="/users/invite"
        class="flex items-center px-4 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 rounded-md transition-colors"
      >
        <Plus class="w-4 h-4 mr-2" />
        Invite Internal User
      </a>
    {/if}
  </div>

  <!-- Search and Filters -->
  <div class="bg-card border rounded-lg p-4">
    <div class="flex flex-wrap items-center gap-4">
      <div class="relative flex-1 min-w-[300px]">
        <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
        <input
          type="text"
          placeholder="Search users by email..."
          bind:value={searchQuery}
          class="pl-10 pr-4 py-2 w-full text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
        />
      </div>
      
      <select
        bind:value={filterRole}
        class="px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
      >
        <option value="all">All Roles</option>
        <option value="super_admin">System Administrator</option>
        <option value="company_admin">Client Account Manager</option>
        <option value="company_manager">Client Support Specialist</option>
      </select>
      
      <select
        bind:value={filterStatus}
        class="px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
      >
        <option value="all">All Status</option>
        <option value="active">Active</option>
        <option value="inactive">Inactive</option>
      </select>
    </div>
  </div>

  <!-- Pending Invitations -->
  {#if invitations.length > 0 && showInvitations}
    <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-semibold text-amber-900 dark:text-amber-200 flex items-center">
          <Mail class="w-4 h-4 mr-2" />
          Pending Invitations ({invitations.length})
        </h3>
        <button
          on:click={() => showInvitations = false}
          class="text-amber-600 hover:text-amber-700 dark:text-amber-400"
        >
          <X class="w-4 h-4" />
        </button>
      </div>
      
      <div class="space-y-2">
        {#each invitations as invitation}
          <div class="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded border border-amber-100 dark:border-amber-900">
            <div class="flex items-center space-x-3">
              <Mail class="w-4 h-4 text-amber-600 dark:text-amber-400" />
              <div>
                <p class="text-sm font-medium">{invitation.email}</p>
                <p class="text-xs text-muted-foreground">
                  {invitation.role === 'super_admin' ? 'System Administrator' : 
                   invitation.role === 'company_admin' ? 'Account Manager' :
                   'Support Specialist'} • Expires {formatDate(invitation.expires_at)}
                </p>
              </div>
            </div>
            {#if canInvite}
              <div class="flex items-center space-x-2">
                <form 
                  method="POST" 
                  action="?/resendInvitation" 
                  use:enhance={() => {
                    loadingResend.add(invitation.id)
                    loadingResend = loadingResend // Trigger reactivity
                    return handleResendInvitation(invitation.id)
                  }}
                >
                  <input type="hidden" name="invitation_id" value={invitation.id} />
                  <button
                    type="submit"
                    disabled={loadingResend.has(invitation.id)}
                    class="text-xs text-amber-600 hover:text-amber-700 dark:text-amber-400 hover:underline disabled:opacity-50"
                  >
                    {#if loadingResend.has(invitation.id)}
                      Sending...
                    {:else}
                      Resend
                    {/if}
                  </button>
                </form>
                
                <!-- Cancel/Remove Invitation Button -->
                <form 
                  method="POST" 
                  action="?/cancelInvitation"
                  use:enhance={handleCancelInvitation}
                >
                  <input type="hidden" name="invitation_id" value={invitation.id} />
                  <button
                    type="submit"
                    class="text-xs text-red-600 hover:text-red-700 dark:text-red-400 hover:underline"
                    title="Cancel invitation"
                  >
                    Cancel
                  </button>
                </form>
              </div>
            {/if}
          </div>
        {/each}
      </div>
    </div>
  {/if}

  <!-- Users List -->
  <div class="bg-card border rounded-lg">
    <div class="px-6 py-4 border-b">
      <h2 class="text-lg font-semibold flex items-center">
        <Users class="w-5 h-5 mr-2" />
        Internal Users ({filteredUsers.length})
      </h2>
    </div>
    
    <div class="overflow-x-auto">
      {#if filteredUsers.length === 0}
        <div class="text-center py-12">
          <Users class="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 class="text-lg font-semibold mb-2">No users found</h3>
          <p class="text-muted-foreground mb-4">
            {searchQuery || filterRole !== 'all' || filterStatus !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Get started by inviting your first internal user'}
          </p>
          {#if canInvite && searchQuery === '' && filterRole === 'all' && filterStatus === 'all'}
            <a
              href="/users/invite"
              class="inline-flex items-center px-4 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 rounded-md transition-colors"
            >
              <Plus class="w-4 h-4 mr-2" />
              Invite Internal User
            </a>
          {/if}
        </div>
      {:else}
        <table class="w-full">
          <thead class="bg-muted/50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                User
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Role
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Client Accounts
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Last Login
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-border">
            {#each filteredUsers as user}
              <tr class="hover:bg-muted/30 transition-colors">
                <td class="px-6 py-4">
                  <div class="flex items-center">
                    <div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center mr-3">
                      <span class="text-sm font-medium text-primary">
                        {user.email.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <p class="text-sm font-medium">{user.email}</p>
                      <p class="text-xs text-muted-foreground">
                        ID: {user.id.slice(0, 8)}...
                      </p>
                    </div>
                  </div>
                </td>
                
                <td class="px-6 py-4">
                  <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full {getRoleBadgeColor(user.role)}">
                    {user.role === 'super_admin' ? 'SYSTEM ADMIN' : 
                     user.role === 'company_admin' ? 'ACCOUNT MANAGER' :
                     'SUPPORT SPECIALIST'}
                  </span>
                </td>
                
                <td class="px-6 py-4">
                  <div class="text-sm">
                    {#if user.company_ids?.length > 0}
                      {#each user.company_ids.slice(0, 2) as companyId}
                        <div class="text-xs">{companyMap[companyId] || companyId}</div>
                      {/each}
                      {#if user.company_ids.length > 2}
                        <div class="text-xs text-muted-foreground">+{user.company_ids.length - 2} more</div>
                      {/if}
                    {:else}
                      <span class="text-xs text-muted-foreground">All client accounts</span>
                    {/if}
                  </div>
                </td>
                
                <td class="px-6 py-4">
                  <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full {getStatusBadgeColor(user.is_active)}">
                    {user.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                
                <td class="px-6 py-4 text-sm text-muted-foreground">
                  {formatDate(user.last_login)}
                </td>
                
                <td class="px-6 py-4 text-right">
                  <div class="flex items-center justify-end space-x-2">
                    {#if canManage && user.id !== data.consoleUser?.id}
                      <form method="POST" action="?/toggleStatus" use:enhance={handleToggleStatus}>
                        <input type="hidden" name="user_id" value={user.id} />
                        <input type="hidden" name="current_status" value={user.is_active} />
                        <button
                          type="submit"
                          class="p-1 text-muted-foreground hover:text-foreground transition-colors"
                          title={user.is_active ? 'Deactivate user' : 'Activate user'}
                        >
                          {#if user.is_active}
                            <EyeOff class="w-4 h-4" />
                          {:else}
                            <Eye class="w-4 h-4" />
                          {/if}
                        </button>
                      </form>
                    {/if}
                    
                    <a
                      href="/users/{user.id}"
                      class="p-1 text-muted-foreground hover:text-foreground transition-colors"
                      title="View details"
                    >
                      <Edit class="w-4 h-4" />
                    </a>
                  </div>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      {/if}
    </div>
  </div>
</div> 