import { fail, redirect } from '@sveltejs/kit'
import { createSupabaseAdminClient } from '$lib/server/supabase-admin'
import { sendInvitationEmail } from '$lib/server/email'
import type { Actions, PageServerLoad } from './$types'
import type { ConsoleRole } from '$lib/types/auth.types'
import crypto from 'crypto'

// Helper function to get role display names
function getRoleDisplayName(role: ConsoleRole): string {
  const roleMap: Record<ConsoleRole, string> = {
    'super_admin': 'ProcureServe System Administrator',
    'company_admin': 'Client Account Manager', 
    'company_manager': 'Client Support Specialist'
  }
  return roleMap[role]
}

export const load: PageServerLoad = async ({ locals }) => {
  const { consoleUser, authManager } = locals

  if (!consoleUser) {
    throw redirect(302, '/login')
  }

  // Check permissions for user invitation
  const hasPermission = await authManager.validatePermission(
    consoleUser.id,
    'users',
    'invite'
  )

  if (!hasPermission) {
    throw redirect(302, '/users?error=insufficient_permissions')
  }

  // SECURITY: Console users are ProcureServe internal staff only
  // They don't belong to client companies - they manage ALL companies
  // No company selection needed for console user invitations
  
  return {
    consoleUser
  }
}

export const actions: Actions = {
  invite: async ({ request, locals, getClientAddress }) => {
    const { consoleUser, authManager } = locals

    if (!consoleUser) {
      return fail(401, { error: 'Unauthorized' })
    }

    // Check permissions
    const hasPermission = await authManager.validatePermission(
      consoleUser.id,
      'users',
      'invite'
    )

    if (!hasPermission) {
      return fail(403, { error: 'Insufficient permissions' })
    }

    try {
      const formData = await request.formData()
      const email = formData.get('email') as string
      const role = formData.get('role') as ConsoleRole

      // Validate required fields
      if (!email || !role) {
        return fail(400, { 
          error: 'Email and role are required',
          values: { email, role }
        })
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        return fail(400, { 
          error: 'Invalid email format',
          values: { email, role }
        })
      }

      // SECURITY: Validate email domain for internal users only
      const allowedDomains = ['procureserve.com']
      const emailDomain = email.split('@')[1]?.toLowerCase()

      if (!allowedDomains.includes(emailDomain)) {
        return fail(400, { 
          error: `Only ProcureServe internal email addresses are allowed. Use an email from: ${allowedDomains.join(', ')}`,
          values: { email, role }
        })
      }

      // Validate role
      const validRoles: ConsoleRole[] = ['super_admin', 'company_admin', 'company_manager']
      if (!validRoles.includes(role)) {
        return fail(400, { 
          error: 'Invalid role selected',
          values: { email, role }
        })
      }

      // Check role hierarchy - users can only invite roles lower than or equal to their own
      const roleHierarchy: Record<ConsoleRole, number> = {
        'super_admin': 3,
        'company_admin': 2,
        'company_manager': 1
      }

      if (consoleUser.role !== 'super_admin' && roleHierarchy[role] > roleHierarchy[consoleUser.role]) {
        return fail(403, { 
          error: 'Cannot invite users with higher role than your own',
          values: { email, role }
        })
      }

      const supabase = createSupabaseAdminClient()

      // Check if user already exists
      const { data: existingUser } = await supabase
        .from('console_users')
        .select('id, email, role')
        .eq('email', email)
        .single()

      if (existingUser) {
        return fail(400, { 
          error: `A user with email ${email} already exists`,
          values: { email, role }
        })
      }

      // Check if there's already a pending invitation
      const { data: existingInvitation } = await supabase
        .from('console_user_invitations')
        .select('id, status, expires_at')
        .eq('email', email)
        .eq('status', 'pending')
        .single()

      if (existingInvitation) {
        // Check if invitation is still valid
        const expiresAt = new Date(existingInvitation.expires_at)
        if (expiresAt > new Date()) {
          return fail(400, { 
            error: `A pending invitation already exists for ${email}`,
            values: { email, role }
          })
        } else {
          // Mark old invitation as expired
          await supabase
            .from('console_user_invitations')
            .update({ status: 'expired' })
            .eq('id', existingInvitation.id)
        }
      }

      // Create new invitation
      const invitationToken = crypto.randomBytes(32).toString('hex')
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 7) // 7 days expiry

      // Default permissions based on role (NO company-specific permissions)
      const defaultPermissions = getDefaultPermissions(role)

      const { data: invitation, error: inviteError } = await supabase
        .from('console_user_invitations')
        .insert({
          email,
          role,
          permissions: defaultPermissions,
          invited_by: consoleUser.id,
          token: invitationToken,
          expires_at: expiresAt.toISOString(),
          status: 'pending'
        })
        .select()
        .single()

      if (inviteError) {
        console.error('Error creating invitation:', inviteError)
        return fail(500, { 
          error: `Failed to create invitation: ${inviteError.message}`,
          values: { email, role }
        })
      }

      // Log the invitation creation
      await authManager.logSecurityEvent({
        event_type: 'user_invited',
        user_id: consoleUser.id,
        user_email: consoleUser.email,
        user_role: consoleUser.role,
        resource: 'console_users',
        resource_id: invitation.id,
        success: true,
        ip_address: getClientAddress(),
        metadata: {
          invited_email: email,
          invited_role: role
        },
        timestamp: new Date().toISOString()
      })

      // Send invitation email using Resend
      try {
        await sendInvitationEmail({
          email,
          role: getRoleDisplayName(role),
          inviterName: consoleUser.email, // Could be improved with actual name
          token: invitationToken,
          expiresAt: expiresAt.toISOString()
        })
        
        console.log(`Invitation email sent successfully to ${email}`)
      } catch (emailError) {
        console.error('Failed to send invitation email:', emailError)
        // Don't fail the invitation creation if email fails
        // But log it for monitoring
        await authManager.logSecurityEvent({
          event_type: 'email_send_failed',
          user_id: consoleUser.id,
          user_email: consoleUser.email,
          user_role: consoleUser.role,
          resource: 'console_user_invitations',
          resource_id: invitation.id,
          success: false,
          ip_address: getClientAddress(),
          metadata: {
            invited_email: email,
            error: emailError instanceof Error ? emailError.message : 'Unknown email error'
          },
          timestamp: new Date().toISOString()
        })
      }

      return {
        success: true,
        message: `Invitation sent successfully to ${email}`,
        invitation: {
          email,
          role: getRoleDisplayName(role),
          expiresAt: expiresAt.toISOString()
        }
      }

    } catch (error) {
      console.error('Invite action error:', error)
      return fail(500, { error: 'Internal server error while creating invitation' })
    }
  }
}

// Helper function to generate default permissions based on role
// SECURITY: All console users have GLOBAL permissions, never company-specific
function getDefaultPermissions(role: ConsoleRole) {
  const basePermissions = []

  switch (role) {
    case 'super_admin':
      // Super admins have full global access to manage the platform
      basePermissions.push(
        { resource: 'users', actions: ['create', 'read', 'update', 'delete', 'invite'] },
        { resource: 'companies', actions: ['create', 'read', 'update', 'delete'] },
        { resource: 'settings', actions: ['read', 'update'] },
        { resource: 'audit_logs', actions: ['read'] },
        { resource: 'enums', actions: ['create', 'read', 'update', 'delete'] }
      )
      break

    case 'company_admin':
      // Company admins can manage client companies but have global permissions
      basePermissions.push(
        { resource: 'users', actions: ['create', 'read', 'update', 'invite'] },
        { resource: 'companies', actions: ['read', 'update'] },
        { resource: 'settings', actions: ['read'] },
        { resource: 'enums', actions: ['read'] }
      )
      break

    case 'company_manager':
      // Company managers have limited global read access for support
      basePermissions.push(
        { resource: 'users', actions: ['read'] },
        { resource: 'companies', actions: ['read'] },
        { resource: 'settings', actions: ['read'] },
        { resource: 'enums', actions: ['read'] }
      )
      break
  }

  return basePermissions
}
