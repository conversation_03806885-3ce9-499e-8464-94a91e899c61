<script lang="ts">
  import { enhance } from '$app/forms'
  import { goto } from '$app/navigation'
  import { addToast } from '$stores/toast'
  import type { PageData, ActionData } from './$types'
  import type { ConsoleRole } from '$lib/types/auth.types'
  import { 
    ArrowLeft, UserPlus, Mail, Shield, Building2, 
    AlertCircle, CheckCircle, Info
  } from 'lucide-svelte'

  export let data: PageData
  export let form: ActionData

  const { consoleUser } = data

  // Form state
  let email = ''
  let role: ConsoleRole = 'company_manager'
  let selectedPermissions: string[] = []
  let loading = false

  // Available roles based on current user's role
  const availableRoles = (): Array<{ value: ConsoleRole; label: string; description: string }> => {
    const roles = [
      {
        value: 'company_manager' as ConsoleRole,
        label: 'Client Support Specialist',
        description: 'Provide support to all client accounts with read-only access'
      },
      {
        value: 'company_admin' as ConsoleRole,
        label: 'Client Account Manager',
        description: 'Manage all client accounts and internal users with elevated permissions'
      }
    ]

    if (consoleUser.role === 'super_admin') {
      roles.push({
        value: 'super_admin' as ConsoleRole,
        label: 'System Administrator',
        description: 'Full ProcureServe platform administration and internal team management'
      })
    }

    return roles
  }

  // Available permissions based on selected role
  const availablePermissions = (selectedRole: ConsoleRole) => {
    const basePermissions = [
      { resource: 'enums', actions: ['read'], label: 'View Enum Configurations' },
      { resource: 'users', actions: ['read'], label: 'View Console Users' },
      { resource: 'companies', actions: ['read'], label: 'View Client Companies' },
      { resource: 'settings', actions: ['read'], label: 'View Platform Settings' },
      { resource: 'audit_logs', actions: ['read'], label: 'View Audit Logs' }
    ]

    if (selectedRole === 'company_admin') {
      basePermissions.push(
        { resource: 'enums', actions: ['create', 'update', 'delete'], label: 'Manage Enum Configurations' },
        { resource: 'users', actions: ['invite', 'update'], label: 'Manage Console Users' },
        { resource: 'companies', actions: ['update'], label: 'Manage Client Companies' }
      )
    }

    if (selectedRole === 'super_admin') {
      basePermissions.push(
        { resource: 'enums', actions: ['create', 'update', 'delete'], label: 'Full Enum Management' },
        { resource: 'users', actions: ['create', 'update', 'delete', 'invite'], label: 'Full User Management' },
        { resource: 'companies', actions: ['create', 'update', 'delete'], label: 'Full Company Management' },
        { resource: 'settings', actions: ['update'], label: 'Platform Administration' }
      )
    }

    return basePermissions
  }

  // Update permissions when role changes
  $: {
    if (role) {
      // Reset permissions when role changes
      selectedPermissions = []
      
      // Auto-select default permissions based on role
      if (role === 'company_manager') {
        selectedPermissions = [
          'enums:read', 'users:read', 'companies:read', 
          'settings:read', 'audit_logs:read'
        ]
      } else if (role === 'company_admin') {
        selectedPermissions = [
          'enums:read', 'enums:create', 'enums:update', 'enums:delete',
          'users:read', 'users:invite', 'users:update',
          'companies:read', 'companies:update',
          'settings:read', 'audit_logs:read'
        ]
      } else if (role === 'super_admin') {
        // Super admins get all permissions by default
        selectedPermissions = availablePermissions(role).flatMap(p => 
          p.actions.map(a => `${p.resource}:${a}`)
        )
      }
    }
  }

  function togglePermission(resource: string, action: string) {
    const permission = `${resource}:${action}`
    if (selectedPermissions.includes(permission)) {
      selectedPermissions = selectedPermissions.filter(p => p !== permission)
    } else {
      selectedPermissions = [...selectedPermissions, permission]
    }
  }

  function isPermissionSelected(resource: string, action: string): boolean {
    return selectedPermissions.includes(`${resource}:${action}`)
  }

  // Handle form submission
  function handleSubmit() {
    loading = true
    return async ({ result, update }: { result: any; update: any }) => {
      loading = false
      
      if (result.type === 'success') {
        addToast({
          type: 'success',
          title: 'Invitation Sent',
          message: form?.message || `Invitation sent to ${email}`
        })
        goto('/users')
      } else if (result.type === 'failure') {
        addToast({
          type: 'error',
          title: 'Invitation Failed',
          message: form?.error || 'Failed to send invitation'
        })
      }
      
      await update()
    }
  }
</script>

<svelte:head>
  <title>Invite ProcureServe Internal User - Console</title>
</svelte:head>

<div class="space-y-6">
  <!-- Header -->
  <div class="flex items-center space-x-4">
    <button
      on:click={() => goto('/users')}
      class="p-2 hover:bg-accent rounded-md transition-colors"
    >
      <ArrowLeft class="w-5 h-5" />
    </button>
    <div>
      <h1 class="text-2xl font-bold">Invite ProcureServe Internal User</h1>
      <p class="text-muted-foreground mt-1">
        Send an invitation to grant internal console access to a ProcureServe employee
      </p>
    </div>
  </div>

  <!-- Form -->
  <form 
    method="POST" 
    action="?/invite" 
    use:enhance={handleSubmit}
    class="grid grid-cols-1 lg:grid-cols-3 gap-6"
  >
    <!-- Main Form -->
    <div class="lg:col-span-2 space-y-6">
      
      <!-- Basic Information -->
      <div class="bg-card border rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Mail class="w-5 h-5 mr-2" />
          User Information
        </h3>
        
        <div class="space-y-4">
          <!-- Email -->
          <div>
            <label for="email" class="block text-sm font-medium mb-2">
              ProcureServe Email Address *
            </label>
            <input
              id="email"
              type="email"
              name="email"
              bind:value={email}
              required
              placeholder="<EMAIL>"
              class="w-full px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            />
            <p class="text-xs text-muted-foreground mt-1">
              Must be a valid ProcureServe internal email address
            </p>
          </div>

          <!-- SECURITY NOTE: Console users don't belong to client companies -->
          <!-- They have global permissions to manage all companies -->

          <!-- Role -->
          <div>
            <label for="role" class="block text-sm font-medium mb-2">
              Role *
            </label>
            <select
              id="role"
              name="role"
              bind:value={role}
              required
              class="w-full px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            >
              {#each availableRoles() as roleOption}
                <option value={roleOption.value}>{roleOption.label}</option>
              {/each}
            </select>
            
            <!-- Role Description -->
            {#if role}
              {#each availableRoles() as roleOption}
                {#if roleOption.value === role}
                  <p class="text-xs text-muted-foreground mt-1">
                    {roleOption.description}
                  </p>
                {/if}
              {/each}
            {/if}
          </div>
        </div>
      </div>

      <!-- Permissions -->
      <div class="bg-card border rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Shield class="w-5 h-5 mr-2" />
          Permissions
        </h3>

        <div class="space-y-4">
          {#each availablePermissions(role) as permission}
            <div class="border rounded-lg p-4">
              <h4 class="font-medium mb-2">{permission.label}</h4>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                {#each permission.actions as action}
                  <label class="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      name="permissions"
                      value="{permission.resource}:{action}"
                      checked={isPermissionSelected(permission.resource, action)}
                      on:change={() => togglePermission(permission.resource, action)}
                      class="w-4 h-4 text-primary bg-background border-input rounded focus:ring-primary"
                    />
                    <span class="text-sm capitalize">{action}</span>
                  </label>
                {/each}
              </div>
            </div>
          {/each}
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
      
      <!-- Help -->
      <div class="bg-card border rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Info class="w-5 h-5 mr-2" />
          Invitation Process
        </h3>
        <div class="space-y-3 text-sm text-muted-foreground">
          <div class="flex items-start space-x-2">
            <CheckCircle class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <p>User will receive an email invitation</p>
          </div>
          <div class="flex items-start space-x-2">
            <CheckCircle class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <p>Invitation expires after 7 days</p>
          </div>
          <div class="flex items-start space-x-2">
            <CheckCircle class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <p>User sets their password on first login</p>
          </div>
          <div class="flex items-start space-x-2">
            <CheckCircle class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <p>Permissions take effect immediately</p>
          </div>
        </div>
      </div>

      <!-- Security Notice -->
      <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
        <div class="flex items-start space-x-2">
          <AlertCircle class="w-5 h-5 text-amber-600 dark:text-amber-400 flex-shrink-0" />
          <div class="text-sm">
            <p class="font-medium text-amber-900 dark:text-amber-200">Internal Access Only</p>
            <p class="text-amber-700 dark:text-amber-300 mt-1">
              Console access is restricted to ProcureServe employees only. Only invite verified internal staff members.
            </p>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="bg-card border rounded-lg p-6">
        <div class="space-y-3">
          <button
            type="submit"
            disabled={loading || !email || !role || selectedPermissions.length === 0}
            class="w-full flex items-center justify-center px-4 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
          >
            {#if loading}
              <div class="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin mr-2"></div>
              Sending Invitation...
            {:else}
              <UserPlus class="w-4 h-4 mr-2" />
              Send Invitation
            {/if}
          </button>
          
          <button
            type="button"
            on:click={() => goto('/users')}
            class="w-full flex items-center justify-center px-4 py-2 text-sm font-medium border border-input bg-background hover:bg-accent rounded-md transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </form>
</div> 