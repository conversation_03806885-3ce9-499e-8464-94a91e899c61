import { fail, redirect } from '@sveltejs/kit'
import { createSupabaseAdminClient } from '$lib/server/supabase-admin'
import type { Actions, PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ url }) => {
  const token = url.searchParams.get('token')

  if (!token) {
    throw redirect(303, '/login?error=invalid_token')
  }

  const supabase = createSupabaseAdminClient()

  // Validate invitation token
  const { data: invitation, error } = await supabase
    .from('console_user_invitations')
    .select('*')
    .eq('token', token)
    .eq('status', 'pending')
    .single()

  if (error || !invitation) {
    throw redirect(303, '/login?error=invalid_token')
  }

  // Check if invitation has expired
  if (new Date(invitation.expires_at) < new Date()) {
    throw redirect(303, '/login?error=invitation_expired')
  }

  // Validate email domain for ProcureServe internal users only
  const allowedDomains = process.env.CONSOLE_ALLOWED_EMAIL_DOMAINS?.split(',') || ['procureserve.com']
  const emailDomain = invitation.email.split('@')[1]?.toLowerCase()

  if (!allowedDomains.includes(emailDomain)) {
    throw redirect(303, '/login?error=invalid_domain')
  }

  return {
    invitation: {
      email: invitation.email,
      role: invitation.role,
      company_id: invitation.company_id,
      token: invitation.token
    }
  }
}

export const actions: Actions = {
  activate: async ({ request, cookies }) => {
    const formData = await request.formData()
    const token = formData.get('token') as string
    const password = formData.get('password') as string
    const confirmPassword = formData.get('confirm_password') as string

    // Validate inputs
    if (!token || !password || !confirmPassword) {
      return fail(400, { error: 'All fields are required' })
    }

    if (password !== confirmPassword) {
      return fail(400, { error: 'Passwords do not match' })
    }

    if (password.length < 8) {
      return fail(400, { error: 'Password must be at least 8 characters long' })
    }

    const supabase = createSupabaseAdminClient()

    try {
      // Get invitation details
      const { data: invitation, error: inviteError } = await supabase
        .from('console_user_invitations')
        .select('*')
        .eq('token', token)
        .eq('status', 'pending')
        .single()

      if (inviteError || !invitation) {
        return fail(400, { error: 'Invalid or expired invitation' })
      }

      // Check if invitation has expired
      if (new Date(invitation.expires_at) < new Date()) {
        return fail(400, { error: 'This invitation has expired' })
      }

      // Create user in Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: invitation.email,
        password: password,
        email_confirm: true,
        user_metadata: {
          console_user: true,
          invited_by: invitation.invited_by,
          role: invitation.role
        }
      })

      if (authError || !authData.user) {
        console.error('Failed to create auth user:', authError)
        return fail(500, { error: 'Failed to create user account' })
      }

      // Create console user record
      const { error: consoleUserError } = await supabase
        .from('console_users')
        .insert({
          id: authData.user.id,
          email: invitation.email,
          role: invitation.role,
          company_ids: [invitation.company_id],
          is_active: true,
          mfa_enabled: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (consoleUserError) {
        console.error('Failed to create console user:', consoleUserError)
        
        // Clean up auth user if console user creation failed
        await supabase.auth.admin.deleteUser(authData.user.id)
        
        return fail(500, { error: 'Failed to create console user record' })
      }

      // Create user permissions
      if (invitation.permissions && invitation.permissions.length > 0) {
        const permissionRecords = invitation.permissions.map((perm: any) => ({
          user_id: authData.user.id,
          resource: perm.resource,
          actions: perm.actions,
          company_id: perm.company_id
        }))

        const { error: permError } = await supabase
          .from('console_user_permissions')
          .insert(permissionRecords)

        if (permError) {
          console.error('Failed to create permissions:', permError)
        }
      }

      // Update invitation status to 'accepted'
      const { error: inviteUpdateError } = await supabase
        .from('console_user_invitations')
        .update({ 
          status: 'accepted'
          // updated_at will be handled by trigger
        })
        .eq('id', invitation.id)

      if (inviteUpdateError) {
        console.error('Failed to update invitation status:', inviteUpdateError)
        // Log this but don't fail the activation since user was created
      } else {
        console.log(`✅ Invitation marked as accepted for: ${invitation.email}`)
      }

      // Log security event
      await supabase
        .from('console_security_events')
        .insert({
          event_type: 'user_activated',
          user_id: authData.user.id,
          user_email: invitation.email,
          user_role: invitation.role,
          success: true,
          metadata: {
            invited_by: invitation.invited_by,
            activation_method: 'invitation'
          },
          timestamp: new Date().toISOString()
        })

      // Sign in the user
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: invitation.email,
        password: password
      })

      if (signInError) {
        // User created but sign in failed - they can login manually
        throw redirect(303, '/login?message=Account created successfully. Please sign in.')
      }

      // Redirect to dashboard
      throw redirect(303, '/dashboard')

    } catch (error) {
      if (error instanceof Response) {
        throw error // Re-throw redirects
      }

      console.error('Activation error:', error)
      return fail(500, { error: 'Failed to activate account' })
    }
  }
} 