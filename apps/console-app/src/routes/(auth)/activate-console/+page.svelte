<script lang="ts">
  import { enhance } from '$app/forms'
  import { Eye, EyeOff, Shield, AlertCircle, CheckCircle, UserPlus } from 'lucide-svelte'
  import type { PageData, ActionData } from './$types'

  export let data: PageData
  export let form: ActionData

  const { invitation } = data

  let showPassword = false
  let showConfirmPassword = false
  let loading = false
  let password = ''
  let confirmPassword = ''

  $: passwordsMatch = password === confirmPassword || confirmPassword === ''
  $: passwordValid = password.length >= 8

  function togglePasswordVisibility() {
    showPassword = !showPassword
  }

  function toggleConfirmPasswordVisibility() {
    showConfirmPassword = !showConfirmPassword
  }
</script>

<svelte:head>
  <title>Activate ProcureServe Internal Account</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <div class="mx-auto w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-4">
        <UserPlus class="w-6 h-6 text-primary-foreground" />
      </div>
      <h2 class="text-3xl font-bold text-foreground">Activate Your ProcureServe Account</h2>
      <p class="mt-2 text-sm text-muted-foreground">
        Set up your password to complete internal console access setup
      </p>
    </div>

    <!-- Invitation Details -->
    <div class="bg-card border rounded-lg p-4">
      <h3 class="text-sm font-medium mb-2">Invitation Details</h3>
      <div class="space-y-1 text-sm">
        <div class="flex justify-between">
          <span class="text-muted-foreground">Email:</span>
          <span class="font-medium">{invitation.email}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-muted-foreground">Role:</span>
          <span class="font-medium capitalize">{invitation.role.replace('_', ' ')}</span>
        </div>
      </div>
    </div>

    <!-- Error Message -->
    {#if form?.error}
      <div class="rounded-md bg-destructive/15 p-4">
        <div class="flex">
          <AlertCircle class="h-5 w-5 text-destructive" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-destructive">Error</h3>
            <div class="mt-2 text-sm text-destructive">
              {form.error}
            </div>
          </div>
        </div>
      </div>
    {/if}

    <!-- Activation Form -->
    <form 
      method="POST" 
      action="?/activate"
      use:enhance={() => {
        loading = true
        return async ({ update }) => {
          loading = false
          await update()
        }
      }}
      class="mt-8 space-y-6"
    >
      <input type="hidden" name="token" value={invitation.token} />
      
      <div class="space-y-4">
        <!-- Password -->
        <div class="form-field">
          <label for="password" class="form-label">
            Password
          </label>
          <div class="relative">
            <input
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              required
              bind:value={password}
              class="form-input pr-10"
              placeholder="Create a secure password"
              disabled={loading}
            />
            <button
              type="button"
              class="absolute inset-y-0 right-0 pr-3 flex items-center"
              on:click={togglePasswordVisibility}
              disabled={loading}
            >
              {#if showPassword}
                <EyeOff class="h-4 w-4 text-muted-foreground" />
              {:else}
                <Eye class="h-4 w-4 text-muted-foreground" />
              {/if}
            </button>
          </div>
          <div class="mt-2 space-y-1">
            <div class="flex items-center space-x-2 text-xs">
              {#if password.length > 0}
                {#if passwordValid}
                  <CheckCircle class="w-3 h-3 text-green-500" />
                  <span class="text-green-600 dark:text-green-400">At least 8 characters</span>
                {:else}
                  <AlertCircle class="w-3 h-3 text-amber-500" />
                  <span class="text-amber-600 dark:text-amber-400">Must be at least 8 characters</span>
                {/if}
              {:else}
                <span class="text-muted-foreground">Minimum 8 characters required</span>
              {/if}
            </div>
          </div>
        </div>

        <!-- Confirm Password -->
        <div class="form-field">
          <label for="confirm_password" class="form-label">
            Confirm Password
          </label>
          <div class="relative">
            <input
              id="confirm_password"
              name="confirm_password"
              type={showConfirmPassword ? 'text' : 'password'}
              required
              bind:value={confirmPassword}
              class="form-input pr-10"
              placeholder="Confirm your password"
              disabled={loading}
            />
            <button
              type="button"
              class="absolute inset-y-0 right-0 pr-3 flex items-center"
              on:click={toggleConfirmPasswordVisibility}
              disabled={loading}
            >
              {#if showConfirmPassword}
                <EyeOff class="h-4 w-4 text-muted-foreground" />
              {:else}
                <Eye class="h-4 w-4 text-muted-foreground" />
              {/if}
            </button>
          </div>
          {#if confirmPassword.length > 0 && !passwordsMatch}
            <p class="mt-1 text-xs text-destructive">Passwords do not match</p>
          {/if}
        </div>
      </div>

      <!-- Security Notice -->
      <div class="bg-muted/50 border rounded-lg p-4">
        <div class="flex items-start space-x-2">
          <Shield class="w-5 h-5 text-primary mt-0.5" />
          <div class="text-sm">
            <p class="font-medium">Internal Access Notice</p>
            <p class="text-muted-foreground mt-1">
              You're being granted ProcureServe internal administrative access. 
              Please choose a strong password and keep it secure.
            </p>
          </div>
        </div>
      </div>

      <!-- Submit Button -->
      <div>
        <button
          type="submit"
          disabled={loading || !passwordValid || !passwordsMatch || !password || !confirmPassword}
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {#if loading}
            <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            Activating Account...
          {:else}
            Activate Account
          {/if}
        </button>
      </div>
    </form>

    <!-- Footer -->
    <div class="text-center">
      <div class="text-sm">
        <a
          href="/login"
          class="font-medium text-primary hover:text-primary/80 transition-colors"
        >
          Already have an account? Sign in
        </a>
      </div>
      <p class="mt-4 text-xs text-muted-foreground">
        This invitation link is valid for 7 days. If it has expired, 
        please contact your administrator for a new invitation.
      </p>
    </div>
  </div>
</div> 