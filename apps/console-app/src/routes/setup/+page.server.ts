import type { Actions } from './$types'
import { SetupManager } from '$lib/server/auth/setup-manager'
import { fail, redirect } from '@sveltejs/kit'

export const actions: Actions = {
  setup: async ({ request }) => {
    const formData = await request.formData()
    const email = formData.get('email')?.toString()
    const password = formData.get('password')?.toString()
    const token = formData.get('token')?.toString()

    // Validate required fields
    if (!email || !password || !token) {
      return fail(400, {
        error: 'All fields are required'
      })
    }

    // Validate password strength
    if (password.length < 8) {
      return fail(400, {
        error: 'Password must be at least 8 characters long'
      })
    }

    const setupManager = new SetupManager()
    
    // Perform setup
    const result = await setupManager.performInitialSetup({
      email,
      password,
      token
    })

    if (!result.success) {
      return fail(400, {
        error: result.error || 'Setup failed'
      })
    }

    // Redirect to login page with success message
    throw redirect(303, '/login?message=Setup completed successfully. Please sign in.')
  }
}