<script lang="ts">
  import { enhance } from '$app/forms'
  import { page } from '$app/stores'
  import type { PageData, ActionData } from './$types'

  export let data: PageData
  export let form: ActionData

  $: token = $page.url.searchParams.get('token') || ''
  $: hasValidToken = data.setupConfig.hasValidToken

  let isSubmitting = false
  let password = ''
  let confirmPassword = ''
  let passwordsMatch = true

  $: passwordsMatch = password === confirmPassword || confirmPassword === ''
</script>

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
  <div class="max-w-md w-full bg-white rounded-xl shadow-xl p-8">
    <div class="text-center mb-8">
      <div class="w-16 h-16 bg-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2v6a2 2 0 01-2 2h-6a2 2 0 01-2-2v-6a2 2 0 012-2m0 0V7a2 2 0 012-2h2a2 2 0 012 2v2"/>
        </svg>
      </div>
      <h1 class="text-2xl font-bold text-gray-900 mb-2">
        ProcureServe II Setup
      </h1>
      <p class="text-gray-600">
        Initialize the first super admin account
      </p>
    </div>

    {#if !hasValidToken}
      <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
          </svg>
          <div>
            <h3 class="text-sm font-medium text-red-800">Invalid Setup Token</h3>
            <p class="text-sm text-red-600 mt-1">
              The setup URL is invalid or expired. Please check the correct setup URL.
            </p>
          </div>
        </div>
      </div>
    {:else}
      {#if form?.error}
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
            </svg>
            <div>
              <h3 class="text-sm font-medium text-red-800">Setup Failed</h3>
              <p class="text-sm text-red-600 mt-1">{form.error}</p>
            </div>
          </div>
        </div>
      {/if}

      <form 
        method="POST" 
        action="?/setup"
        use:enhance={() => {
          isSubmitting = true
          return async ({ update }) => {
            await update()
            isSubmitting = false
          }
        }}
        class="space-y-6"
      >
        <input type="hidden" name="token" value={token} />
        
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
            Admin Email
          </label>
          <input
            id="email"
            name="email"
            type="email"
            value={data.setupConfig.expectedEmail || ''}
            readonly
            class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-700 focus:outline-none"
          />
          <p class="text-xs text-gray-500 mt-1">
            This email is configured in the environment variables
          </p>
        </div>

        <div>
          <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
            Password
          </label>
          <input
            id="password"
            name="password"
            type="password"
            bind:value={password}
            required
            minlength="8"
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-200"
            placeholder="Create a strong password (8+ characters)"
          />
        </div>

        <div>
          <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
            Confirm Password
          </label>
          <input
            id="confirmPassword"
            type="password"
            bind:value={confirmPassword}
            required
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-200"
            class:border-red-300={!passwordsMatch && confirmPassword !== ''}
            class:focus:ring-red-500={!passwordsMatch && confirmPassword !== ''}
            placeholder="Confirm your password"
          />
          {#if !passwordsMatch && confirmPassword !== ''}
            <p class="text-red-600 text-sm mt-1">Passwords do not match</p>
          {/if}
        </div>

        <button
          type="submit"
          disabled={isSubmitting || !passwordsMatch || !password || !confirmPassword}
          class="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {#if isSubmitting}
            <span class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Setting up...
            </span>
          {:else}
            Complete Setup
          {/if}
        </button>
      </form>

      <div class="mt-8 pt-6 border-t border-gray-200">
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div class="flex items-start">
            <svg class="w-5 h-5 text-yellow-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
            <div class="text-sm">
              <h4 class="font-medium text-yellow-800">Security Notice</h4>
              <p class="text-yellow-700 mt-1">
                After setup, disable the ENABLE_INITIAL_SETUP environment variable for security.
              </p>
            </div>
          </div>
        </div>
      </div>
    {/if}
  </div>
</div>