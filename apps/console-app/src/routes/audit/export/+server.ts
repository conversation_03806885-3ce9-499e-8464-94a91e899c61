import type { Request<PERSON><PERSON><PERSON> } from './$types'
import { error } from '@sveltejs/kit'
import { createClient } from '@supabase/supabase-js'
import { PUBLIC_SUPABASE_URL } from '$env/static/public'
import { getServiceRoleKey } from '$lib/env-config'
import { AuditLogger } from '@psii/audit-service'

export const GET: RequestHandler = async ({ locals: { consoleUser }, url }) => {
  try {
    // Verify console user has access (allow both admin and super_admin)
    if (!consoleUser || !['admin', 'super_admin'].includes(consoleUser.role)) {
      throw error(403, 'Access denied: Admin access required')
    }

    // Create service client for audit queries
    const serviceRoleKey = getServiceRoleKey()
    const adminClient = createClient(PUBLIC_SUPABASE_URL, serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    const auditLogger = new AuditLogger(adminClient)

    // Get filter parameters from URL
    const eventTypes = url.searchParams.get('eventTypes')?.split(',').filter(Boolean) || []
    const severities = url.searchParams.get('severities')?.split(',').filter(Boolean) || []
    const dateFrom = url.searchParams.get('dateFrom') || undefined
    const dateTo = url.searchParams.get('dateTo') || undefined

    // Export filtered logs as CSV
    const csvContent = await auditLogger.exportLogsCSV({
      eventTypes: eventTypes.length ? eventTypes : undefined,
      severities: severities.length ? severities : undefined,
      dateFrom,
      dateTo
    })

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `audit-logs-${timestamp}.csv`

    return new Response(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`
      }
    })

  } catch (e) {
    console.error('[AUDIT] Export error:', e)
    throw error(500, 'Failed to export audit logs')
  }
}
