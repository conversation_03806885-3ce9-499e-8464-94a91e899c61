import type { PageServerLoad } from './$types'
import { error } from '@sveltejs/kit'
import { createClient } from '@supabase/supabase-js'
import { PUBLIC_SUPABASE_URL } from '$env/static/public'
import { getServiceRoleKey } from '$lib/env-config'
import { AuditLogger } from '@psii/audit-service'

export const load: PageServerLoad = async ({ locals: { supabase, user, consoleUser }, url }) => {
  try {
    console.log('[AUDIT] Loading audit dashboard with real Supabase data...')

    // Verify console user has access (allow both admin and super_admin)
    if (!consoleUser || !['admin', 'super_admin'].includes(consoleUser.role)) {
      console.error('[AUDIT] Unauthorized access attempt:', { 
        hasConsoleUser: !!consoleUser, 
        userRole: consoleUser?.role 
      })
      throw error(403, 'Access denied: Admin access required')
    }

    // Create service client for real audit queries
    const serviceRoleKey = getServiceRoleKey()
    const adminClient = createClient(PUBLIC_SUPABASE_URL, serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      },
      db: {
        schema: 'public'
      },
      global: {
        headers: {
          'Authorization': `Bearer ${serviceRoleKey}`,
          'apikey': serviceRoleKey
        }
      }
    })

    console.log('[AUDIT] Created Supabase admin client')

    // Initialize audit logger with real database
    const auditLogger = new AuditLogger(adminClient)

    // Get filter parameters from URL
    const eventTypes = url.searchParams.get('eventTypes')?.split(',').filter(Boolean) || []
    const severities = url.searchParams.get('severities')?.split(',').filter(Boolean) || []
    const dateFrom = url.searchParams.get('dateFrom') || undefined
    const dateTo = url.searchParams.get('dateTo') || undefined
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = 50
    const offset = (page - 1) * limit

    console.log('[AUDIT] Searching audit logs with filters:', { eventTypes, severities, dateFrom, dateTo })

    // Search real audit logs with filters
    const searchResult = await auditLogger.searchLogs({
      eventTypes: eventTypes.length ? eventTypes : undefined,
      severities: severities.length ? severities : undefined,
      dateFrom,
      dateTo,
      limit,
      offset
    })

    console.log('[AUDIT] Search result:', { 
      logsCount: searchResult.logs.length, 
      totalCount: searchResult.totalCount 
    })

    // Get real audit statistics
    const stats = await auditLogger.getAuditStats(30)
    console.log('[AUDIT] Statistics:', stats)

    // Get available event types from real data
    const { data: eventTypesData } = await adminClient
      .from('audit_logs')
      .select('event_type')
      .order('event_type')

    const availableEventTypes = [...new Set(eventTypesData?.map(e => e.event_type) || [])]
    console.log('[AUDIT] Available event types:', availableEventTypes)

    return {
      logs: searchResult.logs,
      totalCount: searchResult.totalCount,
      hasMore: searchResult.hasMore,
      currentPage: page,
      stats,
      filters: {
        eventTypes,
        severities,
        dateFrom,
        dateTo
      },
      availableEventTypes
    }

  } catch (e) {
    console.error('[AUDIT] Error loading audit dashboard:', e)
    
    // Return safe fallback data with error info
    return {
      logs: [],
      totalCount: 0,
      hasMore: false,
      currentPage: 1,
      stats: {
        totalEvents: 0,
        securityEvents: 0,
        userActions: 0,
        systemEvents: 0,
        criticalEvents: 0,
        eventsByType: {},
        eventsByDay: []
      },
      filters: {
        eventTypes: [],
        severities: [],
        dateFrom: undefined,
        dateTo: undefined
      },
      availableEventTypes: [],
      error: `Failed to load audit data: ${e instanceof Error ? e.message : 'Unknown error'}`
    }
  }
}
