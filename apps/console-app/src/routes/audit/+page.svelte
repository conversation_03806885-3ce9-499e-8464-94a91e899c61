<script lang="ts">
  import type { PageData } from './$types'
  import { Filter, Download, AlertTriangle, Shield, Activity, Users, Calendar } from 'lucide-svelte'
  import { goto } from '$app/navigation'
  
  export let data: PageData
  
  // Safely destructure data with defaults
  $: logs = data?.logs || []
  $: stats = data?.stats || {
    totalEvents: 0,
    securityEvents: 0,
    userActions: 0,
    systemEvents: 0,
    criticalEvents: 0,
    eventsByType: {},
    eventsByDay: []
  }
  $: filters = data?.filters || {
    eventTypes: [],
    severities: [],
    dateFrom: undefined,
    dateTo: undefined
  }
  $: availableEventTypes = data?.availableEventTypes || []
  
  // Filter state with safe defaults
  let selectedEventTypes: string[] = []
  let selectedSeverities: string[] = []
  let dateFrom = ''
  let dateTo = ''
  
  // Initialize from data when available
  $: if (data?.filters) {
    selectedEventTypes = data.filters.eventTypes || []
    selectedSeverities = data.filters.severities || []
    dateFrom = data.filters.dateFrom || ''
    dateTo = data.filters.dateTo || ''
  }
  
  // Apply filters
  function applyFilters() {
    const params = new URLSearchParams()
    if (selectedEventTypes.length) params.set('eventTypes', selectedEventTypes.join(','))
    if (selectedSeverities.length) params.set('severities', selectedSeverities.join(','))
    if (dateFrom) params.set('dateFrom', dateFrom)
    if (dateTo) params.set('dateTo', dateTo)
    
    goto(`/audit?${params.toString()}`)
  }
  
  // Clear filters
  function clearFilters() {
    selectedEventTypes = []
    selectedSeverities = []
    dateFrom = ''
    dateTo = ''
    goto('/audit')
  }
  
  // Format event type for display
  function formatEventType(eventType: string | undefined | null): string {
    if (!eventType) return 'Unknown Event'
    return eventType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }
  
  // Get severity color
  function getSeverityColor(severity: string): string {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }
  
  // Export audit logs
  async function exportLogs() {
    const params = new URLSearchParams()
    if (selectedEventTypes.length) params.set('eventTypes', selectedEventTypes.join(','))
    if (selectedSeverities.length) params.set('severities', selectedSeverities.join(','))
    if (dateFrom) params.set('dateFrom', dateFrom)
    if (dateTo) params.set('dateTo', dateTo)
    
    window.open(`/audit/export?${params.toString()}`, '_blank')
  }
</script>

<svelte:head>
  <title>Audit Dashboard | Console</title>
</svelte:head>

<div class="space-y-6">
  <!-- Show error if present -->
  {#if data?.error}
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
      <div class="flex items-center gap-2 text-red-800 dark:text-red-200">
        <AlertTriangle class="w-5 h-5" />
        <span class="font-medium">Audit Dashboard Error</span>
      </div>
      <p class="mt-2 text-sm text-red-700 dark:text-red-300">{data.error}</p>
    </div>
  {/if}

  <!-- Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Audit Dashboard</h1>
      <p class="mt-2 text-gray-600 dark:text-gray-400">
        Security audit trail and compliance monitoring
      </p>
    </div>
    
    <button on:click={exportLogs}
            class="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
      <Download class="w-4 h-4" />
      Export Logs
    </button>
  </div>

  <!-- Statistics Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="flex items-center gap-3">
        <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
          <Activity class="w-5 h-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <p class="text-sm text-gray-600 dark:text-gray-400">Total Events</p>
          <p class="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalEvents || 0}</p>
        </div>
      </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="flex items-center gap-3">
        <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
          <Shield class="w-5 h-5 text-red-600 dark:text-red-400" />
        </div>
        <div>
          <p class="text-sm text-gray-600 dark:text-gray-400">Security Events</p>
          <p class="text-2xl font-bold text-gray-900 dark:text-white">{stats.securityEvents || 0}</p>
        </div>
      </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="flex items-center gap-3">
        <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
          <Users class="w-5 h-5 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <p class="text-sm text-gray-600 dark:text-gray-400">User Actions</p>
          <p class="text-2xl font-bold text-gray-900 dark:text-white">{stats.userActions || 0}</p>
        </div>
      </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="flex items-center gap-3">
        <div class="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
          <AlertTriangle class="w-5 h-5 text-orange-600 dark:text-orange-400" />
        </div>
        <div>
          <p class="text-sm text-gray-600 dark:text-gray-400">Critical Events</p>
          <p class="text-2xl font-bold text-gray-900 dark:text-white">{stats.criticalEvents || 0}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
    <div class="flex items-center gap-2 mb-4">
      <Filter class="w-5 h-5 text-gray-600 dark:text-gray-400" />
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Filters</h2>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Event Types Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Event Types
        </label>
        <select multiple bind:value={selectedEventTypes}
                class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
          {#each availableEventTypes as eventType}
            <option value={eventType}>{formatEventType(eventType)}</option>
          {/each}
        </select>
      </div>
      
      <!-- Severity Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Severity
        </label>
        <select multiple bind:value={selectedSeverities}
                class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
          <option value="low">Low</option>
          <option value="medium">Medium</option>
          <option value="high">High</option>
          <option value="critical">Critical</option>
        </select>
      </div>
      
      <!-- Date From -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Date From
        </label>
        <input type="datetime-local" bind:value={dateFrom}
               class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white" />
      </div>
      
      <!-- Date To -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Date To
        </label>
        <input type="datetime-local" bind:value={dateTo}
               class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white" />
      </div>
    </div>
    
    <div class="flex gap-3 mt-4">
      <button on:click={applyFilters}
              class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
        Apply Filters
      </button>
      <button on:click={clearFilters}
              class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
        Clear Filters
      </button>
    </div>
  </div>

  <!-- Audit Logs Table -->
  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
        Audit Logs 
        <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
          ({data.totalCount || 0} total events)
        </span>
      </h2>
    </div>
    
    {#if (logs.length === 0)}
      <div class="p-8 text-center">
        <div class="text-gray-400 dark:text-gray-500 mb-2">
          <Activity class="w-12 h-12 mx-auto" />
        </div>
        <p class="text-gray-600 dark:text-gray-400">
          {data.totalCount === 0 
            ? 'No audit logs found. The audit system is ready and will track events as they occur.' 
            : 'No audit logs found matching your criteria.'
          }
        </p>
      </div>
    {:else}
      <div class="overflow-x-auto">
        <table class="w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-900">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Timestamp
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Event Type
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Severity
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Resource
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                User
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                IP Address
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Details
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {#each logs as log}
              <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {new Date(log.createdAt).toLocaleString()}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                  <span class="font-medium text-gray-900 dark:text-white">
                    {formatEventType(log.eventType)}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {getSeverityColor(log.details?.severity || 'medium')}">
                    {log.details?.severity || 'medium'}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <div>
                    <div class="font-medium">{log.resourceType}</div>
                    <div class="text-xs truncate max-w-24">{log.resourceId}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {log.userId ? log.userId.slice(0, 8) + '...' : 'System'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {log.ipAddress || '-'}
                </td>
                <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  <details class="cursor-pointer">
                    <summary class="hover:text-gray-700 dark:hover:text-gray-300">View Details</summary>
                    <pre class="mt-2 text-xs bg-gray-100 dark:bg-gray-900 p-2 rounded whitespace-pre-wrap max-w-sm overflow-auto">{JSON.stringify(log.details, null, 2)}</pre>
                  </details>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
      
      <!-- Pagination -->
      {#if data.hasMore || data.currentPage > 1}
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <div class="text-sm text-gray-500 dark:text-gray-400">
            Showing {((data.currentPage - 1) * 50) + 1} to {Math.min(data.currentPage * 50, data.totalCount)} of {data.totalCount} events
          </div>
          <div class="flex gap-2">
            {#if data.currentPage > 1}
              <a href="?page={data.currentPage - 1}{filters.eventTypes?.length ? '&eventTypes=' + filters.eventTypes.join(',') : ''}{filters.severities?.length ? '&severities=' + filters.severities.join(',') : ''}{filters.dateFrom ? '&dateFrom=' + filters.dateFrom : ''}{filters.dateTo ? '&dateTo=' + filters.dateTo : ''}"
                 class="px-3 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-600">
                Previous
              </a>
            {/if}
            {#if data.hasMore}
              <a href="?page={data.currentPage + 1}{filters.eventTypes?.length ? '&eventTypes=' + filters.eventTypes.join(',') : ''}{filters.severities?.length ? '&severities=' + filters.severities.join(',') : ''}{filters.dateFrom ? '&dateFrom=' + filters.dateFrom : ''}{filters.dateTo ? '&dateTo=' + filters.dateTo : ''}"
                 class="px-3 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-600">
                Next
              </a>
            {/if}
          </div>
        </div>
      {/if}
    {/if}
  </div>
</div>