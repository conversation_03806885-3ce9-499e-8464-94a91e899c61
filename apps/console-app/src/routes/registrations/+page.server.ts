import type { PageServerLoad } from './$types'
import { supabaseAdmin } from '$lib/server/supabase-admin'

export const load: PageServerLoad = async () => {
  try {
    // Query business_registrations directly
    const { data: registrationsData, error } = await supabaseAdmin
      .from('business_registrations')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(100)
      
    if (error) {
      console.error('Error loading registrations:', error)
      return {
        registrations: [],
        analytics: {
          total_count: 0,
          pending_count: 0,
          approved_count: 0,
          rejected_count: 0,
          new_company_count: 0,
          existing_domain_count: 0,
          avg_approval_time_hours: 0
        }
      }
    }

    // Calculate basic analytics from the data
    const registrations = registrationsData || []
    
    const analytics = {
      total_count: registrations.length,
      pending_count: registrations.filter(r => r.status === 'pending').length,
      approved_count: registrations.filter(r => r.status === 'approved').length,
      rejected_count: registrations.filter(r => r.status === 'rejected').length,
      new_company_count: registrations.filter(r => !r.activated_company_id).length,
      existing_domain_count: registrations.filter(r => r.activated_company_id).length,
      avg_approval_time_hours: 72 // Default placeholder
    }

    return {
      registrations,
      analytics
    }
  } catch (error) {
    console.error('Error loading registration overview:', error)
    return {
      registrations: [],
      analytics: {
        total_count: 0,
        pending_count: 0,
        approved_count: 0,
        rejected_count: 0,
        new_company_count: 0,
        existing_domain_count: 0,
        avg_approval_time_hours: 0
      }
    }
  }
}
