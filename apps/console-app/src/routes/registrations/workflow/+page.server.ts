import type { PageServerLoad } from './$types'
import { supabaseAdmin } from '$lib/server/supabase-admin'

export const load: PageServerLoad = async () => {
  try {
    // Get workflow metrics for the last 7 days
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
    
    // Query business_registrations directly
    const { data: timeline, error: timelineError } = await supabaseAdmin
      .from('business_registrations')
      .select('created_at, status, updated_at, activated_company_id, company_name, contact_person_email')
      .gte('created_at', sevenDaysAgo)
      .order('created_at')

    const { data: domains, error: domainsError } = await supabaseAdmin
      .from('business_registrations')
      .select('company_domain, activated_company_id, status')
      .not('company_domain', 'is', null)

    const { data: escalations, error: escalationsError } = await supabaseAdmin
      .from('business_registrations')
      .select('*')
      .eq('status', 'escalated')
      .order('created_at', { ascending: false })
      .limit(10)

    if (timelineError) {
      console.error('Error loading timeline data:', timelineError)
    }
    
    if (domainsError) {
      console.error('Error loading domains data:', domainsError)
    }
    
    if (escalationsError) {
      console.error('Error loading escalations data:', escalationsError)
    }

    return {
      timeline: timeline || [],
      domains: domains || [],
      escalations: escalations || []
    }
  } catch (error) {
    console.error('Error loading workflow data:', error)
    return {
      timeline: [],
      domains: [],
      escalations: []
    }
  }
}
