<script lang="ts">
  import type { PageData } from './$types'
  import { onMount } from 'svelte'
  import { GitBranch, Clock, AlertTriangle, CheckCircle, Users, ArrowRight, Download, Filter } from 'lucide-svelte'
  import { format, differenceInDays } from 'date-fns'
  import WorkflowAnalytics from '../components/WorkflowAnalytics.svelte'
  import AdvancedFilters from '../components/AdvancedFilters.svelte'
  import ExportReports from '../components/ExportReports.svelte'

  export let data: PageData

  // Component state
  let workflowData: any = null
  let funnelData: any = null
  let currentFilters: any = {}
  let savedPresets: any[] = []
  let isLoading = false
  let showAdvancedAnalytics = false
  let showExportPanel = false

  // Process workflow stages
  $: workflowStages = [
    {
      id: 'submission',
      name: 'Registration Submitted',
      icon: GitBranch,
      color: 'bg-blue-500',
      count: data.timeline?.length || 0,
      description: 'Total registrations received'
    },
    {
      id: 'domain_detection',
      name: 'Domain Detection',
      icon: Users,
      color: 'bg-purple-500',
      count: data.domains?.length || 0,
      description: 'Domain analysis completed'
    },
    {
      id: 'review',
      name: 'Under Review',
      icon: Clock,
      color: 'bg-yellow-500',
      count: data.timeline?.filter(t => t.status === 'pending').length || 0,
      description: 'Awaiting approval decision'
    },
    {
      id: 'approved',
      name: 'Approved',
      icon: CheckCircle,
      color: 'bg-green-500',
      count: data.timeline?.filter(t => t.status === 'approved').length || 0,
      description: 'Successfully approved'
    }
  ]

  // Calculate processing times safely
  $: processingTimes = (data.timeline || [])
    .filter(t => t.status === 'approved' && t.updated_at)
    .map(t => ({
      ...t,
      processingDays: differenceInDays(new Date(t.updated_at), new Date(t.created_at))
    }))
    .sort((a, b) => b.processingDays - a.processingDays)

  $: avgProcessingTime = processingTimes.length > 0
    ? Math.round(processingTimes.reduce((sum, t) => sum + t.processingDays, 0) / processingTimes.length)
    : 0

  // Domain detection insights with safe defaults
  $: domainInsights = {
    newCompanies: (data.domains || []).filter(d => !d.activated_company_id).length,
    existingCustomers: (data.domains || []).filter(d => d.activated_company_id).length,
    detectionRate: (data.domains || []).length > 0 
      ? Math.round(((data.domains || []).filter(d => d.activated_company_id).length / (data.domains || []).length) * 100)
      : 0
  }

  // Load workflow analytics data
  const loadWorkflowAnalytics = async (timeframe = '30d') => {
    isLoading = true
    try {
      const [workflowResponse, funnelResponse] = await Promise.all([
        fetch(`/api/registrations/analytics?metric=workflow&timeframe=${timeframe}`),
        fetch(`/api/registrations/analytics?metric=funnel&timeframe=${timeframe}`)
      ])

      if (workflowResponse.ok) {
        const workflowResult = await workflowResponse.json()
        workflowData = workflowResult.data
      }

      if (funnelResponse.ok) {
        const funnelResult = await funnelResponse.json()
        funnelData = funnelResult.data
      }
    } catch (error) {
      console.error('Failed to load workflow analytics:', error)
    } finally {
      isLoading = false
    }
  }

  // Handle filter changes
  const handleFiltersChanged = (event: CustomEvent) => {
    currentFilters = event.detail
    // In a real implementation, this would reload data with filters
    console.log('Filters changed:', currentFilters)
  }

  // Handle preset management
  const handleSavePreset = (event: CustomEvent) => {
    const preset = event.detail
    savedPresets = [...savedPresets, preset]
    localStorage.setItem('workflow_filter_presets', JSON.stringify(savedPresets))
  }

  const handleDeletePreset = (event: CustomEvent) => {
    const presetId = event.detail
    savedPresets = savedPresets.filter(p => p.id !== presetId)
    localStorage.setItem('workflow_filter_presets', JSON.stringify(savedPresets))
  }

  // Load saved presets
  onMount(() => {
    const saved = localStorage.getItem('workflow_filter_presets')
    if (saved) {
      try {
        savedPresets = JSON.parse(saved)
      } catch (error) {
        console.error('Failed to load saved presets:', error)
      }
    }
    loadWorkflowAnalytics()
  })
</script>

<svelte:head>
  <title>Registration Workflow | Console</title>
</svelte:head>

<div class="space-y-6">
  <!-- Enhanced Workflow Stages -->
  <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold flex items-center gap-2">
        <GitBranch class="w-5 h-5" />
        Registration Workflow Pipeline
      </h3>
      <div class="flex items-center gap-3">
        <button
          on:click={() => showAdvancedAnalytics = !showAdvancedAnalytics}
          class="flex items-center gap-2 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <GitBranch class="w-4 h-4" />
          {showAdvancedAnalytics ? 'Hide' : 'Show'} Advanced Analytics
        </button>
        <button
          on:click={() => showExportPanel = !showExportPanel}
          class="flex items-center gap-2 px-3 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <Download class="w-4 h-4" />
          Export & Reports
        </button>
      </div>
    </div>
    
    <div class="flex items-center justify-between">
      {#each workflowStages as stage, index}
        <div class="flex items-center">
          <div class="flex flex-col items-center">
            <div class="p-4 {stage.color} rounded-full mb-3">
              <svelte:component this={stage.icon} class="w-6 h-6 text-white" />
            </div>
            <h4 class="font-medium text-sm text-center mb-1">{stage.name}</h4>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{stage.count}</p>
            <p class="text-xs text-gray-500 text-center mt-1">{stage.description}</p>
          </div>
          
          {#if index < workflowStages.length - 1}
            <ArrowRight class="w-6 h-6 text-gray-400 mx-6" />
          {/if}
        </div>
      {/each}
    </div>
  </div>

  <!-- Advanced Filters -->
  <AdvancedFilters
    appliedFilters={currentFilters}
    {savedPresets}
    on:filtersChanged={handleFiltersChanged}
    on:savePreset={handleSavePreset}
    on:deletePreset={handleDeletePreset}
  />

  <!-- Export Panel -->
  {#if showExportPanel}
    <ExportReports
      currentData={data.timeline || []}
      analyticsData={{
        total_count: (data.timeline || []).length,
        approved_count: (data.timeline || []).filter(t => t.status === 'approved').length,
        pending_count: (data.timeline || []).filter(t => t.status === 'pending').length,
        rejected_count: (data.timeline || []).filter(t => t.status === 'rejected').length,
        avg_approval_time_hours: avgProcessingTime * 24
      }}
      appliedFilters={currentFilters}
    />
  {/if}

  <!-- Advanced Workflow Analytics -->
  {#if showAdvancedAnalytics}
    {#if isLoading}
      <div class="bg-white dark:bg-gray-800 p-12 rounded-lg border">
        <div class="flex items-center justify-center">
          <div class="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <span class="ml-3 text-gray-600 dark:text-gray-400">Loading advanced analytics...</span>
        </div>
      </div>
    {:else if workflowData}
      <WorkflowAnalytics
        funnelData={funnelData || []}
        stageData={workflowData.stageData || []}
        bottleneckData={workflowData.bottleneckData || []}
        userActionData={workflowData.userActionData || []}
      />
    {/if}
  {/if}

  <!-- Performance Metrics -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
      <div class="flex items-center gap-3 mb-3">
        <Clock class="w-5 h-5 text-blue-600" />
        <h3 class="font-semibold">Avg. Processing Time</h3>
      </div>
      <p class="text-3xl font-bold text-blue-600">{avgProcessingTime}</p>
      <p class="text-sm text-gray-500 mt-1">Days to approval</p>
    </div>

    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
      <div class="flex items-center gap-3 mb-3">
        <Users class="w-5 h-5 text-purple-600" />
        <h3 class="font-semibold">Domain Detection Rate</h3>
      </div>
      <p class="text-3xl font-bold text-purple-600">{domainInsights.detectionRate}%</p>
      <p class="text-sm text-gray-500 mt-1">Existing customers identified</p>
    </div>

    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
      <div class="flex items-center gap-3 mb-3">
        <AlertTriangle class="w-5 h-5 text-orange-600" />
        <h3 class="font-semibold">Escalations</h3>
      </div>
      <p class="text-3xl font-bold text-orange-600">{(data.escalations || []).length}</p>
      <p class="text-sm text-gray-500 mt-1">Requiring attention</p>
    </div>
  </div>

  <!-- Processing Time Analysis -->
  <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
    <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
      <Clock class="w-5 h-5" />
      Processing Time Analysis
    </h3>
    
    {#if processingTimes.length === 0}
      <div class="text-center py-8">
        <Clock class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p class="text-gray-500">No completed approvals in the last 7 days</p>
      </div>
    {:else}
      <div class="space-y-4">
        {#each processingTimes.slice(0, 10) as approval}
          <div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div class="flex items-center gap-4">
              <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <CheckCircle class="w-5 h-5 text-green-600 dark:text-green-300" />
              </div>
              <div>
                <h4 class="font-semibold">{approval.company_name || 'Unknown Company'}</h4>
                <p class="text-sm text-gray-500">{approval.contact_person_email || 'No email'}</p>
              </div>
            </div>
            
            <div class="flex items-center gap-6 text-sm">
              <div class="text-center">
                <p class="font-medium">{approval.processingDays}</p>
                <p class="text-gray-500">days</p>
              </div>
              <div class="text-center">
                <p class="font-medium">{format(new Date(approval.created_at), 'MMM dd')}</p>
                <p class="text-gray-500">submitted</p>
              </div>
              <div class="text-center">
                <p class="font-medium">{format(new Date(approval.updated_at), 'MMM dd')}</p>
                <p class="text-gray-500">approved</p>
              </div>
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </div>

  <!-- Domain Detection Insights -->
  <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
    <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
      <Users class="w-5 h-5" />
      Domain Detection Insights
    </h3>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="space-y-4">
        <div class="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <h4 class="font-medium text-purple-900 dark:text-purple-100 mb-2">New Companies</h4>
          <p class="text-2xl font-bold text-purple-600">{domainInsights.newCompanies}</p>
          <p class="text-sm text-purple-700 dark:text-purple-300">
            Require full company setup
          </p>
        </div>
        
        <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <h4 class="font-medium text-green-900 dark:text-green-100 mb-2">Existing Customers</h4>
          <p class="text-2xl font-bold text-green-600">{domainInsights.existingCustomers}</p>
          <p class="text-sm text-green-700 dark:text-green-300">
            Streamlined approval process
          </p>
        </div>
      </div>
      
      <div class="space-y-4">
        <h4 class="font-medium">Recent Domain Detections</h4>
        {#if (data.domains || []).length === 0}
          <p class="text-gray-500 text-sm">No recent domain detections</p>
        {:else}
          <div class="space-y-2">
            {#each (data.domains || []).slice(0, 5) as domain}
              <div class="flex items-center justify-between p-3 border rounded-lg">
                <span class="font-medium">{domain.company_domain || 'Unknown Domain'}</span>
                <span class="px-2 py-1 text-xs rounded-full
                  {domain.activated_company_id 
                    ? 'bg-green-100 text-green-700' 
                    : 'bg-purple-100 text-purple-700'}">
                  {domain.activated_company_id ? 'Existing' : 'New'}
                </span>
              </div>
            {/each}
          </div>
        {/if}
      </div>
    </div>
  </div>

  <!-- Escalations & Issues -->
  {#if (data.escalations || []).length > 0}
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
      <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
        <AlertTriangle class="w-5 h-5 text-orange-600" />
        Escalations Requiring Attention
      </h3>
      
      <div class="space-y-4">
        {#each (data.escalations || []) as escalation}
          <div class="flex items-center justify-between p-4 border border-orange-200 dark:border-orange-800 rounded-lg bg-orange-50 dark:bg-orange-900/20">
            <div class="flex items-center gap-4">
              <div class="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                <AlertTriangle class="w-5 h-5 text-orange-600 dark:text-orange-300" />
              </div>
              <div>
                <h4 class="font-semibold">{escalation.company_name || 'Unknown Company'}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">{escalation.contact_person_email || 'No email'}</p>
              </div>
            </div>
            
            <div class="flex items-center gap-3 text-sm">
              <span class="px-2 py-1 bg-orange-100 text-orange-700 rounded-full font-medium">
                ESCALATED
              </span>
              <span class="text-gray-500">
                {format(new Date(escalation.created_at), 'MMM dd, yyyy')}
              </span>
            </div>
          </div>
        {/each}
      </div>
    </div>
  {/if}

  <!-- Workflow Optimization Tips -->
  <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-6 rounded-lg border">
    <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
      <GitBranch class="w-5 h-5" />
      Workflow Optimization Tips
    </h3>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
        <h4 class="font-medium mb-2">⚡ Fast Track Existing Customers</h4>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          {domainInsights.detectionRate}% of registrations are from existing customers and can be auto-approved
        </p>
      </div>
      
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
        <h4 class="font-medium mb-2">⏱️ Reduce Processing Time</h4>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Current average: {avgProcessingTime} days. Target: &lt; 3 days for better experience
        </p>
      </div>
      
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
        <h4 class="font-medium mb-2">🚨 Monitor Escalations</h4>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          {(data.escalations || []).length} items need immediate attention to prevent delays
        </p>
      </div>
    </div>
  </div>
</div>
