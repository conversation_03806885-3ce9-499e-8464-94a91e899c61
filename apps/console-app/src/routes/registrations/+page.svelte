<script lang="ts">
  import type { PageData } from './$types'
  import { FileText, Building2 } from 'lucide-svelte'
  import AnalyticsDashboard from './components/AnalyticsDashboard.svelte'

  export let data: PageData

  $: analytics = data.analytics || {
    total_count: 0,
    pending_count: 0,
    approved_count: 0,
    rejected_count: 0,
    new_company_count: 0,
    existing_domain_count: 0,
    avg_approval_time_hours: 0
  }
  $: recentRegistrations = data.registrations?.slice(0, 5) || []
</script>

<svelte:head>
  <title>Registration Overview | Console</title>
</svelte:head>

<div class="space-y-6">
  <!-- Enhanced Analytics Dashboard -->
  <AnalyticsDashboard initialData={analytics} />

  <!-- Recent Activity Section -->
  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold flex items-center gap-2">
        <FileText class="w-5 h-5" />
        Recent Registrations
      </h3>
    </div>
    
    <div class="p-6">
      {#if recentRegistrations.length === 0}
        <div class="text-center py-8">
          <Building2 class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-500">No registrations found</p>
        </div>
      {:else}
        <div class="space-y-4">
          {#each recentRegistrations as registration}
            <div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <div class="flex items-center gap-4">
                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                  <Building2 class="w-5 h-5 text-purple-600 dark:text-purple-300" />
                </div>
                <div>
                  <h4 class="font-semibold">{registration.company_name}</h4>
                  <p class="text-sm text-gray-500">{registration.contact_person_email}</p>
                  {#if registration.activated_company_id}
                    <span class="text-xs text-blue-600 font-medium">Existing Customer</span>
                  {:else}
                    <span class="text-xs text-purple-600 font-medium">New Company</span>
                  {/if}
                </div>
              </div>
              
              <div class="flex items-center gap-3">
                <span class="px-2 py-1 text-xs font-medium rounded-full
                  {registration.status === 'pending' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300' :
                   registration.status === 'approved' ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300' :
                   'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'}">
                  {registration.status.toUpperCase()}
                </span>
                <span class="text-sm text-gray-500">
                  {new Date(registration.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>
          {/each}
        </div>
        
        <div class="mt-6 text-center">
          <a href="/companies/pending" 
             class="inline-flex items-center px-4 py-2 text-sm font-medium text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 transition-colors">
            View All Pending →
          </a>
        </div>
      {/if}
    </div>
  </div>
</div>
