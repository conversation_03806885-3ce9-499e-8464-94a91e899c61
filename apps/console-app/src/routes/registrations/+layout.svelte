<script lang="ts">
  import { page } from '$app/stores'
  import { BarChart3, TrendingUp, GitBranch } from 'lucide-svelte'

  // Sub-navigation for registrations section
  const navItems = [
    {
      href: '/registrations',
      label: 'Overview',
      icon: BarChart3,
      exact: true
    },
    {
      href: '/registrations/analytics',
      label: 'Analytics',
      icon: TrendingUp
    },
    {
      href: '/registrations/workflow',
      label: 'Workflow',
      icon: GitBranch
    }
  ]

  $: currentPath = $page.url.pathname
</script>

<div class="space-y-6">
  <!-- Header -->
  <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Registration Analytics</h1>
    <p class="text-gray-600 dark:text-gray-400 mt-1">
      Comprehensive business registration insights and metrics
    </p>
  </div>

  <!-- Sub Navigation -->
  <div class="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg w-fit">
    {#each navItems as item}
      {@const isActive = item.exact 
        ? currentPath === item.href 
        : currentPath.startsWith(item.href) && item.href !== '/registrations'}
      
      <a
        href={item.href}
        class="flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors
          {isActive 
            ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm' 
            : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'}"
      >
        <svelte:component this={item.icon} class="w-4 h-4" />
        {item.label}
      </a>
    {/each}
  </div>

  <!-- Content -->
  <slot />
</div>
