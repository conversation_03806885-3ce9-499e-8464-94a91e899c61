<script lang="ts">
  import type { PageData } from './$types'
  import { Calendar, BarChart3, Globe, Mail } from 'lucide-svelte'
  import AnalyticsDashboard from '../components/AnalyticsDashboard.svelte'
  import DomainAnalytics from '../components/DomainAnalytics.svelte'
  import EmailAnalytics from '../components/EmailAnalytics.svelte'

  export let data: PageData

  let selectedTimeframe = '30d'
  let activeTab = 'trends'

  const timeframeOptions = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' }
  ]

  const tabs = [
    { id: 'trends', label: 'Trends', icon: BarChart3 },
    { id: 'domains', label: 'Domain Analysis', icon: Globe },
    { id: 'performance', label: 'Email Performance', icon: Mail }
  ]
</script>

<svelte:head>
  <title>Registration Analytics | Console</title>
</svelte:head>

<div class="space-y-6">
  <!-- <PERSON> Header with Controls -->
  <div class="flex items-center justify-between">
    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Registration Analytics</h1>
    
    <div class="flex items-center gap-4">
      <!-- Timeframe Selector -->
      <div class="flex items-center gap-2">
        <Calendar class="w-4 h-4 text-gray-500" />
        <select 
          bind:value={selectedTimeframe}
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-sm focus:ring-2 focus:ring-purple-500"
        >
          {#each timeframeOptions as option}
            <option value={option.value}>{option.label}</option>
          {/each}
        </select>
      </div>
    </div>
  </div>

  <!-- Tab Navigation -->
  <div class="border-b border-gray-200 dark:border-gray-700">
    <nav class="-mb-px flex space-x-8">
      {#each tabs as tab}
        <button
          on:click={() => activeTab = tab.id}
          class="flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors
            {activeTab === tab.id 
              ? 'border-purple-500 text-purple-600 dark:text-purple-400' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'}"
        >
          <svelte:component this={tab.icon} class="w-4 h-4" />
          {tab.label}
        </button>
      {/each}
    </nav>
  </div>

  <!-- Tab Content -->
  <div class="min-h-[600px]">
    {#if activeTab === 'trends'}
      <AnalyticsDashboard initialData={data.analytics} />
    {:else if activeTab === 'domains'}
      <DomainAnalytics timeframe={selectedTimeframe} />
    {:else if activeTab === 'performance'}
      <EmailAnalytics timeframe={selectedTimeframe} />
    {/if}
  </div>

  <!-- Quick Actions Footer -->
  <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border">
    <div class="flex items-center justify-between">
      <div class="text-sm text-gray-600 dark:text-gray-400">
        Analytics refreshed automatically every 5 minutes
      </div>
      <div class="flex gap-2">
        <button class="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200 transition-colors">
          Export CSV
        </button>
        <button class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors">
          Generate Report
        </button>
      </div>
    </div>
  </div>
</div>

