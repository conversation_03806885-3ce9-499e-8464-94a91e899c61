import type { PageServerLoad } from './$types'
import { supabaseAdmin } from '$lib/server/supabase-admin'

export const load: PageServerLoad = async () => {
  try {
    // Get trend data for the last 30 days
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    
    // Query business_registrations directly
    const { data: trends, error: trendsError } = await supabaseAdmin
      .from('business_registrations')
      .select('created_at, status, activated_company_id, company_domain')
      .gte('created_at', thirtyDaysAgo)
      .order('created_at')
    
    const { data: domains, error: domainsError } = await supabaseAdmin
      .from('business_registrations')
      .select('company_domain, activated_company_id, status')
      .not('company_domain', 'is', null)

    if (trendsError) {
      console.error('Error loading trends data:', trendsError)
    }
    
    if (domainsError) {
      console.error('Error loading domains data:', domainsError)
    }

    return {
      trends: trends || [],
      domains: domains || []
    }
  } catch (error) {
    console.error('Error loading analytics:', error)
    return {
      trends: [],
      domains: []
    }
  }
}
