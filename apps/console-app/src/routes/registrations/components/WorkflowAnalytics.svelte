<script lang="ts">
  import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, FunnelChart, Funnel, Cell, PieChart, Pie } from 'recharts'
  import { GitBranch, TrendingDown, AlertTriangle, Clock, CheckCircle, Users, ArrowRight } from 'lucide-svelte'

  export let funnelData: any[] = []
  export let stageData: any[] = []
  export let bottleneckData: any[] = []
  export let userActionData: any[] = []

  // Funnel conversion colors
  const FUNNEL_COLORS = ['#3b82f6', '#6366f1', '#8b5cf6', '#a855f7', '#d946ef']
  
  // Stage duration colors based on performance
  const getStageColor = (avgDays: number) => {
    if (avgDays <= 2) return '#10b981' // Green - Fast
    if (avgDays <= 5) return '#f59e0b' // Yellow - Acceptable
    return '#ef4444' // Red - Slow
  }

  // Calculate conversion rates
  $: conversionRates = funnelData.map((stage, index) => {
    if (index === 0) return { ...stage, conversionRate: 100 }
    const previousStage = funnelData[index - 1]
    const rate = previousStage.value > 0 ? (stage.value / previousStage.value * 100) : 0
    return { ...stage, conversionRate: Math.round(rate * 10) / 10 }
  })

  // Bottleneck identification
  $: criticalBottlenecks = bottleneckData
    .filter(stage => stage.avgDuration > 3) // More than 3 days
    .sort((a, b) => b.avgDuration - a.avgDuration)
    .slice(0, 3)

  // User action heatmap data
  $: actionHeatmap = userActionData.reduce((acc, action) => {
    const hour = new Date(action.timestamp).getHours()
    const day = new Date(action.timestamp).getDay()
    const key = `${day}-${hour}`
    acc[key] = (acc[key] || 0) + 1
    return acc
  }, {})
</script>

<div class="space-y-6">
  <!-- Registration Funnel -->
  <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold flex items-center gap-2">
        <GitBranch class="w-5 h-5" />
        Registration Conversion Funnel
      </h3>
      <div class="text-sm text-gray-500">
        Overall conversion: {conversionRates.length > 0 ? 
          Math.round((conversionRates[conversionRates.length - 1]?.value / conversionRates[0]?.value) * 100) : 0}%
      </div>
    </div>
    
    <div class="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <FunnelChart>
          <Tooltip 
            formatter={(value, name) => [
              `${value} registrations`, 
              name === 'value' ? 'Count' : name
            ]}
            labelFormatter={(name) => `Stage: ${name}`}
          />
          <Funnel 
            dataKey="value" 
            data={conversionRates} 
            isAnimationActive={true}
          >
            {#each conversionRates as entry, index}
              <Cell fill={FUNNEL_COLORS[index % FUNNEL_COLORS.length]} />
            {/each}
          </Funnel>
        </FunnelChart>
      </ResponsiveContainer>
    </div>

    <!-- Conversion Rate Details -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
      {#each conversionRates as stage, index}
        <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">
            {stage.name}
          </div>
          <div class="text-lg font-bold">{stage.value}</div>
          <div class="text-xs text-gray-500">
            {index > 0 ? `${stage.conversionRate}% conversion` : 'Starting point'}
          </div>
        </div>
      {/each}
    </div>
  </div>

  <!-- Stage Duration Analysis -->
  <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold flex items-center gap-2">
        <Clock class="w-5 h-5" />
        Stage Duration Analysis
      </h3>
      <div class="text-sm text-gray-500">
        Target: &lt; 3 days per stage
      </div>
    </div>
    
    <div class="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={stageData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis 
            dataKey="stage" 
            tick={{ fontSize: 12 }} 
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis 
            label={{ value: 'Days', angle: -90, position: 'insideLeft' }}
            tick={{ fontSize: 12 }}
          />
          <Tooltip 
            formatter={(value) => [`${value} days`, 'Average Duration']}
            labelFormatter={(stage) => `Stage: ${stage}`}
          />
          <Bar 
            dataKey="avgDuration" 
            fill={(entry) => getStageColor(entry.avgDuration)}
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>

    <!-- Performance Indicators -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
      <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
        <div class="flex items-center gap-2 mb-2">
          <CheckCircle class="w-4 h-4 text-green-600" />
          <span class="text-sm font-medium text-green-800 dark:text-green-200">Fast Stages</span>
        </div>
        <div class="text-lg font-bold text-green-600">
          {stageData.filter(s => s.avgDuration <= 2).length}
        </div>
        <div class="text-xs text-green-600">≤ 2 days average</div>
      </div>

      <div class="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
        <div class="flex items-center gap-2 mb-2">
          <Clock class="w-4 h-4 text-yellow-600" />
          <span class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Acceptable</span>
        </div>
        <div class="text-lg font-bold text-yellow-600">
          {stageData.filter(s => s.avgDuration > 2 && s.avgDuration <= 5).length}
        </div>
        <div class="text-xs text-yellow-600">3-5 days average</div>
      </div>

      <div class="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
        <div class="flex items-center gap-2 mb-2">
          <TrendingDown class="w-4 h-4 text-red-600" />
          <span class="text-sm font-medium text-red-800 dark:text-red-200">Bottlenecks</span>
        </div>
        <div class="text-lg font-bold text-red-600">
          {stageData.filter(s => s.avgDuration > 5).length}
        </div>
        <div class="text-xs text-red-600">&gt; 5 days average</div>
      </div>
    </div>
  </div>

  <!-- Critical Bottlenecks -->
  {#if criticalBottlenecks.length > 0}
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border border-red-200 dark:border-red-800">
      <h3 class="text-lg font-semibold flex items-center gap-2 mb-6">
        <AlertTriangle class="w-5 h-5 text-red-600" />
        Critical Bottlenecks Identified
      </h3>
      
      <div class="space-y-4">
        {#each criticalBottlenecks as bottleneck}
          <div class="flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <div class="flex items-center gap-4">
              <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                <AlertTriangle class="w-5 h-5 text-red-600 dark:text-red-300" />
              </div>
              <div>
                <h4 class="font-semibold text-red-900 dark:text-red-100">{bottleneck.stage}</h4>
                <p class="text-sm text-red-700 dark:text-red-300">
                  {bottleneck.description || 'Processing stage experiencing delays'}
                </p>
              </div>
            </div>
            
            <div class="text-right">
              <div class="text-lg font-bold text-red-600">{bottleneck.avgDuration} days</div>
              <div class="text-xs text-red-500">Average duration</div>
            </div>
          </div>
        {/each}
      </div>

      <!-- Bottleneck Solutions -->
      <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h4 class="font-semibold text-blue-900 dark:text-blue-100 mb-3">Recommended Actions</h4>
        <ul class="space-y-2 text-sm text-blue-800 dark:text-blue-200">
          <li>• Review approval workflows for the slowest stages</li>
          <li>• Consider automated approvals for low-risk registrations</li>
          <li>• Add additional reviewers to high-volume stages</li>
          <li>• Implement escalation triggers for delayed items</li>
        </ul>
      </div>
    </div>
  {/if}

  <!-- User Action Distribution -->
  <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
    <h3 class="text-lg font-semibold flex items-center gap-2 mb-6">
      <Users class="w-5 h-5" />
      Approval Pipeline Activity
    </h3>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Action Types Distribution -->
      <div>
        <h4 class="font-medium mb-4">Action Types (Last 7 days)</h4>
        <div class="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={[
                  { name: 'Approvals', value: 45, fill: '#10b981' },
                  { name: 'Reviews', value: 120, fill: '#3b82f6' },
                  { name: 'Rejections', value: 8, fill: '#ef4444' },
                  { name: 'Escalations', value: 12, fill: '#f59e0b' }
                ]}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={(entry) => `${entry.name}: ${entry.value}`}
              />
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      <!-- Processing Time Trends -->
      <div>
        <h4 class="font-medium mb-4">Processing Time Trend</h4>
        <div class="space-y-3">
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <span class="text-sm">This Week</span>
            <span class="font-bold text-green-600">2.3 days</span>
          </div>
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <span class="text-sm">Last Week</span>
            <span class="font-bold text-gray-600">3.1 days</span>
          </div>
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <span class="text-sm">Month Avg</span>
            <span class="font-bold text-yellow-600">4.2 days</span>
          </div>
          <div class="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div class="flex items-center gap-2 text-sm text-green-800 dark:text-green-200">
              <TrendingDown class="w-4 h-4" />
              26% improvement this week
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Performance Summary -->
  <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-6 rounded-lg border">
    <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
      <CheckCircle class="w-5 h-5" />
      Workflow Performance Summary
    </h3>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
        <div class="text-2xl font-bold text-blue-600">
          {conversionRates.length > 0 ? 
            Math.round((conversionRates[conversionRates.length - 1]?.value / conversionRates[0]?.value) * 100) : 0}%
        </div>
        <div class="text-sm text-gray-600 dark:text-gray-400">Overall Conversion Rate</div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
        <div class="text-2xl font-bold text-green-600">
          {stageData.length > 0 ? 
            Math.round(stageData.reduce((sum, s) => sum + s.avgDuration, 0) / stageData.length) : 0}
        </div>
        <div class="text-sm text-gray-600 dark:text-gray-400">Avg Processing Days</div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
        <div class="text-2xl font-bold text-purple-600">
          {criticalBottlenecks.length}
        </div>
        <div class="text-sm text-gray-600 dark:text-gray-400">Active Bottlenecks</div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
        <div class="text-2xl font-bold text-orange-600">
          {stageData.filter(s => s.avgDuration <= 2).length}
        </div>
        <div class="text-sm text-gray-600 dark:text-gray-400">Optimized Stages</div>
      </div>
    </div>
  </div>
</div>
