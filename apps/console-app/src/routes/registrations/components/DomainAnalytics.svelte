<script lang="ts">
  import { onMount } from 'svelte'
  import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell, LineChart, Line, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>er } from 'recharts'
  import { Target, Globe, CheckCircle, AlertTriangle, TrendingUp, Users, Building } from 'lucide-svelte'

  export let timeframe = '30d'

  let loading = false
  let domainData: any = null

  const COLORS = {
    existing: '#10b981',
    new: '#8b5cf6', 
    high_accuracy: '#059669',
    medium_accuracy: '#f59e0b',
    low_accuracy: '#ef4444'
  }

  onMount(() => {
    loadDomainAnalytics()
  })

  async function loadDomainAnalytics() {
    loading = true
    try {
      const response = await fetch(`/api/registrations/analytics?metric=domains&timeframe=${timeframe}`)
      const result = await response.json()
      domainData = result.data
    } catch (error) {
      console.error('Failed to load domain analytics:', error)
      domainData = { domains: [], total_domains: 0, detection_rate: 0 }
    } finally {
      loading = false
    }
  }

  $: if (timeframe) {
    loadDomainAnalytics()
  }

  // Process data for charts
  $: topDomains = domainData?.domains?.slice(0, 10) || []
  
  $: accuracyDistribution = domainData?.domains?.reduce((acc: any, domain: any) => {
    const accuracy = domain.detection_accuracy
    let category = 'low_accuracy'
    if (accuracy >= 80) category = 'high_accuracy'
    else if (accuracy >= 50) category = 'medium_accuracy'
    
    acc[category] = (acc[category] || 0) + 1
    return acc
  }, {}) || {}

  $: accuracyChartData = [
    { name: 'High (80%+)', value: accuracyDistribution.high_accuracy || 0, fill: COLORS.high_accuracy },
    { name: 'Medium (50-79%)', value: accuracyDistribution.medium_accuracy || 0, fill: COLORS.medium_accuracy },
    { name: 'Low (<50%)', value: accuracyDistribution.low_accuracy || 0, fill: COLORS.low_accuracy }
  ]

  $: detectionScatterData = domainData?.domains?.map((domain: any) => ({
    domain: domain.domain,
    total: domain.total,
    accuracy: domain.detection_accuracy,
    approved: domain.approved
  })) || []

  $: avgDetectionRate = domainData?.detection_rate || 0
  $: totalDomains = domainData?.total_domains || 0
</script>

<div class="space-y-6">
  <!-- Domain Detection Overview -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Total Domains -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm text-gray-500 dark:text-gray-400">Total Domains</p>
          <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">{totalDomains}</p>
        </div>
        <div class="p-3 bg-blue-500 rounded-full">
          <Globe class="w-6 h-6 text-white" />
        </div>
      </div>
      <p class="text-xs text-gray-500 mt-2">Unique domains detected</p>
    </div>

    <!-- Average Detection Rate -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm text-gray-500 dark:text-gray-400">Detection Rate</p>
          <p class="text-2xl font-bold text-green-600 mt-1">{avgDetectionRate.toFixed(1)}%</p>
        </div>
        <div class="p-3 bg-green-500 rounded-full">
          <Target class="w-6 h-6 text-white" />
        </div>
      </div>
      <p class="text-xs text-gray-500 mt-2">Average accuracy</p>
    </div>

    <!-- High Accuracy Domains -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm text-gray-500 dark:text-gray-400">High Accuracy</p>
          <p class="text-2xl font-bold text-green-600 mt-1">{accuracyDistribution.high_accuracy || 0}</p>
        </div>
        <div class="p-3 bg-green-500 rounded-full">
          <CheckCircle class="w-6 h-6 text-white" />
        </div>
      </div>
      <p class="text-xs text-gray-500 mt-2">80%+ detection rate</p>
    </div>
  </div>

  <!-- Charts Row -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Top Domains Performance -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
      <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
        <Building class="w-5 h-5" />
        Top Domains by Volume
      </h3>
      {#if loading}
        <div class="animate-pulse h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
      {:else if topDomains.length > 0}
        <div style="height: 300px">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={topDomains} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis type="number" className="text-xs fill-gray-600 dark:fill-gray-300" />
              <YAxis dataKey="domain" type="category" width={80} className="text-xs fill-gray-600 dark:fill-gray-300" />
              <Tooltip 
                labelStyle={{ color: '#374151' }}
                contentStyle={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}
              />
              <Bar dataKey="total" fill="#8b5cf6" />
              <Bar dataKey="approved" fill="#10b981" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      {:else}
        <div class="text-center py-8">
          <Globe class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-500">No domain data available</p>
        </div>
      {/if}
    </div>

    <!-- Detection Accuracy Distribution -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
      <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
        <Target class="w-5 h-5" />
        Detection Accuracy Distribution
      </h3>
      {#if loading}
        <div class="animate-pulse h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
      {:else}
        <div style="height: 300px">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={accuracyChartData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {#each accuracyChartData as entry, index}
                  <Cell key="cell-{index}" fill={entry.fill} />
                {/each}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        <!-- Legend -->
        <div class="flex flex-col gap-2 mt-4">
          {#each accuracyChartData as item}
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 rounded-full" style="background-color: {item.fill}"></div>
                <span class="text-sm">{item.name}</span>
              </div>
              <span class="text-sm font-medium">{item.value}</span>
            </div>
          {/each}
        </div>
      {/if}
    </div>
  </div>

  <!-- Domain Details Table -->
  <div class="bg-white dark:bg-gray-800 rounded-lg border">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold flex items-center gap-2">
        <Users class="w-5 h-5" />
        Domain Performance Details
      </h3>
    </div>
    
    <div class="overflow-x-auto">
      {#if loading}
        <div class="p-6">
          <div class="animate-pulse space-y-4">
            {#each Array(5) as _}
              <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            {/each}
          </div>
        </div>
      {:else if topDomains.length > 0}
        <table class="w-full text-sm">
          <thead class="bg-gray-50 dark:bg-gray-900">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Domain
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Total
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Approved
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Pending
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Detection Rate
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Company Match
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
            {#each topDomains as domain}
              <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <Globe class="w-4 h-4 text-gray-400 mr-2" />
                    <span class="font-medium">{domain.domain}</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right font-medium">
                  {domain.total}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-green-600 font-medium">
                  {domain.approved}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-yellow-600">
                  {domain.pending}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right">
                  <span class="px-2 py-1 text-xs rounded-full font-medium
                    {domain.detection_accuracy >= 80 ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300' : 
                     domain.detection_accuracy >= 50 ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300' : 
                     'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'}">
                    {domain.detection_accuracy}%
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right">
                  {#if domain.existing_match > 0}
                    <span class="flex items-center justify-end gap-1 text-blue-600">
                      <CheckCircle class="w-4 h-4" />
                      Yes
                    </span>
                  {:else}
                    <span class="flex items-center justify-end gap-1 text-gray-500">
                      <AlertTriangle class="w-4 h-4" />
                      No
                    </span>
                  {/if}
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      {:else}
        <div class="text-center py-8">
          <Globe class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-500">No domain data available for the selected timeframe</p>
        </div>
      {/if}
    </div>
  </div>
</div>
