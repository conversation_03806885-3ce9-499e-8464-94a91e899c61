<script lang="ts">
  interface SimpleChartData {
    label: string
    value: number
    color: string
  }

  export let data: SimpleChartData[]
  export let height: number = 300
  export let title: string = 'Chart'

  $: maxValue = Math.max(...data.map(d => d.value))
  $: chartHeight = height - 80 // Reserve space for labels
</script>

<div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
  <h3 class="text-lg font-semibold mb-4">{title}</h3>
  
  <div class="space-y-3" style="height: {chartHeight}px">
    {#each data as item}
      <div class="flex items-center gap-3">
        <div class="w-20 text-sm text-gray-600 dark:text-gray-300 truncate">
          {item.label}
        </div>
        <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-8 relative">
          <div 
            class="h-full rounded-full flex items-center justify-end px-3 text-white text-sm font-medium"
            style="background-color: {item.color}; width: {maxValue > 0 ? (item.value / maxValue) * 100 : 0}%"
          >
            {item.value}
          </div>
        </div>
      </div>
    {/each}
  </div>
</div>
