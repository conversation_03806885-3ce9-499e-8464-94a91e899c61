<script lang="ts">
  import { onMount } from 'svelte'
  import { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar } from 'recharts'
  import { Calendar, TrendingUp, Users, Clock, Target, Activity } from 'lucide-svelte'

  export let initialData: any = null

  let selectedTimeframe = '30d'
  let loading = false
  let analyticsData: any = null
  let trendData: any[] = []
  let domainData: any = null

  const timeframeOptions = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' }
  ]

  const COLORS = ['#8b5cf6', '#10b981', '#f59e0b', '#ef4444']

  onMount(() => {
    if (initialData) {
      analyticsData = initialData
    }
    loadAnalytics()
  })

  async function loadAnalytics() {
    loading = true
    try {
      // Load all analytics data
      const [overviewRes, trendsRes, domainsRes] = await Promise.all([
        fetch(`/api/registrations/analytics?metric=overview&timeframe=${selectedTimeframe}`),
        fetch(`/api/registrations/analytics?metric=trends&timeframe=${selectedTimeframe}`),
        fetch(`/api/registrations/analytics?metric=domains&timeframe=${selectedTimeframe}`)
      ])

      const [overview, trends, domains] = await Promise.all([
        overviewRes.json(),
        trendsRes.json(),
        domainsRes.json()
      ])

      analyticsData = overview.data
      trendData = trends.data || []
      domainData = domains.data || {}
    } catch (error) {
      console.error('Failed to load analytics:', error)
    } finally {
      loading = false
    }
  }

  $: if (selectedTimeframe) {
    loadAnalytics()
  }

  $: statusDistribution = analyticsData ? [
    { name: 'Approved', value: analyticsData.approved_count, color: '#10b981' },
    { name: 'Pending', value: analyticsData.pending_count, color: '#f59e0b' },
    { name: 'Rejected', value: analyticsData.rejected_count, color: '#ef4444' }
  ] : []

  $: approvalRate = analyticsData && analyticsData.total_count > 0 
    ? Math.round((analyticsData.approved_count / analyticsData.total_count) * 100) 
    : 0

  const renderTooltip = (active: boolean, payload: any[]) => {
    if (!active || !payload?.length) return null
    
    return (
      `<div class="bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg">
        ${payload.map(p => `
          <p class="text-sm flex items-center gap-2">
            <span class="w-3 h-3 rounded-full" style="background-color: ${p.color}"></span>
            ${p.name}: ${p.value}
          </p>
        `).join('')}
      </div>`
    )
  }
</script>

<div class="space-y-6">
  <!-- Controls -->
  <div class="flex items-center justify-between">
    <h2 class="text-xl font-semibold flex items-center gap-2">
      <Activity class="w-5 h-5" />
      Registration Analytics
    </h2>
    
    <div class="flex items-center gap-2">
      <Calendar class="w-4 h-4 text-gray-500" />
      <select 
        bind:value={selectedTimeframe}
        class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-sm focus:ring-2 focus:ring-purple-500"
      >
        {#each timeframeOptions as option}
          <option value={option.value}>{option.label}</option>
        {/each}
      </select>
    </div>
  </div>

  {#if loading}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {#each Array(4) as _}
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border animate-pulse">
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
          <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      {/each}
    </div>
  {:else if analyticsData}
    <!-- Key Metrics Row -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Total Registrations -->
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Total Registrations</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">{analyticsData.total_count}</p>
          </div>
          <div class="p-3 bg-purple-500 rounded-full">
            <Users class="w-6 h-6 text-white" />
          </div>
        </div>
        <p class="text-xs text-gray-500 mt-2">Last {selectedTimeframe}</p>
      </div>

      <!-- Approval Rate -->
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Approval Rate</p>
            <p class="text-2xl font-bold text-green-600 mt-1">{approvalRate}%</p>
          </div>
          <div class="p-3 bg-green-500 rounded-full">
            <Target class="w-6 h-6 text-white" />
          </div>
        </div>
        <p class="text-xs text-gray-500 mt-2">Success rate</p>
      </div>

      <!-- Avg Processing Time -->
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Avg Processing</p>
            <p class="text-2xl font-bold text-blue-600 mt-1">{Math.round(analyticsData.avg_approval_time_hours / 24)}d</p>
          </div>
          <div class="p-3 bg-blue-500 rounded-full">
            <Clock class="w-6 h-6 text-white" />
          </div>
        </div>
        <p class="text-xs text-gray-500 mt-2">Days to approve</p>
      </div>

      <!-- Domain Detection -->
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Domain Matches</p>
            <p class="text-2xl font-bold text-orange-600 mt-1">{analyticsData.existing_domain_count}</p>
          </div>
          <div class="p-3 bg-orange-500 rounded-full">
            <TrendingUp class="w-6 h-6 text-white" />
          </div>
        </div>
        <p class="text-xs text-gray-500 mt-2">Existing customers</p>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Registration Trends -->
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <h3 class="text-lg font-semibold mb-4">Registration Trends</h3>
        <div style="height: 300px">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis dataKey="date" className="text-xs fill-gray-600 dark:fill-gray-300" />
              <YAxis className="text-xs fill-gray-600 dark:fill-gray-300" />
              <Tooltip content={renderTooltip} />
              <Line type="monotone" dataKey="total" stroke="#8b5cf6" strokeWidth={2} name="Total" />
              <Line type="monotone" dataKey="approved" stroke="#10b981" strokeWidth={2} name="Approved" />
              <Line type="monotone" dataKey="pending" stroke="#f59e0b" strokeWidth={2} name="Pending" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      <!-- Status Distribution -->
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <h3 class="text-lg font-semibold mb-4">Status Distribution</h3>
        <div style="height: 300px">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={statusDistribution}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {#each statusDistribution as entry, index}
                  <Cell key="cell-{index}" fill={entry.color} />
                {/each}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        <!-- Legend -->
        <div class="flex justify-center gap-4 mt-4">
          {#each statusDistribution as item}
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 rounded-full" style="background-color: {item.color}"></div>
              <span class="text-sm">{item.name}: {item.value}</span>
            </div>
          {/each}
        </div>
      </div>
    </div>

    <!-- Domain Analytics -->
    {#if domainData && domainData.domains}
      <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <h3 class="text-lg font-semibold mb-4">Top Domains ({selectedTimeframe})</h3>
        <div class="overflow-x-auto">
          <table class="w-full text-sm">
            <thead>
              <tr class="border-b border-gray-200 dark:border-gray-700">
                <th class="text-left py-2">Domain</th>
                <th class="text-right py-2">Total</th>
                <th class="text-right py-2">Approved</th>
                <th class="text-right py-2">Detection Rate</th>
              </tr>
            </thead>
            <tbody>
              {#each domainData.domains.slice(0, 8) as domain}
                <tr class="border-b border-gray-100 dark:border-gray-800">
                  <td class="py-2 font-medium">{domain.domain}</td>
                  <td class="text-right py-2">{domain.total}</td>
                  <td class="text-right py-2 text-green-600">{domain.approved}</td>
                  <td class="text-right py-2">
                    <span class="px-2 py-1 text-xs rounded-full
                      {domain.detection_accuracy > 80 ? 'bg-green-100 text-green-700' : 
                       domain.detection_accuracy > 50 ? 'bg-yellow-100 text-yellow-700' : 
                       'bg-red-100 text-red-700'}">
                      {domain.detection_accuracy}%
                    </span>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      </div>
    {/if}
  {/if}
</div>
