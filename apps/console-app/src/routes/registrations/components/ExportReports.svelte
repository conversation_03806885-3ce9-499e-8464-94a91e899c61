<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { Download, FileText, Mail, Calendar, Settings, AlertCircle, CheckCircle } from 'lucide-svelte'

  export let currentData: any[] = []
  export let analyticsData: any = {}
  export let appliedFilters: any = {}

  const dispatch = createEventDispatcher()

  // Export state
  let isExporting = false
  let exportFormat = 'csv'
  let includeCharts = true
  let emailReport = false
  let recipientEmail = ''
  let reportSchedule = 'none'
  let exportSuccess = false
  let exportError = ''

  // Export format options
  const exportFormats = [
    { value: 'csv', label: 'CSV Spreadsheet', icon: FileText, description: 'Comma-separated values for Excel/Sheets' },
    { value: 'pdf', label: 'PDF Report', icon: Download, description: 'Formatted report with charts and analysis' },
    { value: 'json', label: 'JSON Data', icon: Settings, description: 'Raw data for developers' }
  ]

  // Schedule options
  const scheduleOptions = [
    { value: 'none', label: 'One-time export' },
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' }
  ]

  // Export sections
  let exportSections = {
    overview: true,
    registrations: true,
    analytics: true,
    workflow: true,
    domains: true
  }

  // Generate CSV data
  const generateCSV = () => {
    if (!currentData || currentData.length === 0) {
      return 'No data available for export'
    }

    const headers = [
      'Company Name',
      'Contact Email',
      'Status',
      'Domain Type',
      'Submission Date',
      'Approval Date',
      'Processing Days',
      'Business Type',
      'Phone',
      'Address'
    ]

    const rows = currentData.map(item => [
      item.company_name || '',
      item.contact_person_email || '',
      item.status || '',
      item.activated_company_id ? 'Existing Customer' : 'New Company',
      item.created_at ? new Date(item.created_at).toLocaleDateString() : '',
      item.updated_at && item.status === 'approved' ? new Date(item.updated_at).toLocaleDateString() : '',
      item.processing_days || '',
      item.business_type || '',
      item.contact_person_phone || '',
      item.business_address || ''
    ])

    return [headers, ...rows].map(row => 
      row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
    ).join('\n')
  }

  // Generate analytics summary for PDF
  const generateAnalyticsSummary = () => {
    return {
      totalRegistrations: analyticsData.total_count || 0,
      pendingReviews: analyticsData.pending_count || 0,
      approvalRate: analyticsData.total_count > 0 
        ? Math.round((analyticsData.approved_count / analyticsData.total_count) * 100) 
        : 0,
      avgProcessingTime: analyticsData.avg_approval_time_hours || 0,
      newCompanies: analyticsData.new_company_count || 0,
      existingCustomers: analyticsData.existing_domain_count || 0,
      generatedAt: new Date().toLocaleString(),
      filterSummary: generateFilterSummary()
    }
  }

  // Generate filter summary
  const generateFilterSummary = () => {
    const filters = []
    if (appliedFilters.search) filters.push(`Search: "${appliedFilters.search}"`)
    if (appliedFilters.status !== 'all') filters.push(`Status: ${appliedFilters.status}`)
    if (appliedFilters.domainType !== 'all') filters.push(`Domain: ${appliedFilters.domainType}`)
    if (appliedFilters.timeRange !== '30d') filters.push(`Period: ${appliedFilters.timeRange}`)
    return filters.length > 0 ? filters.join(', ') : 'No filters applied'
  }

  // Export function
  const performExport = async () => {
    if (isExporting) return

    isExporting = true
    exportError = ''
    exportSuccess = false

    try {
      let exportData: any = {
        format: exportFormat,
        timestamp: new Date().toISOString(),
        sections: exportSections,
        filters: appliedFilters
      }

      if (exportFormat === 'csv') {
        const csvContent = generateCSV()
        downloadFile(csvContent, `registration-data-${Date.now()}.csv`, 'text/csv')
      } 
      else if (exportFormat === 'pdf') {
        const summary = generateAnalyticsSummary()
        await generatePDFReport(summary)
      }
      else if (exportFormat === 'json') {
        const jsonContent = JSON.stringify({
          ...exportData,
          data: currentData,
          analytics: analyticsData
        }, null, 2)
        downloadFile(jsonContent, `registration-export-${Date.now()}.json`, 'application/json')
      }

      // Email report if requested
      if (emailReport && recipientEmail) {
        await sendEmailReport()
      }

      // Schedule report if requested
      if (reportSchedule !== 'none') {
        await scheduleReport()
      }

      exportSuccess = true
      setTimeout(() => exportSuccess = false, 3000)

    } catch (error) {
      console.error('Export error:', error)
      exportError = (error as Error).message || 'Export failed. Please try again.'
    } finally {
      isExporting = false
    }
  }

  // Download file helper
  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // Generate PDF report (simplified - in real implementation would use jsPDF)
  const generatePDFReport = async (summary: any) => {
    // This is a simplified version. In production, would use jsPDF + charts
    const pdfContent = `
PROCURESERVE II - REGISTRATION ANALYTICS REPORT
Generated: ${summary.generatedAt}

EXECUTIVE SUMMARY
================
Total Registrations: ${summary.totalRegistrations}
Pending Reviews: ${summary.pendingReviews}
Approval Rate: ${summary.approvalRate}%
Avg Processing Time: ${summary.avgProcessingTime} hours
New Companies: ${summary.newCompanies}
Existing Customers: ${summary.existingCustomers}

FILTERS APPLIED
===============
${summary.filterSummary}

DETAILED DATA
=============
${generateCSV()}
    `.trim()

    downloadFile(pdfContent, `registration-report-${Date.now()}.txt`, 'text/plain')
  }

  // Send email report
  const sendEmailReport = async () => {
    const response = await fetch('/api/registrations/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'email',
        recipient: recipientEmail,
        format: exportFormat,
        data: currentData,
        analytics: analyticsData,
        filters: appliedFilters
      })
    })

    if (!response.ok) {
      throw new Error('Failed to send email report')
    }
  }

  // Schedule report
  const scheduleReport = async () => {
    const response = await fetch('/api/registrations/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'schedule',
        schedule: reportSchedule,
        recipient: recipientEmail,
        format: exportFormat,
        sections: exportSections,
        filters: appliedFilters
      })
    })

    if (!response.ok) {
      throw new Error('Failed to schedule report')
    }
  }

  // Clear export status
  const clearStatus = () => {
    exportSuccess = false
    exportError = ''
  }
</script>

<div class="bg-white dark:bg-gray-800 border rounded-lg">
  <!-- Header -->
  <div class="p-6 border-b border-gray-200 dark:border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold flex items-center gap-2">
          <Download class="w-5 h-5" />
          Export & Reports
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Export data and generate reports from current view
        </p>
      </div>
      
      <div class="text-sm text-gray-500">
        {currentData.length} records ready for export
      </div>
    </div>
  </div>

  <!-- Export Configuration -->
  <div class="p-6 space-y-6">
    <!-- Export Format Selection -->
    <div class="space-y-4">
      <h4 class="font-medium text-gray-900 dark:text-white">Export Format</h4>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        {#each exportFormats as format}
          <label class="relative">
            <input
              type="radio"
              bind:group={exportFormat}
              value={format.value}
              class="sr-only"
            />
            <div class="p-4 border-2 rounded-lg cursor-pointer transition-all
              {exportFormat === format.value 
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'}">
              <div class="flex items-center gap-3 mb-2">
                <svelte:component this={format.icon} class="w-5 h-5 text-gray-600 dark:text-gray-400" />
                <span class="font-medium">{format.label}</span>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {format.description}
              </p>
            </div>
          </label>
        {/each}
      </div>
    </div>

    <!-- Export Sections -->
    <div class="space-y-4">
      <h4 class="font-medium text-gray-900 dark:text-white">Include in Export</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <label class="flex items-center gap-3">
          <input
            type="checkbox"
            bind:checked={exportSections.overview}
            class="rounded border-gray-300 dark:border-gray-600"
          />
          <span class="text-sm">Overview Analytics</span>
        </label>
        
        <label class="flex items-center gap-3">
          <input
            type="checkbox"
            bind:checked={exportSections.registrations}
            class="rounded border-gray-300 dark:border-gray-600"
          />
          <span class="text-sm">Registration Data</span>
        </label>
        
        <label class="flex items-center gap-3">
          <input
            type="checkbox"
            bind:checked={exportSections.analytics}
            class="rounded border-gray-300 dark:border-gray-600"
          />
          <span class="text-sm">Trend Analytics</span>
        </label>
        
        <label class="flex items-center gap-3">
          <input
            type="checkbox"
            bind:checked={exportSections.workflow}
            class="rounded border-gray-300 dark:border-gray-600"
          />
          <span class="text-sm">Workflow Metrics</span>
        </label>
        
        <label class="flex items-center gap-3">
          <input
            type="checkbox"
            bind:checked={exportSections.domains}
            class="rounded border-gray-300 dark:border-gray-600"
          />
          <span class="text-sm">Domain Analysis</span>
        </label>

        {#if exportFormat === 'pdf'}
          <label class="flex items-center gap-3">
            <input
              type="checkbox"
              bind:checked={includeCharts}
              class="rounded border-gray-300 dark:border-gray-600"
            />
            <span class="text-sm">Include Charts</span>
          </label>
        {/if}
      </div>
    </div>

    <!-- Email Options -->
    <div class="space-y-4">
      <div class="flex items-center gap-3">
        <input
          type="checkbox"
          id="email-report"
          bind:checked={emailReport}
          class="rounded border-gray-300 dark:border-gray-600"
        />
        <label for="email-report" class="font-medium text-gray-900 dark:text-white">
          Email Report
        </label>
      </div>
      
      {#if emailReport}
        <div class="pl-6 space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Recipient Email
            </label>
            <input
              type="email"
              bind:value={recipientEmail}
              placeholder="Enter email address"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
      {/if}
    </div>

    <!-- Scheduling Options -->
    <div class="space-y-4">
      <h4 class="font-medium text-gray-900 dark:text-white">Report Schedule</h4>
      <select
        bind:value={reportSchedule}
        class="w-full md:w-auto px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
      >
        {#each scheduleOptions as option}
          <option value={option.value}>{option.label}</option>
        {/each}
      </select>
      
      {#if reportSchedule !== 'none'}
        <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div class="flex items-center gap-2 text-blue-800 dark:text-blue-200 mb-2">
            <Calendar class="w-4 h-4" />
            <span class="font-medium">Scheduled Report</span>
          </div>
          <p class="text-sm text-blue-700 dark:text-blue-300">
            This report will be generated and sent {reportSchedule} to the specified email address.
            {emailReport ? `Reports will be sent to: ${recipientEmail}` : 'Please enable email delivery above.'}
          </p>
        </div>
      {/if}
    </div>

    <!-- Export Actions -->
    <div class="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
      <div class="flex items-center gap-4">
        {#if exportSuccess}
          <div class="flex items-center gap-2 text-green-600">
            <CheckCircle class="w-4 h-4" />
            <span class="text-sm font-medium">Export completed successfully!</span>
          </div>
        {/if}
        
        {#if exportError}
          <div class="flex items-center gap-2 text-red-600">
            <AlertCircle class="w-4 h-4" />
            <span class="text-sm font-medium">{exportError}</span>
            <button
              on:click={clearStatus}
              class="text-xs text-red-500 hover:text-red-700 underline"
            >
              Dismiss
            </button>
          </div>
        {/if}
      </div>

      <div class="flex items-center gap-3">
        <button
          on:click={performExport}
          disabled={isExporting || currentData.length === 0 || (emailReport && !recipientEmail)}
          class="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {#if isExporting}
            <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            Exporting...
          {:else}
            <Download class="w-4 h-4" />
            Export {exportFormat.toUpperCase()}
          {/if}
        </button>
      </div>
    </div>

    <!-- Export Preview -->
    {#if currentData.length > 0}
      <div class="bg-gray-50 dark:bg-gray-900/50 p-4 rounded-lg">
        <h5 class="font-medium text-gray-900 dark:text-white mb-3">Export Preview</h5>
        <div class="space-y-2 text-sm">
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Records:</span>
            <span class="font-medium">{currentData.length}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Format:</span>
            <span class="font-medium">{exportFormats.find(f => f.value === exportFormat)?.label}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Sections:</span>
            <span class="font-medium">{Object.values(exportSections).filter(Boolean).length} selected</span>
          </div>
          {#if emailReport}
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Email to:</span>
              <span class="font-medium">{recipientEmail || 'Not specified'}</span>
            </div>
          {/if}
          {#if reportSchedule !== 'none'}
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">Schedule:</span>
              <span class="font-medium">{scheduleOptions.find(s => s.value === reportSchedule)?.label}</span>
            </div>
          {/if}
        </div>
      </div>
    {/if}
  </div>
</div>
