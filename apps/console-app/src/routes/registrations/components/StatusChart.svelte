<script lang="ts">
  import { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'

  interface StatusData {
    status: string
    count: number
    percentage: number
  }

  export let data: StatusData[]
  export let height: number = 300
  export let title: string = 'Status Distribution'

  const statusColors = {
    pending: '#f59e0b',
    approved: '#10b981', 
    rejected: '#ef4444',
    escalated: '#8b5cf6'
  }

  $: chartData = data.map(item => ({
    ...item,
    fill: statusColors[item.status as keyof typeof statusColors] || '#6b7280'
  }))

  const customTooltip = (active: boolean, payload: any[], label: string) => {
    if (!active || !payload?.length) return null
    
    const data = payload[0].payload
    return `
      <div class="bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg">
        <p class="font-semibold capitalize">${data.status}</p>
        <p class="text-sm">Count: ${data.count}</p>
        <p class="text-sm">Percentage: ${data.percentage.toFixed(1)}%</p>
      </div>
    `
  }
</script>

<div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
  <h3 class="text-lg font-semibold mb-4">{title}</h3>
  
  <div style="height: {height}px">
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
        <XAxis 
          dataKey="status" 
          className="text-xs fill-gray-600 dark:fill-gray-300"
          tick={{ fontSize: 12 }}
        />
        <YAxis className="text-xs fill-gray-600 dark:fill-gray-300" />
        <Tooltip content={customTooltip} />
        <Bar dataKey="count" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  </div>
</div>
