<script lang="ts">
  import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from 'recharts'

  interface DomainData {
    name: string
    value: number
    color: string
  }

  export let data: DomainData[]
  export let height: number = 300
  export let title: string = 'Domain Distribution'

  const COLORS = [
    '#8b5cf6', '#10b981', '#f59e0b', '#ef4444', 
    '#3b82f6', '#ec4899', '#06b6d4', '#84cc16'
  ]

  $: chartData = data.map((item, index) => ({
    ...item,
    color: item.color || COLORS[index % COLORS.length]
  }))

  const customTooltip = (active: boolean, payload: any[]) => {
    if (!active || !payload?.length) return null
    
    const data = payload[0].payload
    return `
      <div class="bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg">
        <p class="font-semibold">${data.name}</p>
        <p class="text-sm">Count: ${data.value}</p>
      </div>
    `
  }
</script>

<div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
  <h3 class="text-lg font-semibold mb-4">{title}</h3>
  
  <div style="height: {height}px">
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          data={chartData}
          cx="50%"
          cy="50%"
          outerRadius={80}
          dataKey="value"
          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
        >
          {#each chartData as entry}
            <Cell fill={entry.color} />
          {/each}
        </Pie>
        <Tooltip content={customTooltip} />
      </PieChart>
    </ResponsiveContainer>
  </div>
</div>
