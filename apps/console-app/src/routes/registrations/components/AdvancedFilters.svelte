<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { Calendar, Filter, Search, X, Save, Clock, Building, Mail, CheckCircle } from 'lucide-svelte'
  import { format, subDays, subWeeks, subMonths } from 'date-fns'

  export let appliedFilters: any = {}
  export let savedPresets: any[] = []

  const dispatch = createEventDispatcher()

  // Filter state
  let showFilters = false
  let searchQuery = ''
  let selectedStatus = 'all'
  let selectedDomainType = 'all'
  let selectedTimeRange = '30d'
  let customDateRange = { start: '', end: '' }
  let selectedCompanyType = 'all'
  let isAdvancedMode = false

  // Date range presets
  const timeRangePresets = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 3 months' },
    { value: '1y', label: 'Last year' },
    { value: 'custom', label: 'Custom range' }
  ]

  // Status options
  const statusOptions = [
    { value: 'all', label: 'All Statuses', icon: Filter },
    { value: 'pending', label: 'Pending Review', icon: Clock },
    { value: 'approved', label: 'Approved', icon: CheckCircle },
    { value: 'rejected', label: 'Rejected', icon: X },
    { value: 'escalated', label: 'Escalated', icon: Calendar }
  ]

  // Domain type options
  const domainTypeOptions = [
    { value: 'all', label: 'All Domains' },
    { value: 'new_company', label: 'New Companies' },
    { value: 'existing_customer', label: 'Existing Customers' },
    { value: 'undetected', label: 'Detection Failed' }
  ]

  // Company type options
  const companyTypeOptions = [
    { value: 'all', label: 'All Types' },
    { value: 'staffing_agency', label: 'Staffing Agency' },
    { value: 'direct_employer', label: 'Direct Employer' },
    { value: 'consulting_firm', label: 'Consulting Firm' },
    { value: 'other', label: 'Other' }
  ]

  // Debounced search
  let searchTimeout: NodeJS.Timeout
  const debounceSearch = (query: string) => {
    clearTimeout(searchTimeout)
    searchTimeout = setTimeout(() => {
      searchQuery = query
      applyFilters()
    }, 300)
  }

  // Apply filters
  const applyFilters = () => {
    const filters = {
      search: searchQuery,
      status: selectedStatus,
      domainType: selectedDomainType,
      timeRange: selectedTimeRange,
      customDateRange: selectedTimeRange === 'custom' ? customDateRange : null,
      companyType: selectedCompanyType,
      isAdvanced: isAdvancedMode
    }

    dispatch('filtersChanged', filters)
  }

  // Clear all filters
  const clearFilters = () => {
    searchQuery = ''
    selectedStatus = 'all'
    selectedDomainType = 'all'
    selectedTimeRange = '30d'
    customDateRange = { start: '', end: '' }
    selectedCompanyType = 'all'
    isAdvancedMode = false
    applyFilters()
  }

  // Save current filters as preset
  const saveAsPreset = () => {
    const presetName = prompt('Enter a name for this filter preset:')
    if (!presetName) return

    const preset = {
      id: Date.now().toString(),
      name: presetName,
      filters: {
        search: searchQuery,
        status: selectedStatus,
        domainType: selectedDomainType,
        timeRange: selectedTimeRange,
        customDateRange: { ...customDateRange },
        companyType: selectedCompanyType,
        isAdvanced: isAdvancedMode
      },
      createdAt: new Date().toISOString()
    }

    dispatch('savePreset', preset)
  }

  // Load preset
  const loadPreset = (preset: any) => {
    const filters = preset.filters
    searchQuery = filters.search || ''
    selectedStatus = filters.status || 'all'
    selectedDomainType = filters.domainType || 'all'
    selectedTimeRange = filters.timeRange || '30d'
    customDateRange = filters.customDateRange || { start: '', end: '' }
    selectedCompanyType = filters.companyType || 'all'
    isAdvancedMode = filters.isAdvanced || false
    applyFilters()
  }

  // Delete preset
  const deletePreset = (presetId: string) => {
    if (confirm('Are you sure you want to delete this preset?')) {
      dispatch('deletePreset', presetId)
    }
  }

  // Get active filter count
  $: activeFilterCount = [
    searchQuery !== '',
    selectedStatus !== 'all',
    selectedDomainType !== 'all',
    selectedTimeRange !== '30d',
    selectedCompanyType !== 'all'
  ].filter(Boolean).length

  // Format date for input
  const formatDateForInput = (date: Date) => {
    return format(date, 'yyyy-MM-dd')
  }

  // Set quick date ranges
  const setQuickDateRange = (days: number) => {
    const end = new Date()
    const start = subDays(end, days)
    customDateRange = {
      start: formatDateForInput(start),
      end: formatDateForInput(end)
    }
    selectedTimeRange = 'custom'
    applyFilters()
  }
</script>

<div class="bg-white dark:bg-gray-800 border rounded-lg">
  <!-- Filter Header -->
  <div class="p-4 border-b border-gray-200 dark:border-gray-700">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-3">
        <button
          on:click={() => showFilters = !showFilters}
          class="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Filter class="w-4 h-4" />
          Filters
          {#if activeFilterCount > 0}
            <span class="px-2 py-1 bg-blue-800 text-xs rounded-full">
              {activeFilterCount}
            </span>
          {/if}
        </button>

        {#if activeFilterCount > 0}
          <button
            on:click={clearFilters}
            class="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 transition-colors"
          >
            <X class="w-4 h-4" />
            Clear all
          </button>
        {/if}

        <div class="flex items-center gap-2">
          <input
            type="checkbox"
            id="advanced-mode"
            bind:checked={isAdvancedMode}
            on:change={applyFilters}
            class="rounded border-gray-300 dark:border-gray-600"
          />
          <label for="advanced-mode" class="text-sm text-gray-600 dark:text-gray-400">
            Advanced mode
          </label>
        </div>
      </div>

      <div class="flex items-center gap-2">
        <button
          on:click={saveAsPreset}
          class="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 transition-colors"
          disabled={activeFilterCount === 0}
        >
          <Save class="w-4 h-4" />
          Save preset
        </button>
      </div>
    </div>
  </div>

  <!-- Filter Controls -->
  {#if showFilters}
    <div class="p-6 space-y-6 border-b border-gray-200 dark:border-gray-700">
      <!-- Search Bar -->
      <div class="space-y-2">
        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
          Search
        </label>
        <div class="relative">
          <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search by company name, email, or domain..."
            on:input={(e) => debounceSearch((e.target as HTMLInputElement).value)}
            value={searchQuery}
            class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>
      </div>

      <!-- Filter Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Status Filter -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            Status
          </label>
          <select
            bind:value={selectedStatus}
            on:change={applyFilters}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          >
            {#each statusOptions as option}
              <option value={option.value}>{option.label}</option>
            {/each}
          </select>
        </div>

        <!-- Domain Type Filter -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            Domain Type
          </label>
          <select
            bind:value={selectedDomainType}
            on:change={applyFilters}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          >
            {#each domainTypeOptions as option}
              <option value={option.value}>{option.label}</option>
            {/each}
          </select>
        </div>

        <!-- Time Range Filter -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            Time Range
          </label>
          <select
            bind:value={selectedTimeRange}
            on:change={applyFilters}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          >
            {#each timeRangePresets as preset}
              <option value={preset.value}>{preset.label}</option>
            {/each}
          </select>
        </div>

        <!-- Company Type Filter -->
        {#if isAdvancedMode}
          <div class="space-y-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
              Company Type
            </label>
            <select
              bind:value={selectedCompanyType}
              on:change={applyFilters}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            >
              {#each companyTypeOptions as option}
                <option value={option.value}>{option.label}</option>
              {/each}
            </select>
          </div>
        {/if}
      </div>

      <!-- Custom Date Range -->
      {#if selectedTimeRange === 'custom'}
        <div class="space-y-4">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            Custom Date Range
          </label>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="text-xs text-gray-500 mb-1 block">Start Date</label>
              <input
                type="date"
                bind:value={customDateRange.start}
                on:change={applyFilters}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label class="text-xs text-gray-500 mb-1 block">End Date</label>
              <input
                type="date"
                bind:value={customDateRange.end}
                on:change={applyFilters}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
          
          <!-- Quick Date Buttons -->
          <div class="flex flex-wrap gap-2">
            <button
              on:click={() => setQuickDateRange(7)}
              class="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              Last 7 days
            </button>
            <button
              on:click={() => setQuickDateRange(30)}
              class="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              Last 30 days
            </button>
            <button
              on:click={() => setQuickDateRange(90)}
              class="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              Last 90 days
            </button>
          </div>
        </div>
      {/if}
    </div>
  {/if}

  <!-- Saved Presets -->
  {#if savedPresets.length > 0}
    <div class="p-4 bg-gray-50 dark:bg-gray-900/50">
      <div class="flex items-center justify-between mb-3">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
          Saved Filter Presets
        </h4>
      </div>
      
      <div class="flex flex-wrap gap-2">
        {#each savedPresets as preset}
          <div class="flex items-center gap-2 px-3 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
            <button
              on:click={() => loadPreset(preset)}
              class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
            >
              {preset.name}
            </button>
            <button
              on:click={() => deletePreset(preset.id)}
              class="text-gray-400 hover:text-red-600 transition-colors"
            >
              <X class="w-3 h-3" />
            </button>
          </div>
        {/each}
      </div>
    </div>
  {/if}

  <!-- Applied Filters Summary -->
  {#if activeFilterCount > 0}
    <div class="p-4 bg-blue-50 dark:bg-blue-900/20 border-t border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2 text-sm text-blue-800 dark:text-blue-200">
          <Filter class="w-4 h-4" />
          <span class="font-medium">{activeFilterCount} filter{activeFilterCount > 1 ? 's' : ''} applied</span>
        </div>
        
        <div class="flex flex-wrap gap-2">
          {#if searchQuery}
            <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full">
              Search: "{searchQuery}"
            </span>
          {/if}
          {#if selectedStatus !== 'all'}
            <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full">
              Status: {statusOptions.find(s => s.value === selectedStatus)?.label}
            </span>
          {/if}
          {#if selectedDomainType !== 'all'}
            <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full">
              Domain: {domainTypeOptions.find(d => d.value === selectedDomainType)?.label}
            </span>
          {/if}
          {#if selectedTimeRange !== '30d'}
            <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full">
              Time: {timeRangePresets.find(t => t.value === selectedTimeRange)?.label}
            </span>
          {/if}
          {#if selectedCompanyType !== 'all'}
            <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full">
              Type: {companyTypeOptions.find(c => c.value === selectedCompanyType)?.label}
            </span>
          {/if}
        </div>
      </div>
    </div>
  {/if}
</div>
