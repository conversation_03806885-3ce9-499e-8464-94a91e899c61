<script lang="ts">
  import { Check, Clock, AlertTriangle, X, ArrowDown } from 'lucide-svelte'

  interface WorkflowStep {
    id: string
    title: string
    description: string
    status: 'completed' | 'current' | 'pending' | 'error'
    timestamp?: string
    details?: string
  }

  export let steps: WorkflowStep[]
  export let currentStep: string = ''

  function getStepIcon(status: string) {
    switch(status) {
      case 'completed': return Check
      case 'current': return Clock
      case 'error': return X
      default: return Clock
    }
  }

  function getStepColor(status: string) {
    switch(status) {
      case 'completed': return 'bg-green-500'
      case 'current': return 'bg-blue-500'
      case 'error': return 'bg-red-500'
      default: return 'bg-gray-300'
    }
  }

  function getConnectorColor(stepStatus: string, nextStepStatus: string) {
    if (stepStatus === 'completed') return 'bg-green-500'
    if (stepStatus === 'current' && nextStepStatus === 'current') return 'bg-blue-500'
    if (stepStatus === 'error') return 'bg-red-500'
    return 'bg-gray-300'
  }
</script>

<div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
  <h3 class="text-lg font-semibold mb-6">Registration Workflow Status</h3>
  
  <div class="relative">
    {#each steps as step, index}
      <div class="flex items-start gap-4 relative">
        <!-- Step Icon -->
        <div class="flex-shrink-0 relative z-10">
          <div class="w-10 h-10 {getStepColor(step.status)} rounded-full flex items-center justify-center">
            <svelte:component this={getStepIcon(step.status)} class="w-5 h-5 text-white" />
          </div>
        </div>

        <!-- Step Content -->
        <div class="flex-1 min-w-0 pb-8">
          <div class="flex items-center justify-between">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white">
              {step.title}
            </h4>
            {#if step.timestamp}
              <span class="text-xs text-gray-500">
                {step.timestamp}
              </span>
            {/if}
          </div>
          
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            {step.description}
          </p>
          
          {#if step.details}
            <p class="mt-2 text-xs text-gray-500 bg-gray-50 dark:bg-gray-700 p-2 rounded">
              {step.details}
            </p>
          {/if}
        </div>

        <!-- Connector Line -->
        {#if index < steps.length - 1}
          <div class="absolute left-5 top-10 w-0.5 h-8 {getConnectorColor(step.status, steps[index + 1].status)}"></div>
        {/if}
      </div>
    {/each}
  </div>
</div>
