<script lang="ts">
  import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
  import { format } from 'date-fns'

  interface TrendData {
    date: string
    total: number
    approved: number
    pending: number
    rejected: number
  }

  export let data: TrendData[]
  export let height: number = 300
  export let title: string = 'Registration Trends'

  // Process data for daily aggregation
  $: processedData = data.reduce((acc, item) => {
    const date = format(new Date(item.date), 'MMM dd')
    const existing = acc.find(d => d.date === date)
    
    if (existing) {
      existing.total += item.total
      existing.approved += item.approved
      existing.pending += item.pending
      existing.rejected += item.rejected
    } else {
      acc.push({ date, ...item })
    }
    
    return acc
  }, [] as TrendData[])

  const customTooltip = (active: boolean, payload: any[], label: string) => {
    if (!active || !payload?.length) return null
    
    return `
      <div class="bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg">
        <p class="font-semibold">${label}</p>
        ${payload.map(p => `
          <p class="text-sm" style="color: ${p.color}">
            ${p.name}: ${p.value}
          </p>
        `).join('')}
      </div>
    `
  }
</script>

<div class="bg-white dark:bg-gray-800 p-6 rounded-lg border">
  <h3 class="text-lg font-semibold mb-4">{title}</h3>
  
  <div style="height: {height}px">
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={processedData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
        <XAxis 
          dataKey="date" 
          className="text-xs fill-gray-600 dark:fill-gray-300"
        />
        <YAxis className="text-xs fill-gray-600 dark:fill-gray-300" />
        <Tooltip content={customTooltip} />
        
        <Line 
          type="monotone" 
          dataKey="total" 
          stroke="#8b5cf6" 
          strokeWidth={2}
          name="Total"
          dot={{ r: 4 }}
        />
        <Line 
          type="monotone" 
          dataKey="approved" 
          stroke="#10b981" 
          strokeWidth={2}
          name="Approved"
          dot={{ r: 3 }}
        />
        <Line 
          type="monotone" 
          dataKey="pending" 
          stroke="#f59e0b" 
          strokeWidth={2}
          name="Pending"
          dot={{ r: 3 }}
        />
        <Line 
          type="monotone" 
          dataKey="rejected" 
          stroke="#ef4444" 
          strokeWidth={2}
          name="Rejected"
          dot={{ r: 3 }}
        />
      </LineChart>
    </ResponsiveContainer>
  </div>
</div>
