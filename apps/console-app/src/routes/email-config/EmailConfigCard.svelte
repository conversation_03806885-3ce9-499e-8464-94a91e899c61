<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { enhance } from '$app/forms'

  export let config: any
  
  const dispatch = createEventDispatcher()

  let showTestModal = false
  let testEmail = ''

  const getProviderColor = (provider: string) => {
    const colors = {
      supabase: 'bg-green-100 text-green-800',
      zeptomail: 'bg-blue-100 text-blue-800',
      smtp: 'bg-purple-100 text-purple-800',
      ses: 'bg-orange-100 text-orange-800',
      resend: 'bg-pink-100 text-pink-800'
    }
    return colors[provider] || 'bg-gray-100 text-gray-800'
  }

  const formatLastUsed = (dateString: string | null) => {
    if (!dateString) return 'Never'
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
    return date.toLocaleDateString()
  }
</script>

<div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
  <div class="p-6">
    <div class="flex items-center justify-between">
      <div class="flex-1">
        <div class="flex items-center space-x-3 mb-3">
          <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full {getProviderColor(config.provider_type)}">
            {config.provider_type.toUpperCase()}
          </span>
          
          {#if config.is_primary}
            <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800">
              PRIMARY
            </span>
          {/if}
          
          <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full {config.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
            {config.is_active ? 'ACTIVE' : 'INACTIVE'}
          </span>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <dt class="text-xs font-medium text-gray-500 uppercase">From Email</dt>
            <dd class="mt-1 text-sm text-gray-900">{config.from_email}</dd>
          </div>
          
          <div>
            <dt class="text-xs font-medium text-gray-500 uppercase">From Name</dt>
            <dd class="mt-1 text-sm text-gray-900">{config.from_name}</dd>
          </div>
          
          <div>
            <dt class="text-xs font-medium text-gray-500 uppercase">Daily Count</dt>
            <dd class="mt-1 text-sm text-gray-900">{config.daily_email_count}</dd>
          </div>
          
          <div>
            <dt class="text-xs font-medium text-gray-500 uppercase">Last Used</dt>
            <dd class="mt-1 text-sm text-gray-900">{formatLastUsed(config.last_used_at)}</dd>
          </div>
        </div>

        <!-- Capabilities -->
        <div class="mt-4">
          <dt class="text-xs font-medium text-gray-500 uppercase mb-2">Handles</dt>
          <div class="flex flex-wrap gap-2">
            {#if config.handles_invitations}
              <span class="inline-flex px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded">
                User Invitations
              </span>
            {/if}
            {#if config.handles_password_resets}
              <span class="inline-flex px-2 py-1 text-xs bg-yellow-50 text-yellow-700 rounded">
                Password Resets
              </span>
            {/if}
            {#if config.handles_notifications}
              <span class="inline-flex px-2 py-1 text-xs bg-green-50 text-green-700 rounded">
                Notifications
              </span>
            {/if}
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center space-x-2 ml-6">
        <button
          type="button"
          on:click={() => showTestModal = true}
          class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
        >
          Test
        </button>

        {#if !config.is_primary}
          <form method="POST" action="?/setPrimary" use:enhance>
            <input type="hidden" name="config_id" value={config.id} />
            <button
              type="submit"
              class="px-3 py-1 text-xs bg-indigo-100 text-indigo-700 rounded hover:bg-indigo-200"
            >
              Set Primary
            </button>
          </form>
        {/if}

        <button
          type="button"
          on:click={() => dispatch('edit')}
          class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
        >
          Edit
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Test Email Modal -->
{#if showTestModal}
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Test Email Configuration</h3>
        
        <form method="POST" action="?/testConfig" use:enhance>
          <input type="hidden" name="config_id" value={config.id} />
          
          <div class="mb-4">
            <label for="test_email" class="block text-sm font-medium text-gray-700 mb-2">
              Test Email Address
            </label>
            <input
              type="email"
              id="test_email"
              name="test_email"
              bind:value={testEmail}
              required
              placeholder="<EMAIL>"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          
          <div class="flex justify-end space-x-3">
            <button
              type="button"
              on:click={() => showTestModal = false}
              class="px-4 py-2 bg-white text-gray-700 text-sm font-medium border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            
            <button
              type="submit"
              disabled={!testEmail}
              class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 disabled:opacity-50"
            >
              Send Test Email
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
{/if}
