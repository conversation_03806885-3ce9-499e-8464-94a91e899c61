import type { PageServerLoad, Actions } from './$types'
import { error, redirect, fail } from '@sveltejs/kit'
import type { EmailServiceConfig, EmailConfigForm } from '../../../packages/shared-types'

export const load: PageServerLoad = async ({ locals: { supabase, user, session } }) => {
  if (!session || !user) {
    throw redirect(302, '/login')
  }

  // Only admin users can configure email settings (allow both admin and super_admin)
  if (!['admin', 'super_admin'].includes(user.role)) {
    throw error(403, 'Only admin users can configure email settings')
  }

  // Load email configurations
  const { data: emailConfigs, error: configError } = await supabase
    .from('email_service_config')
    .select('*')
    .eq('company_id', user.company_id)
    .order('is_primary', { ascending: false })

  if (configError) {
    console.error('Email config load error:', configError)
  }

  return {
    emailConfigs: emailConfigs || [],
    supportedProviders: [
      { value: 'supabase', label: 'Supabase (Development)', description: 'Free for development and testing' },
      { value: 'resend', label: 'Resend', description: 'Developer-friendly email API (Recommended)' },
      { value: 'smtp', label: 'SMTP/Gmail', description: 'Custom SMTP or Gmail business account' },
      { value: 'ses', label: 'Amazon SES', description: 'Cost-effective for high volume' }
    ]
  }
}

export const actions: Actions = {
  createConfig: async ({ request, locals: { supabase, user, session } }) => {
    if (!session || !user || !['admin', 'super_admin'].includes(user.role)) {
      return fail(403, { message: 'Unauthorized' })
    }

    const formData = await request.formData()
    const configData: EmailConfigForm = {
      provider_type: formData.get('provider_type') as any,
      from_email: formData.get('from_email') as string,
      from_name: formData.get('from_name') as string,
      reply_to_email: formData.get('reply_to_email') as string || undefined,
      api_key: formData.get('api_key') as string || undefined,
      smtp_host: formData.get('smtp_host') as string || undefined,
      smtp_port: parseInt(formData.get('smtp_port') as string) || undefined,
      smtp_username: formData.get('smtp_username') as string || undefined,
      smtp_password: formData.get('smtp_password') as string || undefined,
      handles_invitations: formData.get('handles_invitations') === 'true',
      handles_password_resets: formData.get('handles_password_resets') === 'true',
      handles_notifications: formData.get('handles_notifications') === 'true',
      monthly_email_limit: parseInt(formData.get('monthly_email_limit') as string) || undefined
    }

    if (!configData.provider_type || !configData.from_email || !configData.from_name) {
      return fail(400, { message: 'Provider type, from email, and from name are required' })
    }

    const { data: existingConfigs } = await supabase
      .from('email_service_config')
      .select('id')
      .eq('company_id', user.company_id)

    const isPrimary = !existingConfigs || existingConfigs.length === 0

    const { error: createError } = await supabase
      .from('email_service_config')
      .insert({
        company_id: user.company_id,
        ...configData,
        is_active: true,
        is_primary: isPrimary
      })

    if (createError) {
      return fail(500, { message: 'Failed to create email configuration' })
    }

    return { success: true, message: 'Email configuration created successfully' }
  },

  setPrimary: async ({ request, locals: { supabase, user, session } }) => {
    if (!session || !user || !['admin', 'super_admin'].includes(user.role)) {
      return fail(403, { message: 'Unauthorized' })
    }

    const formData = await request.formData()
    const configId = formData.get('config_id') as string

    await supabase
      .from('email_service_config')
      .update({ is_primary: false })
      .eq('company_id', user.company_id)

    const { error: setPrimaryError } = await supabase
      .from('email_service_config')
      .update({ is_primary: true })
      .eq('id', configId)
      .eq('company_id', user.company_id)

    if (setPrimaryError) {
      return fail(500, { message: 'Failed to set primary configuration' })
    }

    return { success: true, message: 'Primary email configuration updated' }
  },

  testConfig: async ({ request, locals: { supabase, user, session } }) => {
    if (!session || !user || !['admin', 'super_admin'].includes(user.role)) {
      return fail(403, { message: 'Unauthorized' })
    }

    const formData = await request.formData()
    const configId = formData.get('config_id') as string
    const testEmail = formData.get('test_email') as string

    // Import email service
    const { createEmailService } = await import('@psii/email-service')
    const emailService = createEmailService(supabase)

    const result = await emailService.testEmailConfig(configId, testEmail)

    if (result.success) {
      return { success: true, message: 'Test email sent successfully' }
    } else {
      return fail(500, { message: `Test failed: ${result.error}` })
    }
  }
}
