<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { enhance } from '$app/forms'
  import type { EmailProviderType } from '../../../packages/shared-types'

  export let config: any = null
  export let supportedProviders: any[]

  const dispatch = createEventDispatcher()

  let formData = {
    provider_type: config?.provider_type || 'supabase' as EmailProviderType,
    from_email: config?.from_email || '',
    from_name: config?.from_name || 'ProcureServe',
    reply_to_email: config?.reply_to_email || '',
    api_key: config?.api_key || '',
    smtp_host: config?.smtp_host || '',
    smtp_port: config?.smtp_port || 587,
    smtp_username: config?.smtp_username || '',
    smtp_password: config?.smtp_password || '',
    handles_invitations: config?.handles_invitations ?? true,
    handles_password_resets: config?.handles_password_resets ?? true,
    handles_notifications: config?.handles_notifications ?? true,
    monthly_email_limit: config?.monthly_email_limit || 10000
  }

  let loading = false
  const isEditing = !!config

  $: selectedProvider = supportedProviders.find(p => p.value === formData.provider_type)
  $: showApiFields = ['zeptomail', 'ses', 'resend'].includes(formData.provider_type)
  $: showSmtpFields = formData.provider_type === 'smtp'
</script>

<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-8 mx-auto p-0 border w-full max-w-3xl shadow-lg rounded-lg bg-white">
    
    <!-- Header -->
    <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">
        {isEditing ? 'Edit Email Configuration' : 'Add Email Provider'}
      </h3>
      <button 
        type="button"
        on:click={() => dispatch('close')}
        class="text-gray-400 hover:text-gray-600"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Form -->
    <form 
      method="POST" 
      action={isEditing ? '?/updateConfig' : '?/createConfig'}
      use:enhance={() => {
        loading = true
        return async ({ result }) => {
          loading = false
          if (result.type === 'success') {
            dispatch('close')
          }
        }
      }}
      class="px-6 py-4 space-y-6"
    >
      {#if isEditing}
        <input type="hidden" name="config_id" value={config.id} />
      {/if}

      <!-- Provider Selection -->
      <div>
        <label for="provider_type" class="block text-sm font-medium text-gray-700 mb-2">
          Email Provider *
        </label>
        <select
          id="provider_type"
          name="provider_type"
          bind:value={formData.provider_type}
          disabled={isEditing}
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100"
        >
          {#each supportedProviders as provider}
            <option value={provider.value}>{provider.label}</option>
          {/each}
        </select>
        {#if selectedProvider}
          <p class="mt-1 text-xs text-gray-500">{selectedProvider.description}</p>
        {/if}
      </div>

      <!-- Basic Configuration -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="from_email" class="block text-sm font-medium text-gray-700 mb-2">
            From Email *
          </label>
          <input
            type="email"
            id="from_email"
            name="from_email"
            bind:value={formData.from_email}
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          />
        </div>

        <div>
          <label for="from_name" class="block text-sm font-medium text-gray-700 mb-2">
            From Name *
          </label>
          <input
            type="text"
            id="from_name"
            name="from_name"
            bind:value={formData.from_name}
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          />
        </div>
      </div>

      <!-- API Fields -->
      {#if showApiFields}
        <div>
          <label for="api_key" class="block text-sm font-medium text-gray-700 mb-2">
            API Key *
          </label>
          <input
            type="password"
            id="api_key"
            name="api_key"
            bind:value={formData.api_key}
            required={showApiFields}
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          />
        </div>
      {/if}

      <!-- SMTP Fields -->
      {#if showSmtpFields}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="smtp_host" class="block text-sm font-medium text-gray-700 mb-2">
              SMTP Host *
            </label>
            <input
              type="text"
              id="smtp_host"
              name="smtp_host"
              bind:value={formData.smtp_host}
              required={showSmtpFields}
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          <div>
            <label for="smtp_username" class="block text-sm font-medium text-gray-700 mb-2">
              Username *
            </label>
            <input
              type="text"
              id="smtp_username"
              name="smtp_username"
              bind:value={formData.smtp_username}
              required={showSmtpFields}
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
        </div>
        <div>
          <label for="smtp_password" class="block text-sm font-medium text-gray-700 mb-2">
            Password *
          </label>
          <input
            type="password"
            id="smtp_password"
            name="smtp_password"
            bind:value={formData.smtp_password}
            required={showSmtpFields}
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          />
        </div>
      {/if}

      <!-- Capabilities -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">Email Types</label>
        <div class="space-y-2">
          <label class="flex items-center">
            <input
              type="checkbox"
              name="handles_invitations"
              value="true"
              bind:checked={formData.handles_invitations}
              class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
            />
            <span class="ml-3 text-sm text-gray-700">User Invitations</span>
          </label>
          <label class="flex items-center">
            <input
              type="checkbox"
              name="handles_password_resets"
              value="true"
              bind:checked={formData.handles_password_resets}
              class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
            />
            <span class="ml-3 text-sm text-gray-700">Password Resets</span>
          </label>
          <label class="flex items-center">
            <input
              type="checkbox"
              name="handles_notifications"
              value="true"
              bind:checked={formData.handles_notifications}
              class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
            />
            <span class="ml-3 text-sm text-gray-700">Notifications</span>
          </label>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          on:click={() => dispatch('close')}
          class="px-4 py-2 bg-white text-gray-700 text-sm font-medium border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Cancel
        </button>
        
        <button
          type="submit"
          disabled={loading || !formData.from_email || !formData.from_name}
          class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 disabled:opacity-50"
        >
          {loading ? 'Saving...' : (isEditing ? 'Update Configuration' : 'Add Provider')}
        </button>
      </div>
    </form>
  </div>
</div>
