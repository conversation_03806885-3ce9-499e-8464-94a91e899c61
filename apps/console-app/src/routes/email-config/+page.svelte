<script lang="ts">
  import type { PageData } from './$types'
  import EmailConfigCard from './EmailConfigCard.svelte'
  import EmailConfigForm from './EmailConfigForm.svelte'

  export let data: PageData
  export let form: any

  $: ({ emailConfigs, supportedProviders } = data)

  let showCreateForm = false
  let editingConfig: any = null

  const handleCreateNew = () => {
    editingConfig = null
    showCreateForm = true
  }

  const handleEdit = (config: any) => {
    editingConfig = config
    showCreateForm = true
  }

  const handleFormClose = () => {
    showCreateForm = false
    editingConfig = null
  }

  $: primaryConfig = emailConfigs.find(config => config.is_primary)
  $: activeConfigs = emailConfigs.filter(config => config.is_active)
</script>

<svelte:head>
  <title>Email Configuration | Console</title>
</svelte:head>

<div class="max-w-6xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Email Configuration</h1>
        <p class="mt-1 text-sm text-gray-600">
          Configure email providers for user invitations, password resets, and notifications
        </p>
      </div>
      
      <button 
        type="button"
        on:click={handleCreateNew}
        class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700"
      >
        Add Email Provider
      </button>
    </div>
  </div>

  <!-- Success/Error Messages -->
  {#if form?.success}
    <div class="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
      <p class="text-sm font-medium text-green-800">{form.message}</p>
    </div>
  {/if}

  {#if form?.message && !form?.success}
    <div class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
      <p class="text-sm font-medium text-red-800">{form.message}</p>
    </div>
  {/if}

  <!-- Current Configuration Status -->
  <div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Email Service Status</h3>
    
    {#if primaryConfig}
      <div class="flex items-center space-x-3 mb-4">
        <div class="flex-shrink-0 w-3 h-3 bg-green-400 rounded-full"></div>
        <div>
          <p class="text-sm font-medium text-gray-900">
            Primary Provider: {primaryConfig.provider_type.toUpperCase()}
          </p>
          <p class="text-xs text-gray-500">
            From: {primaryConfig.from_email} | 
            Last used: {primaryConfig.last_used_at ? new Date(primaryConfig.last_used_at).toLocaleDateString() : 'Never'}
          </p>
        </div>
      </div>
    {:else}
      <div class="flex items-center space-x-3 mb-4">
        <div class="flex-shrink-0 w-3 h-3 bg-yellow-400 rounded-full"></div>
        <div>
          <p class="text-sm font-medium text-gray-900">Using Supabase Fallback</p>
          <p class="text-xs text-gray-500">Configure a provider for production use</p>
        </div>
      </div>
    {/if}

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
      <div class="text-center">
        <p class="text-2xl font-bold text-gray-900">{activeConfigs.length}</p>
        <p class="text-xs text-gray-500">Active Providers</p>
      </div>
      <div class="text-center">
        <p class="text-2xl font-bold text-gray-900">
          {emailConfigs.reduce((sum, config) => sum + config.daily_email_count, 0)}
        </p>
        <p class="text-xs text-gray-500">Emails Today</p>
      </div>
      <div class="text-center">
        <p class="text-2xl font-bold text-gray-900">{supportedProviders.length}</p>
        <p class="text-xs text-gray-500">Supported Providers</p>
      </div>
    </div>
  </div>

  <!-- Email Configurations -->
  <div class="space-y-6">
    {#if emailConfigs.length === 0}
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No email providers configured</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by adding your first email provider.</p>
        <div class="mt-6">
          <button 
            type="button"
            on:click={handleCreateNew}
            class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700"
          >
            Add Email Provider
          </button>
        </div>
      </div>
    {:else}
      {#each emailConfigs as config}
        <EmailConfigCard 
          {config} 
          on:edit={() => handleEdit(config)}
          on:setPrimary
          on:test
        />
      {/each}
    {/if}
  </div>
</div>

<!-- Create/Edit Form Modal -->
{#if showCreateForm}
  <EmailConfigForm 
    config={editingConfig}
    {supportedProviders}
    on:close={handleFormClose}
  />
{/if}
