// Enhanced enum creation for application-wide enums (no company validation)
import { fail, redirect } from '@sveltejs/kit'
import { createSupabaseAdminClient } from '$lib/server/supabase-admin'
import type { Actions, PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ locals }) => {
  const { consoleUser, authManager } = locals

  if (!consoleUser) {
    throw redirect(302, '/login')
  }

  // Check permissions for enum creation
  const hasPermission = await authManager.validatePermission(
    consoleUser.id,
    'enums',
    'create'
  )

  if (!hasPermission) {
    throw redirect(302, '/dashboard?error=insufficient_permissions')
  }

  return {
    consoleUser
  }
}

export const actions: Actions = {
  createEnum: async ({ request, locals, getClientAddress }) => {
    const { consoleUser, authManager } = locals

    if (!consoleUser) {
      return fail(401, { error: 'Unauthorized' })
    }

    // Check permissions
    const hasPermission = await authManager.validatePermission(
      consoleUser.id,
      'enums',
      'create'
    )

    if (!hasPermission) {
      return fail(403, { error: 'Insufficient permissions' })
    }

    try {
      const formData = await request.formData()
      const category = formData.get('category') as string
      const displayName = formData.get('display_name') as string
      const description = formData.get('description') as string
      const isSystem = formData.get('is_system') === 'true'
      const isEditable = formData.get('is_editable') === 'true'
      const isHierarchical = formData.get('is_hierarchical') === 'true'
      const valuesJson = formData.get('values') as string

      // Validate required fields
      if (!category || !displayName || !valuesJson) {
        return fail(400, { 
          error: 'Category, display name, and values are required',
          values: { category, displayName, description }
        })
      }

      // Validate category format (lowercase with underscores)
      if (!/^[a-z0-9_]+$/.test(category)) {
        return fail(400, { 
          error: 'Category must contain only lowercase letters, numbers, and underscores',
          values: { category, displayName, description }
        })
      }

      // Parse and validate values JSON
      let values
      try {
        values = JSON.parse(valuesJson)
        if (!Array.isArray(values)) {
          throw new Error('Values must be an array')
        }
      } catch (error) {
        return fail(400, { 
          error: 'Invalid values format. Must be valid JSON array.',
          values: { category, displayName, description }
        })
      }

      // Validate enum values structure
      for (const value of values) {
        if (!value.key || !value.label) {
          return fail(400, { 
            error: 'Each enum value must have a key and label',
            values: { category, displayName, description }
          })
        }
        if (!/^[a-z0-9_]+$/.test(value.key)) {
          return fail(400, { 
            error: 'Enum value keys must contain only lowercase letters, numbers, and underscores',
            values: { category, displayName, description }
          })
        }
      }

      const supabase = createSupabaseAdminClient()

      // Check if category already exists
      const { data: existingEnum } = await supabase
        .from('application_enums')
        .select('id')
        .eq('category', category)
        .single()

      if (existingEnum) {
        return fail(400, { 
          error: 'An enum with this category already exists',
          values: { category, displayName, description }
        })
      }

      // Create the enum
      const { data: newEnum, error: createError } = await supabase
        .from('application_enums')
        .insert({
          category,
          display_name: displayName,
          description: description || null,
          values,
          is_system: isSystem,
          is_editable: isEditable,
          is_hierarchical: isHierarchical,
          created_by: consoleUser.id,
          updated_by: consoleUser.id
        })
        .select()
        .single()

      if (createError) {
        console.error('Error creating enum:', createError)
        return fail(500, { 
          error: 'Failed to create enum',
          values: { category, displayName, description }
        })
      }

      // Log the operation
      await supabase
        .from('application_enum_operations')
        .insert({
          enum_id: newEnum.id,
          operation_type: 'created',
          user_id: consoleUser.id,
          changes: {
            category,
            display_name: displayName,
            values_count: values.length
          }
        })

      // Log security event
      await authManager.logSecurityEvent({
        event_type: 'enum_created',
        user_id: consoleUser.id,
        user_email: consoleUser.email,
        user_role: consoleUser.role,
        resource: 'application_enums',
        resource_id: newEnum.id,
        success: true,
        ip_address: getClientAddress(),
        metadata: {
          enum_category: category,
          enum_display_name: displayName,
          values_count: values.length
        },
        timestamp: new Date().toISOString()
      })

      throw redirect(302, `/enums/${newEnum.id}?created=true`)

    } catch (error) {
      if (error.status === 302) {
        throw error // Re-throw redirect
      }
      console.error('Create enum error:', error)
      return fail(500, { error: 'Internal server error' })
    }
  }
}
