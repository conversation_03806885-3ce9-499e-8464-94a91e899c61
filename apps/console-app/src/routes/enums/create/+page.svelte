<script lang="ts">
  import { goto } from '$app/navigation'
  import { Plus, Trash2, Shield, Settings, Palette } from 'lucide-svelte'
  import type { PageData } from './$types'
  
  let { data }: { data: PageData } = $props()
  
  let category = $state('')
  let displayName = $state('')
  let description = $state('')
  let isSystem = $state(false)
  let isEditable = $state(true)
  let isHierarchical = $state(false)
  let values = $state([{ key: '', label: '', color: '#6b7280', active: true, sort_order: 1 }])
  let isSubmitting = $state(false)
  let showPreview = $state(true)
  
  const colorOptions = [
    '#ef4444', '#f59e0b', '#10b981', '#3b82f6', '#8b5cf6', '#ec4899',
    '#06b6d4', '#84cc16', '#f97316', '#6366f1', '#14b8a6', '#a855f7'
  ]
  
  function formatCategoryName(value: string) {
    return value
      .toLowerCase()
      .replace(/[^a-z0-9_]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
  }
  
  function formatValueKey(value: string) {
    return value
      .toUpperCase()
      .replace(/[^A-Z0-9_]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
  }
  
  function addValue() {
    const maxSortOrder = Math.max(...values.map(v => v.sort_order || 0))
    values = [...values, { 
      key: '', 
      label: '', 
      color: colorOptions[values.length % colorOptions.length],
      active: true,
      sort_order: maxSortOrder + 1
    }]
  }
  
  function removeValue(index: number) {
    values = values.filter((_, i) => i !== index)
  }
  
  async function handleSubmit(event: Event) {
    event.preventDefault()
    isSubmitting = true
    
    try {
      const response = await fetch('/api/enums', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          category,
          display_name: displayName,
          description,
          is_system: isSystem,
          is_editable: isEditable,
          is_hierarchical: isHierarchical,
          values: values.filter(v => v.key && v.label)
        })
      })
      
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to create enum')
      }
      
      goto('/enums')
    } catch (error) {
      console.error('Error creating enum:', error)
      alert(error instanceof Error ? error.message : 'Failed to create enum')
    } finally {
      isSubmitting = false
    }
  }
</script>

<div class="min-h-screen bg-background">
  <div class="container mx-auto py-8 px-4 max-w-7xl">
    <div class="mb-8">
      <h1 class="text-3xl font-bold">Create New Enum</h1>
      <p class="text-muted-foreground mt-2">Define a new enumeration for your system</p>
    </div>

    <form onsubmit={handleSubmit} class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <div class="bg-card border rounded-lg p-6">
          <h2 class="text-lg font-semibold mb-4">Basic Information</h2>
          
          <div class="space-y-4">
            <div>
              <label for="category" class="block text-sm font-medium mb-1">
                Category Name <span class="text-destructive">*</span>
              </label>
              <input
                type="text"
                id="category"
                name="category"
                bind:value={category}
                oninput={(e: Event) => category = formatCategoryName((e.target as HTMLInputElement).value)}
                placeholder="e.g., job_status"
                class="w-full px-3 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                required
              />
              <p class="text-xs text-muted-foreground mt-1">
                Lowercase with underscores, no spaces
              </p>
            </div>

            <div>
              <label for="display_name" class="block text-sm font-medium mb-1">
                Display Name <span class="text-destructive">*</span>
              </label>
              <input
                type="text"
                id="display_name"
                name="display_name"
                bind:value={displayName}
                placeholder="e.g., Job Status"
                class="w-full px-3 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                required
              />
            </div>

            <div>
              <label for="description" class="block text-sm font-medium mb-1">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                bind:value={description}
                placeholder="Brief description of this enum's purpose"
                rows="3"
                class="w-full px-3 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              ></textarea>
            </div>
          </div>
        </div>

        <!-- Settings -->
        <div class="bg-card border rounded-lg p-6">
          <h2 class="text-lg font-semibold mb-4">Settings</h2>
          
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <Shield class="w-5 h-5 text-blue-500" />
                <div>
                  <label for="is_system" class="text-sm font-medium">System Protected</label>
                  <p class="text-xs text-muted-foreground">Prevents deletion and restricts modifications</p>
                </div>
              </div>
              <input
                type="checkbox"
                id="is_system"
                name="is_system"
                bind:checked={isSystem}
                class="w-4 h-4 text-primary bg-background border-gray-300 rounded focus:ring-primary"
              />
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <Settings class="w-5 h-5 text-green-500" />
                <div>
                  <label for="is_editable" class="text-sm font-medium">User Editable</label>
                  <p class="text-xs text-muted-foreground">Allows console users to modify values</p>
                </div>
              </div>
              <input
                type="checkbox"
                id="is_editable"
                name="is_editable"
                bind:checked={isEditable}
                disabled={isSystem}
                class="w-4 h-4 text-primary bg-background border-gray-300 rounded focus:ring-primary"
              />
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <Settings class="w-5 h-5 text-purple-500" />
                <div>
                  <label for="is_hierarchical" class="text-sm font-medium">Hierarchical Structure</label>
                  <p class="text-xs text-muted-foreground">Supports subcategories (like EAD types)</p>
                </div>
              </div>
              <input
                type="checkbox"
                id="is_hierarchical"
                name="is_hierarchical"
                bind:checked={isHierarchical}
                class="w-4 h-4 text-primary bg-background border-gray-300 rounded focus:ring-primary"
              />
            </div>
          </div>
        </div>

        <!-- Enum Values -->
        <div class="bg-card border rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold">Enum Values</h2>
            <button
              type="button"
              onclick={addValue}
              class="flex items-center px-3 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 rounded-md transition-colors"
            >
              <Plus class="w-4 h-4 mr-2" />
              Add Value
            </button>
          </div>

          <div class="space-y-3">
            {#each values as value, index}
              <div class="grid grid-cols-12 gap-3 items-center p-3 bg-muted/50 rounded-lg">
                <!-- Sort Order -->
                <div class="col-span-1">
                  <input
                    type="number"
                    bind:value={value.sort_order}
                    min="1"
                    class="w-full px-2 py-1 text-xs bg-background border border-input rounded"
                    title="Sort Order"
                  />
                </div>

                <!-- Key -->
                <div class="col-span-3">
                  <input
                    type="text"
                    bind:value={value.key}
                    oninput={(e: Event) => value.key = formatValueKey((e.target as HTMLInputElement).value)}
                    placeholder="key"
                    class="w-full px-2 py-1 text-sm bg-background border border-input rounded focus:outline-none focus:ring-1 focus:ring-ring"
                    required
                  />
                </div>

                <!-- Label -->
                <div class="col-span-3">
                  <input
                    type="text"
                    bind:value={value.label}
                    placeholder="Display Label"
                    class="w-full px-2 py-1 text-sm bg-background border border-input rounded focus:outline-none focus:ring-1 focus:ring-ring"
                    required
                  />
                </div>

                <!-- Color -->
                <div class="col-span-2">
                  <div class="flex items-center space-x-2">
                    <input
                      type="color"
                      bind:value={value.color}
                      class="w-8 h-8 border border-input rounded cursor-pointer"
                    />
                    <div class="flex flex-wrap gap-1">
                      {#each colorOptions.slice(0, 4) as color}
                        <button
                          type="button"
                          onclick={() => value.color = color}
                          class="w-4 h-4 rounded border border-gray-300 hover:scale-110 transition-transform"
                          style="background-color: {color}"
                          aria-label="Set color to {color}"
                        ></button>
                      {/each}
                    </div>
                  </div>
                </div>

                <!-- Active -->
                <div class="col-span-1 flex justify-center">
                  <input
                    type="checkbox"
                    bind:checked={value.active}
                    class="w-4 h-4 text-primary bg-background border-gray-300 rounded focus:ring-primary"
                    title="Active"
                  />
                </div>

                <!-- Actions -->
                <div class="col-span-2 flex justify-end">
                  <button
                    type="button"
                    onclick={() => removeValue(index)}
                    disabled={values.length <= 1}
                    class="p-1 text-destructive hover:bg-destructive/10 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    aria-label="Remove Value"
                  >
                    <Trash2 class="w-4 h-4" />
                  </button>
                </div>
              </div>
            {/each}
          </div>

          {#if values.length === 0}
            <div class="text-center py-8 text-muted-foreground">
              <Palette class="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No enum values added yet</p>
              <button
                type="button"
                onclick={addValue}
                class="mt-2 text-primary hover:underline"
              >
                Add your first value
              </button>
            </div>
          {/if}
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6 border-t">
          <a
            href="/enums"
            class="flex items-center px-4 py-2 text-sm font-medium border border-input bg-background hover:bg-accent rounded-md transition-colors"
          >
            Cancel
          </a>
          <button
            type="submit"
            disabled={isSubmitting}
            class="flex items-center px-6 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 rounded-md transition-colors disabled:opacity-50"
          >
            {#if isSubmitting}
              <div class="w-4 h-4 mr-2 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin"></div>
              Creating...
            {:else}
              <Plus class="w-4 h-4 mr-2" />
              Create Enum
            {/if}
          </button>
        </div>
      </div>

      <!-- Preview Section -->
      {#if showPreview}
        <div class="space-y-6">
          <div class="bg-card border rounded-lg p-6 sticky top-4">
            <h2 class="text-lg font-semibold mb-4">Preview</h2>
            
            <!-- Enum Card Preview -->
            <div class="bg-muted/50 border rounded-lg p-4">
              <div class="flex items-start justify-between mb-3">
                <div class="flex items-center space-x-3">
                  <div class="p-2 bg-primary/10 rounded-lg">
                    <Palette class="w-4 h-4 text-primary" />
                  </div>
                  <div>
                    <h3 class="font-semibold flex items-center gap-2">
                      {displayName || 'Enum Display Name'}
                      {#if isSystem}
                        <Shield class="w-3 h-3 text-blue-500" aria-label="System Protected" />
                      {/if}
                      {#if isHierarchical}
                        <Settings class="w-3 h-3 text-green-500" aria-label="Hierarchical Structure" />
                      {/if}
                    </h3>
                    <p class="text-xs text-muted-foreground">{category || 'enum_category'}</p>
                  </div>
                </div>
              </div>

              {#if description}
                <p class="text-xs text-muted-foreground mb-3">{description}</p>
              {/if}

              <!-- Values Preview -->
              <div class="space-y-2 mb-3">
                {#each values.slice(0, 3) as value}
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                      <div 
                        class="w-2 h-2 rounded-full" 
                        style="background-color: {value.color || '#6b7280'}"
                      ></div>
                      <span class="text-xs {value.active !== false ? '' : 'opacity-50'}">
                        {value.label || 'Label'}
                      </span>
                    </div>
                    {#if value.active === false}
                      <span class="text-xs text-muted-foreground">Inactive</span>
                    {/if}
                  </div>
                {/each}
                {#if values.length > 3}
                  <p class="text-xs text-muted-foreground">
                    +{values.length - 3} more values
                  </p>
                {/if}
              </div>

              <!-- Metadata Preview -->
              <div class="space-y-1 text-xs text-muted-foreground border-t pt-3">
                <div class="flex justify-between">
                  <span>Total Values:</span>
                  <span class="font-medium">{values.length}</span>
                </div>
                <div class="flex justify-between">
                  <span>Active:</span>
                  <span class="font-medium">{values.filter(v => v.active !== false).length}</span>
                </div>
                <div class="flex justify-between">
                  <span>System:</span>
                  <span class="font-medium">{isSystem ? 'Yes' : 'No'}</span>
                </div>
                <div class="flex justify-between">
                  <span>Editable:</span>
                  <span class="font-medium">{isEditable ? 'Yes' : 'No'}</span>
                </div>
              </div>
            </div>

            <!-- JSON Preview -->
            <div class="mt-4">
              <h3 class="text-sm font-medium mb-2">JSON Structure</h3>
              <pre class="text-xs bg-muted p-3 rounded border overflow-x-auto">{JSON.stringify(values, null, 2)}</pre>
            </div>
          </div>
        </div>
      {/if}
    </form>
  </div>
</div>
