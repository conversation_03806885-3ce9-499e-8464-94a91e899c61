<script lang="ts">
  import type { PageData } from './$types'
  import { 
    FileText, 
    Plus, 
    Search, 
    Filter,
    Download,
    Upload,
    Edit,
    Trash2,
    Eye,
    MoreHorizontal,
    Shield,
    Settings,
    Database
  } from 'lucide-svelte'
  import { addToast } from '$stores/toast'

  export let data: PageData

  const { consoleUser, applicationEnums, stats, needsMigration, error } = data

  let searchQuery = ''
  let selectedCategory = 'all'
  let showFilters = false
  let selectedType = 'all'

  // Enum categories for filtering
  const enumCategories = [
    { value: 'all', label: 'All Categories' },
    { value: 'work_authorization_types', label: 'Work Authorization Types' },
    { value: 'job_statuses', label: 'Job Statuses' },
    { value: 'interview_statuses', label: 'Interview Statuses' },
    { value: 'interview_types', label: 'Interview Types' },
    { value: 'offer_statuses', label: 'Offer Statuses' },
    { value: 'submission_statuses', label: 'Submission Statuses' },
    { value: 'candidate_statuses', label: 'Candidate Statuses' },
    { value: 'application_statuses', label: 'Application Statuses' },
    { value: 'employment_types', label: 'Employment Types' },
    { value: 'skill_levels', label: 'Skill Levels' },
    { value: 'priority_levels', label: 'Priority Levels' }
  ]

  const enumTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'system', label: 'System Enums' },
    { value: 'editable', label: 'Editable Enums' },
    { value: 'hierarchical', label: 'Hierarchical Enums' }
  ]

  // Filter enums based on search, category, and type
  $: filteredEnums = (applicationEnums || []).filter(enumItem => {
    const matchesSearch = searchQuery === '' || 
      enumItem.display_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      enumItem.category.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesCategory = selectedCategory === 'all' || enumItem.category === selectedCategory
    
    const matchesType = selectedType === 'all' || 
      (selectedType === 'system' && enumItem.is_system) ||
      (selectedType === 'editable' && enumItem.is_editable) ||
      (selectedType === 'hierarchical' && enumItem.is_hierarchical)
    
    return matchesSearch && matchesCategory && matchesType
  })

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  function getEnumValueCount(values: any[]): number {
    if (!Array.isArray(values)) return 0
    let count = values.length
    
    // Count subcategories for hierarchical enums
    values.forEach(value => {
      if (value.subcategories && Array.isArray(value.subcategories)) {
        count += value.subcategories.length
      }
    })
    
    return count
  }

  function getActiveValueCount(values: any[]): number {
    if (!Array.isArray(values)) return 0
    return values.filter(value => value.active !== false).length
  }

  function handleExport() {
    addToast({
      type: 'info',
      title: 'Export Started',
      message: 'Your enum data export is being prepared...'
    })
  }

  function handleImport() {
    addToast({
      type: 'info',
      title: 'Import Feature',
      message: 'Bulk import functionality coming soon!'
    })
  }

  function handleDeleteEnum(enumId: string, enumName: string, isSystem: boolean) {
    if (isSystem) {
      addToast({
        type: 'error',
        title: 'Cannot Delete',
        message: 'System enums cannot be deleted as they are required for core functionality.'
      })
      return
    }

    addToast({
      type: 'warning',
      title: 'Delete Enum',
      message: `Are you sure you want to delete "${enumName}"? This action cannot be undone.`,
      actions: [
        {
          label: 'Delete',
          action: async () => {
            try {
              const formData = new FormData()
              formData.append('enum_id', enumId)
              
              const response = await fetch('?/deleteEnum', {
                method: 'POST',
                body: formData
              })
              
              if (response.ok) {
                addToast({
                  type: 'success',
                  title: 'Enum Deleted',
                  message: 'The enum has been successfully deleted.'
                })
                window.location.reload()
              } else {
                addToast({
                  type: 'error',
                  title: 'Delete Failed',
                  message: 'Failed to delete the enum. Please try again.'
                })
              }
            } catch (error) {
              addToast({
                type: 'error',
                title: 'Delete Failed',
                message: 'An error occurred while deleting the enum.'
              })
            }
          },
          style: 'primary'
        },
        {
          label: 'Cancel',
          action: () => {},
          style: 'secondary'
        }
      ]
    })
  }
</script>

<svelte:head>
  <title>Application Enum Management - Console</title>
</svelte:head>

<div class="space-y-6">
  <!-- Migration Notice -->
  {#if needsMigration || error}
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div class="flex items-start space-x-3">
        <Shield class="w-5 h-5 text-yellow-500 mt-0.5" />
        <div class="text-sm">
          <p class="font-medium text-yellow-900">Database Migration Required</p>
          <p class="text-yellow-700 mt-1">
            {#if needsMigration}
              The application_enums table needs to be created. Please run the cloud migration scripts in your Supabase dashboard.
            {:else if error}
              {error}
            {/if}
          </p>
          <div class="mt-3">
            <p class="font-medium text-yellow-900">To fix this:</p>
            <ol class="list-decimal list-inside text-yellow-700 mt-1 space-y-1">
              <li>Open Supabase Dashboard → SQL Editor</li>
              <li>Run <code class="bg-yellow-100 px-1 rounded">/supabase/cloud-enum-migration.sql</code></li>
              <li>Run <code class="bg-yellow-100 px-1 rounded">/supabase/cloud-enum-seed.sql</code></li>
              <li>Refresh this page</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  {/if}

  <!-- Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold">Application Enum Management</h1>
      <p class="text-muted-foreground mt-1">
        Configure application-wide dropdown options and categorized values
      </p>
    </div>
    <div class="flex items-center space-x-3">
      <button
        on:click={handleImport}
        class="flex items-center px-4 py-2 text-sm font-medium border border-input bg-background hover:bg-accent rounded-md transition-colors"
      >
        <Upload class="w-4 h-4 mr-2" />
        Import
      </button>
      <button
        on:click={handleExport}
        class="flex items-center px-4 py-2 text-sm font-medium border border-input bg-background hover:bg-accent rounded-md transition-colors"
      >
        <Download class="w-4 h-4 mr-2" />
        Export
      </button>
      <a
        href="/enums/create"
        class="flex items-center px-4 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 rounded-md transition-colors"
      >
        <Plus class="w-4 h-4 mr-2" />
        Create Enum
      </a>
    </div>
  </div>

  <!-- Statistics Cards -->
  {#if stats}
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="bg-card border rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-muted-foreground">Total Enums</p>
            <p class="text-2xl font-bold">{stats.totalApplicationEnums || 0}</p>
          </div>
          <Database class="w-8 h-8 text-primary" />
        </div>
      </div>
      <div class="bg-card border rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-muted-foreground">System Enums</p>
            <p class="text-2xl font-bold">{stats.systemEnums || 0}</p>
          </div>
          <Shield class="w-8 h-8 text-blue-500" />
        </div>
      </div>
      <div class="bg-card border rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-muted-foreground">Editable Enums</p>
            <p class="text-2xl font-bold">{stats.editableEnums || 0}</p>
          </div>
          <Settings class="w-8 h-8 text-green-500" />
        </div>
      </div>
    </div>
  {/if}

  <!-- Filters -->
  <div class="bg-card border rounded-lg p-4">
    <div class="flex items-center space-x-4">
      <!-- Search -->
      <div class="relative flex-1 max-w-sm">
        <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
        <input
          type="text"
          placeholder="Search enums..."
          bind:value={searchQuery}
          class="pl-10 pr-4 py-2 w-full text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
        />
      </div>

      <!-- Category Filter -->
      <select
        bind:value={selectedCategory}
        class="px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
      >
        {#each enumCategories as category}
          <option value={category.value}>{category.label}</option>
        {/each}
      </select>

      <!-- Type Filter -->
      <select
        bind:value={selectedType}
        class="px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
      >
        {#each enumTypes as type}
          <option value={type.value}>{type.label}</option>
        {/each}
      </select>

      <!-- Advanced Filters Toggle -->
      <button
        on:click={() => showFilters = !showFilters}
        class="flex items-center px-3 py-2 text-sm font-medium border border-input bg-background hover:bg-accent rounded-md transition-colors"
      >
        <Filter class="w-4 h-4 mr-2" />
        Filters
      </button>
    </div>

    {#if showFilters}
      <div class="mt-4 pt-4 border-t">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium mb-2">Updated After</label>
            <input
              type="date"
              class="w-full px-3 py-2 text-sm bg-background border border-input rounded-md"
            />
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">Hierarchical</label>
            <select class="w-full px-3 py-2 text-sm bg-background border border-input rounded-md">
              <option value="">All</option>
              <option value="true">Hierarchical Only</option>
              <option value="false">Flat Only</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">System Status</label>
            <select class="w-full px-3 py-2 text-sm bg-background border border-input rounded-md">
              <option value="">All</option>
              <option value="system">System Protected</option>
              <option value="editable">User Editable</option>
            </select>
          </div>
        </div>
      </div>
    {/if}
  </div>

  <!-- Enums Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {#each filteredEnums as enumItem}
      <div class="bg-card border rounded-lg p-6 hover:shadow-md transition-shadow">
        <!-- Header -->
        <div class="flex items-start justify-between mb-4">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-primary/10 rounded-lg">
              <FileText class="w-5 h-5 text-primary" />
            </div>
            <div>
              <h3 class="font-semibold flex items-center gap-2">
                {enumItem.display_name}
                {#if enumItem.is_system}
                  <Shield class="w-4 h-4 text-blue-500" title="System Protected" />
                {/if}
                {#if enumItem.is_hierarchical}
                  <Settings class="w-4 h-4 text-green-500" title="Hierarchical Structure" />
                {/if}
              </h3>
              <p class="text-sm text-muted-foreground">{enumItem.category}</p>
            </div>
          </div>
          
          <!-- Actions Menu -->
          <div class="relative">
            <button class="p-1 hover:bg-accent rounded-md transition-colors">
              <MoreHorizontal class="w-4 h-4" />
            </button>
          </div>
        </div>

        <!-- Description -->
        {#if enumItem.description}
          <p class="text-sm text-muted-foreground mb-3">{enumItem.description}</p>
        {/if}

        <!-- Enum Values Preview -->
        <div class="space-y-2 mb-4">
          {#each (enumItem.values || []).slice(0, 3) as value}
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <div 
                  class="w-3 h-3 rounded-full" 
                  style="background-color: {value.color || '#6b7280'}"
                ></div>
                <span class="text-sm {value.active !== false ? '' : 'opacity-50'}">{value.label}</span>
                {#if value.subcategories && value.subcategories.length > 0}
                  <span class="text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded">
                    +{value.subcategories.length}
                  </span>
                {/if}
              </div>
              {#if value.active === false}
                <span class="text-xs text-muted-foreground">Inactive</span>
              {/if}
            </div>
          {/each}
          {#if (enumItem.values || []).length > 3}
            <p class="text-xs text-muted-foreground">
              +{(enumItem.values || []).length - 3} more values
            </p>
          {/if}
        </div>

        <!-- Metadata -->
        <div class="space-y-2 text-xs text-muted-foreground border-t pt-4">
          <div class="flex justify-between">
            <span>Total Values:</span>
            <span class="font-medium">{getEnumValueCount(enumItem.values || [])}</span>
          </div>
          <div class="flex justify-between">
            <span>Active:</span>
            <span class="font-medium">{getActiveValueCount(enumItem.values || [])}</span>
          </div>
          <div class="flex justify-between">
            <span>Updated:</span>
            <span>{formatDate(enumItem.updated_at)}</span>
          </div>
          <div class="flex justify-between">
            <span>Version:</span>
            <span class="font-medium">v{enumItem.version || 1}</span>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between mt-4 pt-4 border-t">
          <div class="flex items-center space-x-2">
            <a
              href="/enums/{enumItem.id}"
              class="flex items-center px-2 py-1 text-xs font-medium border border-input bg-background hover:bg-accent rounded transition-colors"
            >
              <Eye class="w-3 h-3 mr-1" />
              View
            </a>
            {#if enumItem.is_editable}
              <a
                href="/enums/{enumItem.id}/edit"
                class="flex items-center px-2 py-1 text-xs font-medium border border-input bg-background hover:bg-accent rounded transition-colors"
              >
                <Edit class="w-3 h-3 mr-1" />
                Edit
              </a>
            {/if}
          </div>
          {#if !enumItem.is_system}
            <button
              on:click={() => handleDeleteEnum(enumItem.id, enumItem.display_name, enumItem.is_system)}
              class="flex items-center px-2 py-1 text-xs font-medium text-destructive hover:bg-destructive/10 rounded transition-colors"
            >
              <Trash2 class="w-3 h-3 mr-1" />
              Delete
            </button>
          {/if}
        </div>
      </div>
    {/each}
  </div>

  <!-- Empty State -->
  {#if filteredEnums.length === 0}
    <div class="text-center py-12">
      <FileText class="w-12 h-12 text-muted-foreground mx-auto mb-4" />
      <h3 class="text-lg font-semibold mb-2">No enums found</h3>
      <p class="text-muted-foreground mb-4">
        {searchQuery || selectedCategory !== 'all' || selectedType !== 'all'
          ? 'Try adjusting your search or filters' 
          : 'Get started by creating your first enum'}
      </p>
      {#if searchQuery === '' && selectedCategory === 'all' && selectedType === 'all'}
        <a
          href="/enums/create"
          class="inline-flex items-center px-4 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 rounded-md transition-colors"
        >
          <Plus class="w-4 h-4 mr-2" />
          Create Your First Enum
        </a>
      {/if}
    </div>
  {/if}
</div>
