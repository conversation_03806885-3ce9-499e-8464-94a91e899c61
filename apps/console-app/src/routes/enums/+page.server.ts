// Enhanced console enum management with application-wide support
import { fail, redirect } from '@sveltejs/kit'
import { createSupabaseAdminClient } from '$lib/server/supabase-admin'
import type { Actions, PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ locals }) => {
  const { consoleUser, authManager } = locals

  if (!consoleUser) {
    throw redirect(302, '/login')
  }

  // Check permissions for enum management
  const hasPermission = await authManager.validatePermission(
    consoleUser.id,
    'enums',
    'read'
  )

  if (!hasPermission) {
    throw redirect(302, '/dashboard?error=insufficient_permissions')
  }

  try {
    const supabase = createSupabaseAdminClient()

    // Get application-wide enums (handle case where table doesn't exist yet)
    const { data: applicationEnums, error: appEnumError } = await supabase
      .from('application_enums')
      .select('*')
      .order('category')

    if (appEnumError) {
      console.error('Error fetching application enums:', appEnumError)
      
      // If table doesn't exist, return empty state
      if (appEnumError.message?.includes('does not exist') || appEnumError.code === 'PGRST116') {
        return {
          consoleUser,
          applicationEnums: [],
          stats: {
            totalApplicationEnums: 0,
            systemEnums: 0,
            editableEnums: 0
          },
          needsMigration: true
        }
      }
    }

    return {
      consoleUser,
      applicationEnums: applicationEnums || [],
      // Statistics
      stats: {
        totalApplicationEnums: applicationEnums?.length || 0,
        systemEnums: applicationEnums?.filter(e => e.is_system).length || 0,
        editableEnums: applicationEnums?.filter(e => e.is_editable).length || 0
      }
    }

  } catch (error) {
    console.error('Enum management load error:', error)
    
    // Return empty state instead of throwing error
    return {
      consoleUser,
      applicationEnums: [],
      stats: {
        totalApplicationEnums: 0,
        systemEnums: 0,
        editableEnums: 0
      },
      error: 'Failed to load enums. Please check if migrations have been run.'
    }
  }
}

export const actions: Actions = {
  deleteEnum: async ({ request, locals, getClientAddress }) => {
    const { consoleUser, authManager } = locals

    if (!consoleUser) {
      return fail(401, { error: 'Unauthorized' })
    }

    // Check permissions
    const hasPermission = await authManager.validatePermission(
      consoleUser.id,
      'enums',
      'delete'
    )

    if (!hasPermission) {
      return fail(403, { error: 'Insufficient permissions' })
    }

    try {
      const formData = await request.formData()
      const enumId = formData.get('enum_id') as string

      if (!enumId) {
        return fail(400, { error: 'Enum ID is required' })
      }

      const supabase = createSupabaseAdminClient()

      // Get enum details for validation and logging
      const { data: enumData, error: fetchError } = await supabase
        .from('application_enums')
        .select('*')
        .eq('id', enumId)
        .single()

      if (fetchError || !enumData) {
        return fail(404, { error: 'Enum not found' })
      }

      // Prevent deletion of system enums
      if (enumData.is_system) {
        return fail(400, { error: 'System enums cannot be deleted' })
      }

      // Delete the enum
      const { error: deleteError } = await supabase
        .from('application_enums')
        .delete()
        .eq('id', enumId)

      if (deleteError) {
        console.error('Error deleting enum:', deleteError)
        return fail(500, { error: 'Failed to delete enum' })
      }

      // Log the operation
      await authManager.logSecurityEvent({
        event_type: 'enum_deleted',
        user_id: consoleUser.id,
        user_email: consoleUser.email,
        user_role: consoleUser.role,
        resource: 'application_enums',
        resource_id: enumId,
        success: true,
        ip_address: getClientAddress(),
        metadata: {
          enum_category: enumData.category,
          enum_display_name: enumData.display_name
        },
        timestamp: new Date().toISOString()
      })

      return { success: true, message: 'Enum deleted successfully' }

    } catch (error) {
      console.error('Delete enum error:', error)
      return fail(500, { error: 'Internal server error' })
    }
  }
}
