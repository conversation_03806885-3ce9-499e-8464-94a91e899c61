// Enhanced enum detail view for application-wide enums
import { error, redirect } from '@sveltejs/kit'
import { createSupabaseAdminClient } from '$lib/server/supabase-admin'
import type { PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ locals, params }) => {
  const { consoleUser, authManager } = locals
  const { id } = params

  if (!consoleUser) {
    throw redirect(302, '/login')
  }

  // Check permissions
  const hasPermission = await authManager.validatePermission(
    consoleUser.id,
    'enums',
    'read'
  )

  if (!hasPermission) {
    throw redirect(302, '/dashboard?error=insufficient_permissions')
  }

  try {
    const supabase = createSupabaseAdminClient()

    // Get enum details from application_enums table
    const { data: enumData, error: enumError } = await supabase
      .from('application_enums')
      .select('*')
      .eq('id', id)
      .single()

    if (enumError || !enumData) {
      console.error('Error fetching enum:', enumError)
      throw error(404, 'Enum not found')
    }

    // Get enum operation history
    const { data: operations } = await supabase
      .from('application_enum_operations')
      .select(`
        *,
        console_users!application_enum_operations_user_id_fkey (
          email,
          name
        )
      `)
      .eq('enum_id', id)
      .order('timestamp', { ascending: false })
      .limit(10)

    return {
      consoleUser,
      enumData,
      operations: operations || []
    }

  } catch (err) {
    console.error('Enum detail load error:', err)
    if (err.status) {
      throw err // Re-throw SvelteKit errors
    }
    throw error(500, 'Failed to load enum details')
  }
}
