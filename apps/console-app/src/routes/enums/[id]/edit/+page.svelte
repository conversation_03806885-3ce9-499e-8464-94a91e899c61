<!-- Enhanced enum edit interface with hierarchical support -->
<script lang="ts">
  import type { PageData } from './$types'
  import { 
    ArrowLeft,
    Save,
    Plus,
    Trash2,
    Shield,
    Settings,
    ChevronDown,
    ChevronRight
  } from 'lucide-svelte'
  import { addToast } from '$stores/toast'
  import { enhance } from '$app/forms'

  export let data: PageData
  export let form

  const { consoleUser, enumData } = data

  // Form state
  let displayName = enumData.display_name || ''
  let description = enumData.description || ''
  let values = JSON.parse(JSON.stringify(enumData.values || []))

  // UI state
  let isSubmitting = false
  let expandedValues = new Set<number>()

  // Predefined color options
  const colorOptions = [
    '#22c55e', '#3b82f6', '#f59e0b', '#ef4444', 
    '#8b5cf6', '#06b6d4', '#10b981', '#f97316',
    '#84cc16', '#a855f7', '#6b7280', '#374151',
    '#dc2626', '#991b1b'
  ]

  function toggleExpanded(index: number) {
    if (expandedValues.has(index)) {
      expandedValues.delete(index)
    } else {
      expandedValues.add(index)
    }
    expandedValues = new Set(expandedValues)
  }

  function addValue() {
    values = [...values, {
      key: '',
      label: '',
      color: colorOptions[values.length % colorOptions.length],
      active: true,
      sort_order: values.length + 1,
      subcategories: enumData.is_hierarchical ? [] : undefined
    }]
  }

  function removeValue(index: number) {
    if (values.length > 1) {
      values = values.filter((_, i) => i !== index)
      // Update sort orders
      values.forEach((value, i) => {
        value.sort_order = i + 1
      })
    }
  }

  function addSubcategory(valueIndex: number) {
    if (values[valueIndex].subcategories) {
      values[valueIndex].subcategories.push({
        key: '',
        label: '',
        description: ''
      })
      values = [...values]
    }
  }

  function removeSubcategory(valueIndex: number, subIndex: number) {
    if (values[valueIndex].subcategories) {
      values[valueIndex].subcategories.splice(subIndex, 1)
      values = [...values]
    }
  }

  function formatValueKey(input: string): string {
    return input
      .toLowerCase()
      .replace(/[^a-z0-9_]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
  }

  function validateForm(): boolean {
    if (!displayName.trim()) {
      addToast({
        type: 'error',
        title: 'Validation Error',
        message: 'Display name is required'
      })
      return false
    }

    if (values.length === 0) {
      addToast({
        type: 'error',
        title: 'Validation Error',
        message: 'At least one enum value is required'
      })
      return false
    }

    for (const value of values) {
      if (!value.key.trim() || !value.label.trim()) {
        addToast({
          type: 'error',
          title: 'Validation Error',
          message: 'All enum values must have both key and label'
        })
        return false
      }

      // Validate subcategories if hierarchical
      if (value.subcategories) {
        for (const sub of value.subcategories) {
          if (!sub.key.trim() || !sub.label.trim()) {
            addToast({
              type: 'error',
              title: 'Validation Error',
              message: 'All subcategories must have both key and label'
            })
            return false
          }
        }
      }
    }

    return true
  }

  // Handle form errors
  $: if (form?.error) {
    addToast({
      type: 'error',
      title: 'Error',
      message: form.error
    })
  }
</script>

<svelte:head>
  <title>Edit {enumData.display_name} - Enum Management</title>
</svelte:head>

<div class="space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div class="flex items-center space-x-4">
      <a
        href="/enums/{enumData.id}"
        class="flex items-center px-3 py-2 text-sm font-medium border border-input bg-background hover:bg-accent rounded-md transition-colors"
      >
        <ArrowLeft class="w-4 h-4 mr-2" />
        Back to Details
      </a>
      <div>
        <h1 class="text-2xl font-bold flex items-center gap-3">
          Edit {enumData.display_name}
          {#if enumData.is_system}
            <Shield class="w-6 h-6 text-blue-500" title="System Protected" />
          {/if}
          {#if enumData.is_hierarchical}
            <Settings class="w-6 h-6 text-green-500" title="Hierarchical Structure" />
          {/if}
        </h1>
        <p class="text-muted-foreground mt-1">
          Category: <code class="bg-muted px-2 py-1 rounded text-sm">{enumData.category}</code>
        </p>
      </div>
    </div>
  </div>

  <!-- System Enum Warning -->
  {#if enumData.is_system}
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-start space-x-3">
        <Shield class="w-5 h-5 text-blue-500 mt-0.5" />
        <div class="text-sm">
          <p class="font-medium text-blue-900">System Protected Enum</p>
          <p class="text-blue-700 mt-1">
            This is a core system enum. Changes should be made carefully as they may affect application functionality.
          </p>
        </div>
      </div>
    </div>
  {/if}

  <!-- Hierarchical Info -->
  {#if enumData.is_hierarchical}
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
      <div class="flex items-start space-x-3">
        <Settings class="w-5 h-5 text-green-500 mt-0.5" />
        <div class="text-sm">
          <p class="font-medium text-green-900">Hierarchical Enum</p>
          <p class="text-green-700 mt-1">
            This enum supports subcategories. You can expand values to edit their subcategories.
          </p>
        </div>
      </div>
    </div>
  {/if}

  <!-- Form Section -->
  <form 
    method="POST" 
    action="?/updateEnum"
    use:enhance={({ formData, cancel }) => {
      if (!validateForm()) {
        cancel()
        return
      }
      
      isSubmitting = true
      formData.set('values', JSON.stringify(values))
      
      return async ({ result, update }) => {
        isSubmitting = false
        if (result.type === 'success') {
          addToast({
            type: 'success',
            title: 'Enum Updated',
            message: 'The enum has been updated successfully.'
          })
        }
        await update()
      }
    }}
    class="space-y-6"
  >
    <!-- Basic Information -->
    <div class="bg-card border rounded-lg p-6">
      <h2 class="text-lg font-semibold mb-4">Basic Information</h2>
      
      <div class="space-y-4">
        <div>
          <label for="display_name" class="block text-sm font-medium mb-2">
            Display Name *
          </label>
          <input
            type="text"
            id="display_name"
            name="display_name"
            bind:value={displayName}
            placeholder="e.g., Job Statuses"
            class="w-full px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            required
          />
        </div>

        <div>
          <label for="description" class="block text-sm font-medium mb-2">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            bind:value={description}
            placeholder="Describe what this enum is used for..."
            rows="2"
            class="w-full px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
          ></textarea>
        </div>
      </div>
    </div>

    <!-- Enum Values -->
    <div class="bg-card border rounded-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold">Enum Values</h2>
        <button
          type="button"
          on:click={addValue}
          class="flex items-center px-3 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 rounded-md transition-colors"
        >
          <Plus class="w-4 h-4 mr-2" />
          Add Value
        </button>
      </div>

      <div class="space-y-4">
        {#each values as value, index}
          <div class="border rounded-lg p-4 bg-muted/30">
            <!-- Main Value -->
            <div class="grid grid-cols-12 gap-3 items-center">
              <!-- Expand/Collapse for hierarchical -->
              <div class="col-span-1">
                {#if enumData.is_hierarchical && value.subcategories}
                  <button
                    type="button"
                    on:click={() => toggleExpanded(index)}
                    class="p-1 hover:bg-accent rounded transition-colors"
                  >
                    {#if expandedValues.has(index)}
                      <ChevronDown class="w-4 h-4" />
                    {:else}
                      <ChevronRight class="w-4 h-4" />
                    {/if}
                  </button>
                {:else}
                  <span class="text-sm text-muted-foreground">{value.sort_order || index + 1}</span>
                {/if}
              </div>

              <!-- Key -->
              <div class="col-span-3">
                <input
                  type="text"
                  bind:value={value.key}
                  on:input={(e) => value.key = formatValueKey(e.target.value)}
                  placeholder="key"
                  class="w-full px-2 py-1 text-sm bg-background border border-input rounded focus:outline-none focus:ring-1 focus:ring-ring"
                  required
                />
              </div>

              <!-- Label -->
              <div class="col-span-4">
                <input
                  type="text"
                  bind:value={value.label}
                  placeholder="Display Label"
                  class="w-full px-2 py-1 text-sm bg-background border border-input rounded focus:outline-none focus:ring-1 focus:ring-ring"
                  required
                />
              </div>

              <!-- Color -->
              <div class="col-span-2">
                <div class="flex items-center space-x-2">
                  <input
                    type="color"
                    bind:value={value.color}
                    class="w-8 h-8 border border-input rounded cursor-pointer"
                  />
                  <div class="flex gap-1">
                    {#each colorOptions.slice(0, 2) as color}
                      <button
                        type="button"
                        on:click={() => value.color = color}
                        class="w-3 h-3 rounded border border-gray-300 hover:scale-110 transition-transform"
                        style="background-color: {color}"
                        title={color}
                      ></button>
                    {/each}
                  </div>
                </div>
              </div>

              <!-- Active -->
              <div class="col-span-1 flex justify-center">
                <input
                  type="checkbox"
                  bind:checked={value.active}
                  class="w-4 h-4 text-primary bg-background border-gray-300 rounded focus:ring-primary"
                  title="Active"
                />
              </div>

              <!-- Actions -->
              <div class="col-span-1 flex justify-end">
                <button
                  type="button"
                  on:click={() => removeValue(index)}
                  disabled={values.length <= 1}
                  class="p-1 text-destructive hover:bg-destructive/10 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Remove Value"
                >
                  <Trash2 class="w-4 h-4" />
                </button>
              </div>
            </div>

            <!-- Subcategories (for hierarchical enums) -->
            {#if enumData.is_hierarchical && value.subcategories && expandedValues.has(index)}
              <div class="mt-4 ml-8 space-y-3 border-l-2 border-muted pl-4">
                <div class="flex items-center justify-between">
                  <h4 class="text-sm font-medium">Subcategories</h4>
                  <button
                    type="button"
                    on:click={() => addSubcategory(index)}
                    class="flex items-center px-2 py-1 text-xs font-medium bg-secondary text-secondary-foreground hover:bg-secondary/80 rounded transition-colors"
                  >
                    <Plus class="w-3 h-3 mr-1" />
                    Add Subcategory
                  </button>
                </div>

                {#each value.subcategories as subcategory, subIndex}
                  <div class="grid grid-cols-12 gap-2 items-center bg-background/50 p-2 rounded">
                    <!-- Sub Key -->
                    <div class="col-span-3">
                      <input
                        type="text"
                        bind:value={subcategory.key}
                        on:input={(e) => subcategory.key = formatValueKey(e.target.value)}
                        placeholder="sub_key"
                        class="w-full px-2 py-1 text-xs bg-background border border-input rounded"
                        required
                      />
                    </div>

                    <!-- Sub Label -->
                    <div class="col-span-4">
                      <input
                        type="text"
                        bind:value={subcategory.label}
                        placeholder="Subcategory Label"
                        class="w-full px-2 py-1 text-xs bg-background border border-input rounded"
                        required
                      />
                    </div>

                    <!-- Sub Description -->
                    <div class="col-span-4">
                      <input
                        type="text"
                        bind:value={subcategory.description}
                        placeholder="Description (optional)"
                        class="w-full px-2 py-1 text-xs bg-background border border-input rounded"
                      />
                    </div>

                    <!-- Remove Sub -->
                    <div class="col-span-1 flex justify-end">
                      <button
                        type="button"
                        on:click={() => removeSubcategory(index, subIndex)}
                        class="p-1 text-destructive hover:bg-destructive/10 rounded transition-colors"
                        title="Remove Subcategory"
                      >
                        <Trash2 class="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                {/each}
              </div>
            {/if}
          </div>
        {/each}
      </div>
    </div>

    <!-- Form Actions -->
    <div class="flex items-center justify-between pt-6 border-t">
      <a
        href="/enums/{enumData.id}"
        class="flex items-center px-4 py-2 text-sm font-medium border border-input bg-background hover:bg-accent rounded-md transition-colors"
      >
        Cancel
      </a>
      <button
        type="submit"
        disabled={isSubmitting}
        class="flex items-center px-6 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 rounded-md transition-colors disabled:opacity-50"
      >
        {#if isSubmitting}
          <div class="w-4 h-4 mr-2 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin"></div>
          Saving...
        {:else}
          <Save class="w-4 h-4 mr-2" />
          Save Changes
        {/if}
      </button>
    </div>
  </form>
</div>
