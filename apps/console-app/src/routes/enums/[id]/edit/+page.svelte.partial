            <button
              type="submit"
              disabled={loading || errors.length > 0 || !displayName || values.length === 0 || !values.some(v => v.active) || !hasChanges}
              class="w-full flex items-center justify-center px-4 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
            >
              {#if loading}
                <div class="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin mr-2"></div>
                Updating...
              {:else}
                <Save class="w-4 h-4 mr-2" />
                Update Enum
              {/if}
            </button>
            
            <button
              type="button"
              on:click={() => goto(`/enums/${enumData.id}`)}
              class="w-full flex items-center justify-center px-4 py-2 text-sm font-medium border border-input bg-background hover:bg-accent rounded-md transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Hidden form fields -->
    <input type="hidden" name="values" value={JSON.stringify(values)} />
    <input type="hidden" name="version" value={enumData.version} />
  </form>
</div>

<!-- Click outside to close color picker -->
<svelte:window on:click={() => showColorPicker = -1} />
