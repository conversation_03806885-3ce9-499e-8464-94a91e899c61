// Enhanced enum edit for application-wide enums
import { fail, redirect, error } from '@sveltejs/kit'
import { createSupabaseAdminClient } from '$lib/server/supabase-admin'
import type { Actions, PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ locals, params }) => {
  const { consoleUser, authManager } = locals
  const { id } = params

  if (!consoleUser) {
    throw redirect(302, '/login')
  }

  // Check permissions
  const hasPermission = await authManager.validatePermission(
    consoleUser.id,
    'enums',
    'update'
  )

  if (!hasPermission) {
    throw redirect(302, '/dashboard?error=insufficient_permissions')
  }

  try {
    const supabase = createSupabaseAdminClient()

    // Get enum details from application_enums table
    const { data: enumData, error: enumError } = await supabase
      .from('application_enums')
      .select('*')
      .eq('id', id)
      .single()

    if (enumError || !enumData) {
      console.error('Error fetching enum:', enumError)
      throw error(404, 'Enum not found')
    }

    // Check if enum is editable
    if (!enumData.is_editable) {
      throw redirect(302, `/enums/${id}?error=not_editable`)
    }

    return {
      consoleUser,
      enumData
    }

  } catch (err) {
    console.error('Enum edit load error:', err)
    if (err.status) {
      throw err // Re-throw SvelteKit errors
    }
    throw error(500, 'Failed to load enum for editing')
  }
}

export const actions: Actions = {
  updateEnum: async ({ request, locals, params, getClientAddress }) => {
    const { consoleUser, authManager } = locals
    const { id } = params

    if (!consoleUser) {
      return fail(401, { error: 'Unauthorized' })
    }

    // Check permissions
    const hasPermission = await authManager.validatePermission(
      consoleUser.id,
      'enums',
      'update'
    )

    if (!hasPermission) {
      return fail(403, { error: 'Insufficient permissions' })
    }

    try {
      const formData = await request.formData()
      const displayName = formData.get('display_name') as string
      const description = formData.get('description') as string
      const valuesJson = formData.get('values') as string

      // Validate required fields
      if (!displayName || !valuesJson) {
        return fail(400, { 
          error: 'Display name and values are required',
          values: { displayName, description }
        })
      }

      // Parse and validate values JSON
      let values
      try {
        values = JSON.parse(valuesJson)
        if (!Array.isArray(values)) {
          throw new Error('Values must be an array')
        }
      } catch (error) {
        return fail(400, { 
          error: 'Invalid values format. Must be valid JSON array.',
          values: { displayName, description }
        })
      }

      // Validate enum values structure
      for (const value of values) {
        if (!value.key || !value.label) {
          return fail(400, { 
            error: 'Each enum value must have a key and label',
            values: { displayName, description }
          })
        }
        if (!/^[a-z0-9_]+$/.test(value.key)) {
          return fail(400, { 
            error: 'Enum value keys must contain only lowercase letters, numbers, and underscores',
            values: { displayName, description }
          })
        }
      }

      const supabase = createSupabaseAdminClient()

      // Get current enum data for comparison
      const { data: currentEnum, error: fetchError } = await supabase
        .from('application_enums')
        .select('*')
        .eq('id', id)
        .single()

      if (fetchError || !currentEnum) {
        return fail(404, { error: 'Enum not found' })
      }

      // Check if enum is editable
      if (!currentEnum.is_editable) {
        return fail(403, { error: 'This enum is not editable' })
      }

      // Update the enum
      const { data: updatedEnum, error: updateError } = await supabase
        .from('application_enums')
        .update({
          display_name: displayName,
          description: description || null,
          values,
          version: (currentEnum.version || 1) + 1,
          updated_at: new Date().toISOString(),
          updated_by: consoleUser.id
        })
        .eq('id', id)
        .select()
        .single()

      if (updateError) {
        console.error('Error updating enum:', updateError)
        return fail(500, { 
          error: 'Failed to update enum',
          values: { displayName, description }
        })
      }

      // Log the operation
      await supabase
        .from('application_enum_operations')
        .insert({
          enum_id: id,
          operation_type: 'updated',
          user_id: consoleUser.id,
          changes: {
            display_name: displayName,
            values_count: values.length,
            previous_version: currentEnum.version || 1,
            new_version: (currentEnum.version || 1) + 1
          }
        })

      // Log security event
      await authManager.logSecurityEvent({
        event_type: 'enum_updated',
        user_id: consoleUser.id,
        user_email: consoleUser.email,
        user_role: consoleUser.role,
        resource: 'application_enums',
        resource_id: id,
        success: true,
        ip_address: getClientAddress(),
        metadata: {
          enum_category: currentEnum.category,
          enum_display_name: displayName,
          values_count: values.length
        },
        timestamp: new Date().toISOString()
      })

      throw redirect(302, `/enums/${id}?updated=true`)

    } catch (error) {
      if (error.status === 302) {
        throw error // Re-throw redirect
      }
      console.error('Update enum error:', error)
      return fail(500, { error: 'Internal server error' })
    }
  }
}
