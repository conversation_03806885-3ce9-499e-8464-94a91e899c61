<script lang="ts">
  import type { PageData } from './$types'
  import { 
    ArrowLeft,
    Edit,
    Shield,
    Settings,
    Database,
    Clock,
    User,
    Eye,
    Trash2
  } from 'lucide-svelte'
  import { addToast } from '$stores/toast'

  export let data: PageData

  const { consoleUser, enumData, operations } = data

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  function getEnumValueCount(values: any[]): number {
    if (!Array.isArray(values)) return 0
    let count = values.length
    
    // Count subcategories for hierarchical enums
    values.forEach(value => {
      if (value.subcategories && Array.isArray(value.subcategories)) {
        count += value.subcategories.length
      }
    })
    
    return count
  }

  function getActiveValueCount(values: any[]): number {
    if (!Array.isArray(values)) return 0
    return values.filter(value => value.active !== false).length
  }

  function copyEnumKey(key: string) {
    navigator.clipboard.writeText(key)
    addToast({
      type: 'success',
      title: 'Copied',
      message: `Enum key "${key}" copied to clipboard`
    })
  }
</script>

<svelte:head>
  <title>{enumData.display_name} - Enum Management</title>
</svelte:head>

<div class="space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div class="flex items-center space-x-4">
      <a
        href="/enums"
        class="flex items-center px-3 py-2 text-sm font-medium border border-input bg-background hover:bg-accent rounded-md transition-colors"
      >
        <ArrowLeft class="w-4 h-4 mr-2" />
        Back to Enums
      </a>
      <div>
        <h1 class="text-2xl font-bold flex items-center gap-3">
          {enumData.display_name}
          {#if enumData.is_system}
            <Shield class="w-6 h-6 text-blue-500" title="System Protected" />
          {/if}
          {#if enumData.is_hierarchical}
            <Settings class="w-6 h-6 text-green-500" title="Hierarchical Structure" />
          {/if}
        </h1>
        <p class="text-muted-foreground mt-1">
          Category: <code class="bg-muted px-2 py-1 rounded text-sm">{enumData.category}</code>
        </p>
      </div>
    </div>
    <div class="flex items-center space-x-3">
      {#if enumData.is_editable}
        <a
          href="/enums/{enumData.id}/edit"
          class="flex items-center px-4 py-2 text-sm font-medium border border-input bg-background hover:bg-accent rounded-md transition-colors"
        >
          <Edit class="w-4 h-4 mr-2" />
          Edit Enum
        </a>
      {/if}
    </div>
  </div>

  <!-- Overview Cards -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
    <div class="bg-card border rounded-lg p-4">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-muted-foreground">Total Values</p>
          <p class="text-2xl font-bold">{getEnumValueCount(enumData.values || [])}</p>
        </div>
        <Database class="w-8 h-8 text-primary" />
      </div>
    </div>
    <div class="bg-card border rounded-lg p-4">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-muted-foreground">Active Values</p>
          <p class="text-2xl font-bold">{getActiveValueCount(enumData.values || [])}</p>
        </div>
        <Eye class="w-8 h-8 text-green-500" />
      </div>
    </div>
    <div class="bg-card border rounded-lg p-4">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-muted-foreground">Version</p>
          <p class="text-2xl font-bold">v{enumData.version || 1}</p>
        </div>
        <Settings class="w-8 h-8 text-blue-500" />
      </div>
    </div>
    <div class="bg-card border rounded-lg p-4">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-muted-foreground">Last Updated</p>
          <p class="text-sm font-bold">{formatDate(enumData.updated_at)}</p>
        </div>
        <Clock class="w-8 h-8 text-purple-500" />
      </div>
    </div>
  </div>

  <!-- Description -->
  {#if enumData.description}
    <div class="bg-card border rounded-lg p-6">
      <h2 class="text-lg font-semibold mb-3">Description</h2>
      <p class="text-muted-foreground">{enumData.description}</p>
    </div>
  {/if}

  <!-- Enum Values -->
  <div class="bg-card border rounded-lg p-6">
    <h2 class="text-lg font-semibold mb-4">Enum Values</h2>
    
    {#if enumData.values && enumData.values.length > 0}
      <div class="space-y-3">
        {#each enumData.values as value, index}
          <div class="border rounded-lg p-4 {value.active === false ? 'opacity-60' : ''}">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <!-- Color indicator -->
                <div 
                  class="w-4 h-4 rounded-full border-2 border-gray-300" 
                  style="background-color: {value.color || '#6b7280'}"
                ></div>
                
                <!-- Value info -->
                <div>
                  <div class="flex items-center space-x-2">
                    <h3 class="font-medium">{value.label}</h3>
                    {#if value.active === false}
                      <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">Inactive</span>
                    {/if}
                  </div>
                  <button
                    on:click={() => copyEnumKey(value.key)}
                    class="text-sm text-muted-foreground hover:text-primary transition-colors"
                    title="Click to copy"
                  >
                    Key: <code class="bg-muted px-1 py-0.5 rounded">{value.key}</code>
                  </button>
                  {#if value.description}
                    <p class="text-sm text-muted-foreground mt-1">{value.description}</p>
                  {/if}
                </div>
              </div>

              <!-- Sort order -->
              {#if value.sort_order}
                <div class="text-sm text-muted-foreground">
                  Order: {value.sort_order}
                </div>
              {/if}
            </div>

            <!-- Subcategories for hierarchical enums -->
            {#if value.subcategories && value.subcategories.length > 0}
              <div class="mt-4 ml-8 space-y-2">
                <h4 class="text-sm font-medium text-muted-foreground">Subcategories:</h4>
                {#each value.subcategories as subcategory}
                  <div class="flex items-center space-x-3 p-2 bg-muted/50 rounded">
                    <div class="w-2 h-2 rounded-full bg-gray-400"></div>
                    <div>
                      <span class="text-sm font-medium">{subcategory.label}</span>
                      <button
                        on:click={() => copyEnumKey(`${value.key}_${subcategory.key}`)}
                        class="ml-2 text-xs text-muted-foreground hover:text-primary transition-colors"
                        title="Click to copy combined key"
                      >
                        <code class="bg-muted px-1 py-0.5 rounded">{value.key}_{subcategory.key}</code>
                      </button>
                      {#if subcategory.description}
                        <p class="text-xs text-muted-foreground mt-1">{subcategory.description}</p>
                      {/if}
                    </div>
                  </div>
                {/each}
              </div>
            {/if}
          </div>
        {/each}
      </div>
    {:else}
      <div class="text-center py-8 text-muted-foreground">
        <Database class="w-12 h-12 mx-auto mb-4 opacity-50" />
        <p>No enum values defined</p>
      </div>
    {/if}
  </div>

  <!-- Configuration -->
  <div class="bg-card border rounded-lg p-6">
    <h2 class="text-lg font-semibold mb-4">Configuration</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium">System Protected</span>
          <span class="text-sm {enumData.is_system ? 'text-blue-600' : 'text-gray-500'}">
            {enumData.is_system ? 'Yes' : 'No'}
          </span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium">User Editable</span>
          <span class="text-sm {enumData.is_editable ? 'text-green-600' : 'text-gray-500'}">
            {enumData.is_editable ? 'Yes' : 'No'}
          </span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium">Hierarchical</span>
          <span class="text-sm {enumData.is_hierarchical ? 'text-purple-600' : 'text-gray-500'}">
            {enumData.is_hierarchical ? 'Yes' : 'No'}
          </span>
        </div>
      </div>
      
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium">Created</span>
          <span class="text-sm text-muted-foreground">{formatDate(enumData.created_at)}</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium">Updated</span>
          <span class="text-sm text-muted-foreground">{formatDate(enumData.updated_at)}</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium">Current Version</span>
          <span class="text-sm text-muted-foreground">v{enumData.version || 1}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Operation History -->
  {#if operations && operations.length > 0}
    <div class="bg-card border rounded-lg p-6">
      <h2 class="text-lg font-semibold mb-4">Recent Operations</h2>
      
      <div class="space-y-3">
        {#each operations as operation}
          <div class="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
            <div class="flex items-center space-x-3">
              <User class="w-4 h-4 text-muted-foreground" />
              <div>
                <p class="text-sm font-medium capitalize">{operation.operation_type.replace('_', ' ')}</p>
                <p class="text-xs text-muted-foreground">
                  {operation.console_users?.email || 'Unknown user'}
                </p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-xs text-muted-foreground">{formatDate(operation.timestamp)}</p>
            </div>
          </div>
        {/each}
      </div>
    </div>
  {/if}
</div>
