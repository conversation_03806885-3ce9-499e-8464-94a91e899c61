import { json } from '@sveltejs/kit'
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types'
import { supabaseAdmin } from '$lib/server/supabase-admin'

export const GET: RequestHandler = async ({ url }) => {
  try {
    const timeframe = url.searchParams.get('timeframe') || '30d'
    const metric = url.searchParams.get('metric') || 'overview'

    switch (metric) {
      case 'overview':
        return await getOverviewAnalytics(timeframe)
      case 'trends':
        return await getTrendAnalytics(timeframe)
      case 'domains':
        return await getDomainAnalytics(timeframe)
      case 'performance':
        return await getPerformanceAnalytics(timeframe)
      case 'workflow':
        return await getWorkflowAnalytics(timeframe)
      case 'funnel':
        return await getFunnelAnalytics(timeframe)
      default:
        return await getOverviewAnalytics(timeframe)
    }
  } catch (error) {
    console.error('Analytics API error:', error)
    return json({ error: 'Failed to fetch analytics' }, { status: 500 })
  }
}

async function getOverviewAnalytics(timeframe: string) {
  try {
    // Calculate date range
    const days = parseInt(timeframe.replace('d', ''))
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)
    
    // Query business_registrations directly
    const { data: registrations, error } = await supabaseAdmin
      .from('business_registrations')
      .select('*')
      .gte('created_at', startDate.toISOString())
    
    if (error) {
      console.error('Overview analytics query error:', error)
      return json({ 
        success: false, 
        data: {
          total_count: 0,
          pending_count: 0,
          approved_count: 0,
          rejected_count: 0,
          new_company_count: 0,
          existing_domain_count: 0,
          avg_approval_time_hours: 0,
          timeframe: timeframe
        }
      })
    }

    const validRegistrations = registrations || []

    // Calculate analytics
    const analytics = {
      total_count: validRegistrations.length,
      pending_count: validRegistrations.filter((r: any) => r.status === 'pending').length,
      approved_count: validRegistrations.filter((r: any) => r.status === 'approved').length,
      rejected_count: validRegistrations.filter((r: any) => r.status === 'rejected').length,
      new_company_count: validRegistrations.filter((r: any) => !r.activated_company_id).length,
      existing_domain_count: validRegistrations.filter((r: any) => r.activated_company_id).length,
      avg_approval_time_hours: calculateAverageApprovalTime(validRegistrations),
      timeframe: timeframe
    }

    return json({
      success: true,
      data: analytics
    })
  } catch (error) {
    console.error('Overview analytics error:', error)
    return json({ 
      success: false, 
      data: {
        total_count: 0,
        pending_count: 0,
        approved_count: 0,
        rejected_count: 0,
        new_company_count: 0,
        existing_domain_count: 0,
        avg_approval_time_hours: 0,
        timeframe: timeframe
      }
    })
  }
}

async function getTrendAnalytics(timeframe: string) {
  try {
    const days = parseInt(timeframe.replace('d', ''))
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)
    
    // Query business_registrations directly
    const { data: registrations, error } = await supabaseAdmin
      .from('business_registrations')
      .select('created_at, status')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true })
    
    if (error) {
      console.error('Trend analytics query error:', error)
      return json({ success: false, data: [] })
    }

    // Group by date
    const trendData = groupRegistrationsByDate(registrations || [])

    return json({
      success: true,
      data: trendData
    })
  } catch (error) {
    console.error('Trend analytics error:', error)
    return json({ success: false, data: [] })
  }
}

async function getDomainAnalytics(timeframe: string) {
  try {
    const days = parseInt(timeframe.replace('d', ''))
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)
    
    // Query business_registrations directly
    const { data: registrations, error } = await supabaseAdmin
      .from('business_registrations')
      .select('contact_person_email, status, activated_company_id')
      .gte('created_at', startDate.toISOString())
    
    if (error) {
      console.error('Domain analytics query error:', error)
      return json({ success: false, data: {} })
    }

    const domainAnalytics = analyzeDomains(registrations || [])

    return json({
      success: true,
      data: domainAnalytics
    })
  } catch (error) {
    console.error('Domain analytics error:', error)
    return json({ success: false, data: {} })
  }
}

async function getPerformanceAnalytics(timeframe: string) {
  return json({
    success: true,
    data: {
      email_delivery_rate: 98.5,
      avg_response_time: 2.3,
      template_performance: {
        welcome: 95.2,
        invitation: 87.8,
        approval: 99.1
      }
    }
  })
}

async function getWorkflowAnalytics(timeframe: string) {
  try {
    const days = parseInt(timeframe.replace('d', ''))
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)
    
    // Query business_registrations directly
    const { data: registrations, error } = await supabaseAdmin
      .from('business_registrations')
      .select('*')
      .gte('created_at', startDate.toISOString())
    
    if (error) {
      console.error('Workflow analytics query error:', error)
      return json({ success: false, data: null })
    }

    const validRegistrations = registrations || []

    // Calculate stage durations
    const stageData = [
      {
        stage: 'Domain Detection',
        avgDuration: 0.5, // Usually instant
        description: 'Automated domain analysis'
      },
      {
        stage: 'Initial Review',
        avgDuration: calculateStageAverage(validRegistrations, 'pending'),
        description: 'First review by team'
      },
      {
        stage: 'Approval Decision',
        avgDuration: calculateStageAverage(validRegistrations, 'approved'),
        description: 'Final approval process'
      },
      {
        stage: 'Company Setup',
        avgDuration: 1.2,
        description: 'Account activation'
      }
    ]

    // Calculate bottlenecks
    const bottleneckData = stageData.filter(stage => stage.avgDuration > 3)

    // Generate funnel data
    const funnelData = [
      { name: 'Submissions', value: validRegistrations.length },
      { name: 'Under Review', value: validRegistrations.filter(r => r.status === 'pending').length },
      { name: 'Approved', value: validRegistrations.filter(r => r.status === 'approved').length },
      { name: 'Activated', value: validRegistrations.filter(r => r.status === 'approved').length } // Simplified
    ]

    // User action data (simplified for demo)
    const userActionData = validRegistrations.map(r => ({
      timestamp: r.created_at,
      action: 'submission',
      user: r.contact_person_email
    }))

    return json({
      success: true,
      data: {
        stageData,
        bottleneckData,
        funnelData,
        userActionData: userActionData.slice(0, 100) // Limit for performance
      }
    })
  } catch (error) {
    console.error('Workflow analytics error:', error)
    return json({ success: false, data: null })
  }
}

async function getFunnelAnalytics(timeframe: string) {
  try {
    const days = parseInt(timeframe.replace('d', ''))
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)
    
    // Query business_registrations directly
    const { data: registrations, error } = await supabaseAdmin
      .from('business_registrations')
      .select('status, created_at, updated_at')
      .gte('created_at', startDate.toISOString())
    
    if (error) {
      console.error('Funnel analytics query error:', error)
      return json({ success: false, data: [] })
    }

    const validRegistrations = registrations || []

    const funnelStages = [
      { name: 'Registration Submitted', value: validRegistrations.length },
      { name: 'Domain Detected', value: validRegistrations.length }, // Assume all go through detection
      { name: 'Under Review', value: validRegistrations.filter(r => ['pending', 'approved', 'rejected'].includes(r.status)).length },
      { name: 'Decision Made', value: validRegistrations.filter(r => ['approved', 'rejected'].includes(r.status)).length },
      { name: 'Approved', value: validRegistrations.filter(r => r.status === 'approved').length }
    ]

    return json({
      success: true,
      data: funnelStages
    })
  } catch (error) {
    console.error('Funnel analytics error:', error)
    return json({ success: false, data: [] })
  }
}

function calculateStageAverage(registrations: any[], status: string) {
  const filtered = registrations.filter(r => r.status === status && r.created_at && r.updated_at)
  if (filtered.length === 0) return 0
  
  const totalDays = filtered.reduce((sum, reg) => {
    const created = new Date(reg.created_at)
    const updated = new Date(reg.updated_at)
    const days = (updated.getTime() - created.getTime()) / (1000 * 60 * 60 * 24)
    return sum + days
  }, 0)
  
  return Math.round(totalDays / filtered.length * 10) / 10 // Round to 1 decimal
}

function calculateAverageApprovalTime(registrations: any[]) {
  const approvedRegistrations = registrations.filter(r => 
    r.status === 'approved' && r.created_at && r.updated_at
  )
  
  if (approvedRegistrations.length === 0) return 0
  
  const totalHours = approvedRegistrations.reduce((sum, reg) => {
    const created = new Date(reg.created_at)
    const updated = new Date(reg.updated_at)
    const hours = (updated.getTime() - created.getTime()) / (1000 * 60 * 60)
    return sum + hours
  }, 0)
  
  return Math.round(totalHours / approvedRegistrations.length)
}

function groupRegistrationsByDate(registrations: any[]) {
  const grouped = registrations.reduce((acc: any, reg: any) => {
    const date = new Date(reg.created_at).toISOString().split('T')[0]
    
    if (!acc[date]) {
      acc[date] = { date, total: 0, approved: 0, pending: 0, rejected: 0 }
    }
    
    acc[date].total++
    acc[date][reg.status as keyof typeof acc[typeof date]]++
    
    return acc
  }, {})
  
  return Object.values(grouped)
}

function analyzeDomains(registrations: any[]) {
  const domains = registrations.reduce((acc: any, reg: any) => {
    if (!reg.contact_person_email) return acc
    
    const domain = reg.contact_person_email.split('@')[1]
    if (!domain) return acc
    
    if (!acc[domain]) {
      acc[domain] = { 
        domain, 
        total: 0, 
        approved: 0, 
        pending: 0, 
        rejected: 0,
        detection_accuracy: 0,
        existing_match: 0
      }
    }
    
    acc[domain].total++
    acc[domain][reg.status]++
    if (reg.activated_company_id) acc[domain].existing_match++
    
    return acc
  }, {})
  
  // Calculate detection accuracy
  Object.values(domains).forEach((domain: any) => {
    domain.detection_accuracy = domain.total > 0 
      ? Math.round((domain.existing_match / domain.total) * 100)
      : 0
  })
  
  return {
    domains: Object.values(domains).slice(0, 10), // Top 10 domains
    total_domains: Object.keys(domains).length,
    detection_rate: Object.values(domains).reduce((sum: number, d: any) => sum + d.detection_accuracy, 0) / Object.keys(domains).length || 0
  }
}
