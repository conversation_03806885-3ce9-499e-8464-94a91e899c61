import { json } from '@sveltejs/kit'
import type { Request<PERSON>and<PERSON> } from './$types'
import { supabaseAdmin } from '$lib/server/supabase-admin'

export const GET: RequestHandler = async ({ url }) => {
  try {
    const timeframe = url.searchParams.get('timeframe') || '30d'
    
    // Calculate date range
    const now = new Date()
    let startDate: Date
    
    switch (timeframe) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    // Query business_registrations directly
    const { data: metricsData, error } = await supabaseAdmin
      .from('business_registrations')
      .select('created_at, status, activated_company_id')
      .gte('created_at', startDate.toISOString())
      .order('created_at')
    
    if (error) {
      console.error('Error fetching metrics data:', error)
      return json({
        success: false,
        error: 'Failed to fetch metrics',
        metrics: {
          total: 0,
          pending: 0,
          approved: 0,
          rejected: 0,
          newCompanies: 0,
          existingCustomers: 0,
          approvalRate: 0
        }
      }, { status: 500 })
    }

    const registrations = metricsData || []

    // Calculate metrics
    const metrics = {
      total: registrations.length,
      pending: registrations.filter(r => r.status === 'pending').length,
      approved: registrations.filter(r => r.status === 'approved').length,
      rejected: registrations.filter(r => r.status === 'rejected').length,
      newCompanies: registrations.filter(r => !r.activated_company_id).length,
      existingCustomers: registrations.filter(r => r.activated_company_id).length,
      approvalRate: registrations.length > 0 
        ? Math.round((registrations.filter(r => r.status === 'approved').length / registrations.length) * 100)
        : 0
    }

    return json({
      success: true,
      timeframe,
      metrics,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error fetching metrics:', error)
    return json({
      success: false,
      error: 'Failed to fetch metrics',
      metrics: {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        newCompanies: 0,
        existingCustomers: 0,
        approvalRate: 0
      }
    }, { status: 500 })
  }
}
