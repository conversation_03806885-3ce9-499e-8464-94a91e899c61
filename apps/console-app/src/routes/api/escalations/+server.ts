// Escalation API endpoint for processing escalations and analytics
import { json, error } from '@sveltejs/kit';
import { escalationService } from '$lib/business-email/escalation-service';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { action } = await request.json();

    switch (action) {
      case 'process_escalations':
        console.log('[API] Processing escalations...');
        const result = await escalationService.processEscalations();
        return json({
          success: true,
          data: result,
          message: `Processed ${result.processed} registrations: ${result.escalated} escalated, ${result.auto_denied} auto-denied, ${result.reminders_sent} reminders sent`
        });

      case 'get_analytics':
        console.log('[API] Getting escalation analytics...');
        const analytics = await escalationService.getEscalationAnalytics();
        return json({
          success: true,
          data: analytics
        });

      case 'manual_escalation':
        const { registration_id, reason } = await request.json();
        if (!registration_id) {
          throw error(400, 'Registration ID is required for manual escalation');
        }
        
        console.log(`[API] Manually escalating registration ${registration_id}...`);
        const escalated = await escalationService.manualEscalation(registration_id, reason || 'Manual escalation');
        
        return json({
          success: escalated,
          message: escalated ? 'Registration escalated successfully' : 'Failed to escalate registration'
        });

      default:
        throw error(400, 'Invalid action. Supported actions: process_escalations, get_analytics, manual_escalation');
    }
  } catch (err) {
    console.error('[API] Escalation endpoint error:', err);
    throw error(500, {
      message: 'Failed to process escalation request',
      details: err instanceof Error ? err.message : 'Unknown error'
    });
  }
};

export const GET: RequestHandler = async () => {
  try {
    // Get escalation analytics for dashboard
    const analytics = await escalationService.getEscalationAnalytics();
    
    return json({
      success: true,
      data: analytics
    });
  } catch (err) {
    console.error('[API] Failed to get escalation analytics:', err);
    throw error(500, {
      message: 'Failed to get escalation analytics',
      details: err instanceof Error ? err.message : 'Unknown error'
    });
  }
};
