// Test endpoint for escalation processing
import { json } from '@sveltejs/kit';
import { runEscalationProcessor, escalationHealthCheck } from '$lib/business-email/escalation-processor';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { action } = await request.json();

    switch (action) {
      case 'run_processor':
        console.log('[TEST] Running escalation processor manually...');
        const result = await runEscalationProcessor();
        return json(result);

      case 'health_check':
        console.log('[TEST] Running escalation health check...');
        const health = await escalationHealthCheck();
        return json(health);

      default:
        return json({
          success: false,
          error: 'Invalid action. Use: run_processor, health_check'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('[TEST] Escalation test error:', error);
    return json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
};

export const GET: RequestHandler = async () => {
  // Default action is health check
  try {
    const health = await escalationHealthCheck();
    return json(health);
  } catch (error) {
    return json({
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
};
