<script lang="ts">
  import { enhance } from '$app/forms'
  import { addToast } from '$stores/toast'
  import type { PageData, ActionData } from './$types'
  import { User, Mail, Phone, Shield, Save, Key } from 'lucide-svelte'

  export let data: PageData
  export let form: ActionData

  const { consoleUser } = data

  // Form state
  let firstName = consoleUser.first_name || ''
  let lastName = consoleUser.last_name || ''
  let phone = consoleUser.phone || ''
  let loading = false

  // Handle form submission
  function handleSubmit() {
    loading = true
    return async ({ result, update }: { result: any; update: any }) => {
      loading = false
      
      if (result.type === 'success') {
        addToast({
          type: 'success',
          title: 'Profile Updated',
          message: 'Your profile has been updated successfully'
        })
      } else if (result.type === 'failure') {
        addToast({
          type: 'error',
          title: 'Update Failed',
          message: form?.error || 'Failed to update profile'
        })
      }
      
      await update()
    }
  }
</script>

<svelte:head>
  <title>Profile Settings - Console</title>
</svelte:head>

<div class="space-y-6">
  <!-- Header -->
  <div>
    <h1 class="text-2xl font-bold">Profile Settings</h1>
    <p class="text-muted-foreground mt-1">
      Manage your personal information and account settings
    </p>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    
    <!-- Profile Form -->
    <div class="lg:col-span-2">
      <form 
        method="POST" 
        action="?/updateProfile" 
        use:enhance={handleSubmit}
        class="bg-card border rounded-lg p-6"
      >
        <h3 class="text-lg font-semibold mb-6 flex items-center">
          <User class="w-5 h-5 mr-2" />
          Personal Information
        </h3>
        
        <div class="space-y-4">
          <!-- First Name -->
          <div>
            <label for="first_name" class="block text-sm font-medium mb-2">
              First Name *
            </label>
            <input
              id="first_name"
              type="text"
              name="first_name"
              bind:value={firstName}
              required
              class="w-full px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>

          <!-- Last Name -->
          <div>
            <label for="last_name" class="block text-sm font-medium mb-2">
              Last Name *
            </label>
            <input
              id="last_name"
              type="text"
              name="last_name"
              bind:value={lastName}
              required
              class="w-full px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>

          <!-- Phone -->
          <div>
            <label for="phone" class="block text-sm font-medium mb-2">
              Phone Number
            </label>
            <input
              id="phone"
              type="tel"
              name="phone"
              bind:value={phone}
              placeholder="+****************"
              class="w-full px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end pt-4">
            <button
              type="submit"
              disabled={loading || !firstName || !lastName}
              class="flex items-center px-4 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
            >
              {#if loading}
                <div class="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin mr-2"></div>
                Updating...
              {:else}
                <Save class="w-4 h-4 mr-2" />
                Update Profile
              {/if}
            </button>
          </div>
        </div>
      </form>
    </div>

    <!-- Account Information Sidebar -->
    <div class="space-y-6">
      
      <!-- Account Details -->
      <div class="bg-card border rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Shield class="w-5 h-5 mr-2" />
          Account Details
        </h3>
        
        <div class="space-y-4 text-sm">
          <div class="flex items-start space-x-2">
            <Mail class="w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0" />
            <div>
              <p class="font-medium">Email Address</p>
              <p class="text-muted-foreground">{consoleUser.email}</p>
            </div>
          </div>
          
          <div class="flex items-start space-x-2">
            <Shield class="w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0" />
            <div>
              <p class="font-medium">Role</p>
              <p class="text-muted-foreground capitalize">{consoleUser.role.replace('_', ' ')}</p>
            </div>
          </div>
          
          <div class="flex items-start space-x-2">
            <User class="w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0" />
            <div>
              <p class="font-medium">Account Status</p>
              <p class="text-green-600">Active</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Security Actions -->
      <div class="bg-card border rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Key class="w-5 h-5 mr-2" />
          Security
        </h3>
        
        <div class="space-y-3">
          <a
            href="/reset-password"
            class="w-full flex items-center justify-center px-4 py-2 text-sm font-medium border border-input bg-background hover:bg-accent rounded-md transition-colors"
          >
            <Key class="w-4 h-4 mr-2" />
            Change Password
          </a>
        </div>
      </div>

      <!-- Help -->
      <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
        <div class="text-sm">
          <p class="font-medium text-amber-900 dark:text-amber-200">Need Help?</p>
          <p class="text-amber-700 dark:text-amber-300 mt-1">
            Contact your system administrator if you need to update your email address or role permissions.
          </p>
        </div>
      </div>

    </div>
  </div>
</div>
