import { fail, redirect } from '@sveltejs/kit'
import { createSupabaseAdminClient } from '$lib/server/supabase-admin'
import type { Actions, PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ locals }) => {
  const { consoleUser } = locals

  if (!consoleUser) {
    throw redirect(302, '/login')
  }

  return {
    consoleUser
  }
}

export const actions: Actions = {
  updateProfile: async ({ request, locals }) => {
    const { consoleUser } = locals

    if (!consoleUser) {
      return fail(401, { error: 'Unauthorized' })
    }

    try {
      const formData = await request.formData()
      const firstName = formData.get('first_name') as string
      const lastName = formData.get('last_name') as string
      const phone = formData.get('phone') as string

      // Validate required fields
      if (!firstName || !lastName) {
        return fail(400, { 
          error: 'First name and last name are required',
          values: { firstName, lastName, phone }
        })
      }

      const supabase = createSupabaseAdminClient()

      // Update user profile
      const { error: updateError } = await supabase
        .from('console_users')
        .update({
          first_name: firstName,
          last_name: lastName,
          phone: phone || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', consoleUser.id)

      if (updateError) {
        console.error('Error updating profile:', updateError)
        return fail(500, { 
          error: 'Failed to update profile',
          values: { firstName, lastName, phone }
        })
      }

      return {
        success: true,
        message: 'Profile updated successfully'
      }

    } catch (error) {
      console.error('Profile update error:', error)
      return fail(500, { error: 'Internal server error while updating profile' })
    }
  }
}
