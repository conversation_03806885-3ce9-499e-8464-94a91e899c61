<script lang="ts">
  import { enhance } from '$app/forms'
  import { goto } from '$app/navigation'
  import { addToast } from '$stores/toast'
  import type { PageData, ActionData } from './$types'
  import { UserPlus, Lock, User, Mail, Shield, Eye, EyeOff } from 'lucide-svelte'

  export let data: PageData
  export let form: ActionData

  const { invitation } = data

  // Form state
  let firstName = ''
  let lastName = ''
  let password = ''
  let confirmPassword = ''
  let loading = false
  let showPassword = false
  let showConfirmPassword = false

  // Password validation
  $: passwordMatch = password === confirmPassword
  $: passwordValid = password.length >= 8
  $: formValid = firstName && lastName && password && confirmPassword && passwordMatch && passwordValid

  // Handle form submission
  function handleSubmit() {
    loading = true
    return async ({ result, update }: { result: any; update: any }) => {
      loading = false
      
      if (result.type === 'success') {
        addToast({
          type: 'success',
          title: 'Account Created',
          message: 'Your account has been created successfully!'
        })
        // Redirect to login
        goto('/login?success=account_created')
      } else if (result.type === 'failure') {
        addToast({
          type: 'error',
          title: 'Account Creation Failed',
          message: form?.error || 'Failed to create account'
        })
      }
      
      await update()
    }
  }
</script>

<svelte:head>
  <title>Accept Invitation - ProcureServe Console</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    
    <!-- Header -->
    <div class="text-center">
      <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
        <UserPlus class="w-8 h-8 text-primary" />
      </div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Welcome to ProcureServe</h1>
      <p class="text-gray-600 dark:text-gray-400 mt-2">
        Complete your account setup to get started
      </p>
    </div>

    <!-- Invitation Details -->
    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
      <div class="flex items-center space-x-3 mb-4">
        <Mail class="w-5 h-5 text-gray-400" />
        <div>
          <p class="text-sm font-medium text-gray-900 dark:text-white">{invitation.email}</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">Invited as {invitation.role.replace('_', ' ')}</p>
        </div>
      </div>
      
      <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
        <div class="flex items-start space-x-2">
          <Shield class="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
          <div class="text-sm">
            <p class="font-medium text-blue-900 dark:text-blue-200">Internal Access</p>
            <p class="text-blue-700 dark:text-blue-300">
              You're being granted access to the ProcureServe internal console.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Account Setup Form -->
    <form 
      method="POST" 
      action="?/acceptInvitation" 
      use:enhance={handleSubmit}
      class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 space-y-6"
    >
      <input type="hidden" name="token" value={invitation.token} />
      
      <div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Account Information</h3>
        
        <div class="grid grid-cols-2 gap-4">
          <!-- First Name -->
          <div>
            <label for="first_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              First Name *
            </label>
            <input
              id="first_name"
              type="text"
              name="first_name"
              bind:value={firstName}
              required
              class="w-full px-3 py-2 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:text-white"
              placeholder="Enter first name"
            />
          </div>

          <!-- Last Name -->
          <div>
            <label for="last_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Last Name *
            </label>
            <input
              id="last_name"
              type="text"
              name="last_name"
              bind:value={lastName}
              required
              class="w-full px-3 py-2 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:text-white"
              placeholder="Enter last name"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Security</h3>
        
        <!-- Password -->
        <div class="space-y-4">
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Password *
            </label>
            <div class="relative">
              <Lock class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                name="password"
                bind:value={password}
                required
                class="w-full pl-10 pr-10 py-2 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:text-white"
                placeholder="Enter password"
              />
              <button
                type="button"
                on:click={() => showPassword = !showPassword}
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {#if showPassword}
                  <EyeOff class="w-4 h-4" />
                {:else}
                  <Eye class="w-4 h-4" />
                {/if}
              </button>
            </div>
            {#if password && !passwordValid}
              <p class="text-xs text-red-600 mt-1">Password must be at least 8 characters long</p>
            {/if}
          </div>

          <!-- Confirm Password -->
          <div>
            <label for="confirm_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Confirm Password *
            </label>
            <div class="relative">
              <Lock class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                id="confirm_password"
                type={showConfirmPassword ? 'text' : 'password'}
                name="confirm_password"
                bind:value={confirmPassword}
                required
                class="w-full pl-10 pr-10 py-2 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:text-white"
                placeholder="Confirm password"
              />
              <button
                type="button"
                on:click={() => showConfirmPassword = !showConfirmPassword}
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {#if showConfirmPassword}
                  <EyeOff class="w-4 h-4" />
                {:else}
                  <Eye class="w-4 h-4" />
                {/if}
              </button>
            </div>
            {#if confirmPassword && !passwordMatch}
              <p class="text-xs text-red-600 mt-1">Passwords do not match</p>
            {/if}
          </div>
        </div>
      </div>

      <!-- Submit Button -->
      <button
        type="submit"
        disabled={!formValid || loading}
        class="w-full flex items-center justify-center px-4 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
      >
        {#if loading}
          <div class="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin mr-2"></div>
          Creating Account...
        {:else}
          <User class="w-4 h-4 mr-2" />
          Create Account
        {/if}
      </button>
    </form>

    <!-- Footer -->
    <div class="text-center">
      <p class="text-xs text-gray-500 dark:text-gray-400">
        By creating an account, you agree to ProcureServe's terms of service and privacy policy.
      </p>
    </div>

  </div>
</div>
