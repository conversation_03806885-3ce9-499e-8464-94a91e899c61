import { fail, redirect } from '@sveltejs/kit'
import { createSupabaseAdminClient } from '$lib/server/supabase-admin'
import { sendPasswordResetEmail } from '$lib/server/email'
import type { Actions, PageServerLoad } from './$types'
import crypto from 'crypto'

export const load: PageServerLoad = async ({ locals }) => {
  const { consoleUser } = locals

  if (!consoleUser) {
    throw redirect(302, '/login')
  }

  return {
    consoleUser
  }
}

export const actions: Actions = {
  requestReset: async ({ request, locals, getClientAddress }) => {
    const { consoleUser } = locals

    if (!consoleUser) {
      return fail(401, { error: 'Unauthorized' })
    }

    try {
      const formData = await request.formData()
      const email = formData.get('email') as string

      // Verify email matches current user
      if (email !== consoleUser.email) {
        return fail(400, { 
          error: 'Email must match your current account email',
          values: { email }
        })
      }

      const supabase = createSupabaseAdminClient()

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex')
      const expiresAt = new Date()
      expiresAt.setHours(expiresAt.getHours() + 1) // 1 hour expiry

      // Store reset token (we'll create this table)
      const { error: tokenError } = await supabase
        .from('console_password_resets')
        .insert({
          user_id: consoleUser.id,
          token: resetToken,
          expires_at: expiresAt.toISOString(),
          used: false
        })

      if (tokenError) {
        console.error('Error storing reset token:', tokenError)
        return fail(500, { 
          error: 'Failed to create password reset request',
          values: { email }
        })
      }

      // Send reset email
      try {
        await sendPasswordResetEmail({
          email: consoleUser.email,
          userName: `${consoleUser.first_name || ''} ${consoleUser.last_name || ''}`.trim() || consoleUser.email,
          resetToken,
          expiresAt: expiresAt.toISOString()
        })

        console.log(`Password reset email sent to ${email}`)
      } catch (emailError) {
        console.error('Failed to send reset email:', emailError)
        return fail(500, { 
          error: 'Failed to send password reset email',
          values: { email }
        })
      }

      return {
        success: true,
        message: `Password reset instructions sent to ${email}`
      }

    } catch (error) {
      console.error('Password reset error:', error)
      return fail(500, { error: 'Internal server error while processing reset request' })
    }
  }
}
