<script lang="ts">
  import { enhance } from '$app/forms'
  import { addToast } from '$stores/toast'
  import type { PageData, ActionData } from './$types'
  import { Key, Mail, ArrowLeft, Send } from 'lucide-svelte'
  import { goto } from '$app/navigation'

  export let data: PageData
  export let form: ActionData

  const { consoleUser } = data

  // Form state
  let email = consoleUser.email
  let loading = false

  // Handle form submission
  function handleSubmit() {
    loading = true
    return async ({ result, update }: { result: any; update: any }) => {
      loading = false
      
      if (result.type === 'success') {
        addToast({
          type: 'success',
          title: 'Reset Email Sent',
          message: form?.message || 'Password reset instructions have been sent'
        })
      } else if (result.type === 'failure') {
        addToast({
          type: 'error',
          title: 'Reset Failed',
          message: form?.error || 'Failed to send password reset email'
        })
      }
      
      await update()
    }
  }
</script>

<svelte:head>
  <title>Reset Password - Console</title>
</svelte:head>

<div class="max-w-md mx-auto space-y-6">
  
  <!-- Header -->
  <div class="text-center">
    <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
      <Key class="w-8 h-8 text-primary" />
    </div>
    <h1 class="text-2xl font-bold">Reset Your Password</h1>
    <p class="text-muted-foreground mt-2">
      We'll send you a secure link to reset your password
    </p>
  </div>

  <!-- Reset Form -->
  <form 
    method="POST" 
    action="?/requestReset" 
    use:enhance={handleSubmit}
    class="bg-card border rounded-lg p-6 space-y-4"
  >
    
    <!-- Email Field -->
    <div>
      <label for="email" class="block text-sm font-medium mb-2">
        Email Address
      </label>
      <div class="relative">
        <Mail class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
        <input
          id="email"
          type="email"
          name="email"
          bind:value={email}
          readonly
          class="w-full pl-10 pr-3 py-2 text-sm bg-muted border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
        />
      </div>
      <p class="text-xs text-muted-foreground mt-1">
        Password reset will be sent to your registered email address
      </p>
    </div>

    <!-- Submit Button -->
    <button
      type="submit"
      disabled={loading}
      class="w-full flex items-center justify-center px-4 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
    >
      {#if loading}
        <div class="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin mr-2"></div>
        Sending Reset Email...
      {:else}
        <Send class="w-4 h-4 mr-2" />
        Send Reset Instructions
      {/if}
    </button>

  </form>

  <!-- Back to Profile -->
  <div class="text-center">
    <button
      on:click={() => goto('/profile')}
      class="inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"
    >
      <ArrowLeft class="w-4 h-4 mr-1" />
      Back to Profile
    </button>
  </div>

  <!-- Security Notice -->
  <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
    <div class="text-sm">
      <p class="font-medium text-amber-900 dark:text-amber-200">Security Notice</p>
      <p class="text-amber-700 dark:text-amber-300 mt-1">
        The reset link will expire in 1 hour for security purposes. If you don't receive the email, check your spam folder or contact your system administrator.
      </p>
    </div>
  </div>

</div>
