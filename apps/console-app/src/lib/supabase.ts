import { create<PERSON>rowser<PERSON><PERSON>, createServer<PERSON>lient, isBrowser } from '@supabase/ssr'
import { PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY } from '$env/static/public'
import type { Database } from '$lib/types/database'

// App-specific session configuration for security isolation
const CONSOLE_APP_SESSION_PREFIX = 'psii-console'

// Browser client for client-side operations with app-specific session isolation
export const supabase = createBrowserClient<Database>(
  PUBLIC_SUPABASE_URL,
  PUBLIC_SUPABASE_ANON_KEY,
  {
    auth: {
      flowType: 'pkce', 
      persistSession: true,
      storage: {
        // App-specific session storage to prevent cross-app contamination
        getItem: (key: string) => {
          if (typeof window !== 'undefined') {
            return window.localStorage.getItem(`${CONSOLE_APP_SESSION_PREFIX}.${key}`)
          }
          return null
        },
        setItem: (key: string, value: string) => {
          if (typeof window !== 'undefined') {
            window.localStorage.setItem(`${CONSOLE_APP_SESSION_PREFIX}.${key}`, value)
          }
        },
        removeItem: (key: string) => {
          if (typeof window !== 'undefined') {
            window.localStorage.removeItem(`${CONSOLE_APP_SESSION_PREFIX}.${key}`)
          }
        }
      }
    }
  }
)

// Server client factory for server-side operations with session isolation
export function createSupabaseServerClient(cookies: {
  get: (name: string) => string | undefined
  set: (name: string, value: string, options?: any) => void
  remove: (name: string, options?: any) => void
}) {
  return createServerClient<Database>(
    PUBLIC_SUPABASE_URL,
    PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        get(name: string) {
          // Only read console app specific cookies
          const appSpecificName = name.startsWith('sb-') 
            ? name.replace('sb-', `sb-${CONSOLE_APP_SESSION_PREFIX}-`)
            : name
          return cookies.get(appSpecificName)
        },
        set(name: string, value: string, options?: any) {
          // Prefix cookie names to prevent cross-app interference
          const appSpecificName = name.startsWith('sb-') 
            ? name.replace('sb-', `sb-${CONSOLE_APP_SESSION_PREFIX}-`)
            : name
            
          cookies.set(appSpecificName, value, {
            ...options,
            path: '/',
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax'
          })
        },
        remove(name: string, options?: any) {
          const appSpecificName = name.startsWith('sb-') 
            ? name.replace('sb-', `sb-${CONSOLE_APP_SESSION_PREFIX}-`)
            : name
            
          cookies.remove(appSpecificName, {
            ...options,
            path: '/'
          })
        }
      }
    }
  )
}

// Enhanced load client with app-specific session management
export const createSupabaseLoadClient = (fetch: any, event?: any) => {
  if (isBrowser()) {
    return createBrowserClient<Database>(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
      global: { fetch },
      auth: {
        flowType: 'pkce',
        persistSession: true,
        storage: {
          getItem: (key: string) => {
            if (typeof window !== 'undefined') {
              return window.localStorage.getItem(`${CONSOLE_APP_SESSION_PREFIX}.${key}`)
            }
            return null
          },
          setItem: (key: string, value: string) => {
            if (typeof window !== 'undefined') {
              window.localStorage.setItem(`${CONSOLE_APP_SESSION_PREFIX}.${key}`, value)
            }
          },
          removeItem: (key: string) => {
            if (typeof window !== 'undefined') {
              window.localStorage.removeItem(`${CONSOLE_APP_SESSION_PREFIX}.${key}`)
            }
          }
        }
      }
    })
  }

  return createServerClient<Database>(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
    global: { fetch },
    cookies: {
      getAll() {
        const allCookies = event?.cookies.getAll() || []
        // Only return cookies for this app - console specific
        return allCookies.filter(cookie => 
          cookie.name.startsWith(`sb-${CONSOLE_APP_SESSION_PREFIX}-`) ||
          (cookie.name.startsWith('sb-') && !cookie.name.includes('customer') && !cookie.name.includes('candidate'))
        )
      },
      setAll(cookiesToSet) {
        cookiesToSet.forEach(({ name, value, options }) => {
          // Prefix cookie names to prevent cross-app interference
          const appSpecificName = name.startsWith('sb-') 
            ? name.replace('sb-', `sb-${CONSOLE_APP_SESSION_PREFIX}-`)
            : name
          
          event?.cookies.set(appSpecificName, value, { 
            ...options, 
            path: '/',
            sameSite: 'lax',
            secure: process.env.NODE_ENV === 'production',
            httpOnly: options?.httpOnly ?? true
          })
        })
      }
    }
  })
}

