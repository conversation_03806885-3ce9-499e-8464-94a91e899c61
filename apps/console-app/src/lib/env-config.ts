// Environment configuration helper for Console App
// This file provides secure access to environment variables

// Direct service role key import for console operations
const HARDCODED_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhmemh2cmtuamd3dHJrZ3lpbmpmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA2ODI4MiwiZXhwIjoyMDY1NjQ0MjgyfQ._scXNK1IgQuVok-iIBVxQYmcWntrI2VLInx1_Cz8knI';

// Get service role key from environment
export function getServiceRoleKey(): string {
  // Try multiple environment variable sources first
  const serviceRoleKey = 
    process.env.SUPABASE_SERVICE_ROLE_KEY ||
    process.env.PRIVATE_SUPABASE_SERVICE_ROLE_KEY ||
    process.env.SUPABASE_SERVICE_KEY ||
    HARDCODED_SERVICE_ROLE_KEY; // Fallback to hardcoded key for console app

  console.log('[ENV-CONFIG] Service role key source:', {
    fromEnv: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    fromPrivateEnv: !!process.env.PRIVATE_SUPABASE_SERVICE_ROLE_KEY,
    fromServiceKey: !!process.env.SUPABASE_SERVICE_KEY,
    usingHardcoded: !process.env.SUPABASE_SERVICE_ROLE_KEY && !process.env.PRIVATE_SUPABASE_SERVICE_ROLE_KEY && !process.env.SUPABASE_SERVICE_KEY,
    hasKey: !!serviceRoleKey
  });

  if (!serviceRoleKey) {
    console.error('[ENV-CONFIG] Service role key not found in any source');
    console.error('[ENV-CONFIG] Available env vars:', Object.keys(process.env).filter(key => key.includes('SUPABASE')));
    throw new Error('Service role key not configured');
  }

  return serviceRoleKey;
}

// Validate environment configuration
export function validateEnvironment(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check if we have some form of service role key
  const hasServiceRoleKey = !!(
    process.env.SUPABASE_SERVICE_ROLE_KEY || 
    process.env.PRIVATE_SUPABASE_SERVICE_ROLE_KEY ||
    process.env.SUPABASE_SERVICE_KEY ||
    HARDCODED_SERVICE_ROLE_KEY
  );

  if (!hasServiceRoleKey) {
    errors.push('Missing SUPABASE_SERVICE_ROLE_KEY');
  }

  if (!process.env.PUBLIC_SUPABASE_URL) {
    errors.push('Missing PUBLIC_SUPABASE_URL');
  }

  if (!process.env.PUBLIC_SUPABASE_ANON_KEY) {
    errors.push('Missing PUBLIC_SUPABASE_ANON_KEY');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}
