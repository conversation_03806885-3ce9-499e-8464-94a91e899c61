// Client-safe enum validation utilities
// This file contains only the data and types that can be safely imported on the client

// Predefined enum categories (client-safe)
export const ENUM_CATEGORIES = {
  work_authorization_types: {
    label: 'Work Authorization Types',
    description: 'Types of work authorization status for candidates',
    common_values: [
      { key: 'us_citizen', label: 'US Citizen', color: '#22c55e' },
      { key: 'green_card', label: 'Green Card', color: '#3b82f6' },
      { key: 'h1b', label: 'H1B Visa', color: '#f59e0b' },
      { key: 'opt', label: 'OPT', color: '#8b5cf6' },
      { key: 'tn_visa', label: 'TN Visa', color: '#06b6d4' }
    ]
  },
  job_statuses: {
    label: 'Job Statuses',
    description: 'Current status of job postings',
    common_values: [
      { key: 'draft', label: 'Draft', color: '#6b7280' },
      { key: 'open', label: 'Open', color: '#22c55e' },
      { key: 'in_progress', label: 'In Progress', color: '#f59e0b' },
      { key: 'on_hold', label: 'On Hold', color: '#ef4444' },
      { key: 'closed', label: 'Closed', color: '#374151' }
    ]
  },
  candidate_statuses: {
    label: 'Candidate Statuses',
    description: 'Current status of candidates in the system',
    common_values: [
      { key: 'new', label: 'New', color: '#3b82f6' },
      { key: 'active', label: 'Active', color: '#22c55e' },
      { key: 'interviewing', label: 'Interviewing', color: '#f59e0b' },
      { key: 'placed', label: 'Placed', color: '#10b981' },
      { key: 'inactive', label: 'Inactive', color: '#6b7280' }
    ]
  }
} as const
