<script lang="ts">
  import type { AdminEmailSelection } from '@psii/shared-types'
  import { User, Mail, Plus, Check } from 'lucide-svelte'
  import { createEventDispatcher } from 'svelte'

  export let company_id: string | undefined = undefined
  export let default_email: string = ''
  export let default_name: string = ''
  export let existing_admins: Array<{
    email: string
    name: string
    title?: string
  }> = []

  const dispatch = createEventDispatcher<{
    selection: AdminEmailSelection
  }>()

  let selection_mode: 'existing' | 'custom' = existing_admins.length > 0 ? 'existing' : 'custom'
  let selected_admin_index: number = -1
  let custom_email: string = default_email
  let custom_name: string = default_name
  let custom_title: string = ''

  // Validation
  $: email_valid = custom_email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(custom_email)
  $: name_valid = custom_name.trim().length >= 2
  $: can_proceed = selection_mode === 'existing' ? 
    selected_admin_index >= 0 : 
    email_valid && name_valid

  // Handle selection changes
  function handleSelectionChange() {
    if (!can_proceed) return

    let selection: AdminEmailSelection

    if (selection_mode === 'existing' && selected_admin_index >= 0) {
      const admin = existing_admins[selected_admin_index]
      selection = {
        email: admin.email,
        name: admin.name,
        title: admin.title,
        is_custom: false
      }
    } else {
      selection = {
        email: custom_email,
        name: custom_name,
        title: custom_title || undefined,
        is_custom: true
      }
    }

    dispatch('selection', selection)
  }

  // Auto-dispatch when selection becomes valid
  $: if (can_proceed) {
    handleSelectionChange()
  }
</script>

<div class="space-y-4">
  <div class="flex items-center gap-2 mb-3">
    <Mail class="w-5 h-5 text-gray-500" />
    <h3 class="text-sm font-medium text-gray-900 dark:text-white">
      Select Admin for Activation Email
    </h3>
  </div>

  <!-- Selection Mode Toggle -->
  {#if existing_admins.length > 0}
    <div class="flex gap-2 p-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
      <button
        type="button"
        on:click={() => { selection_mode = 'existing'; selected_admin_index = -1; }}
        class="flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors
               {selection_mode === 'existing' 
                 ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm' 
                 : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'}"
      >
        <User class="w-4 h-4 inline mr-2" />
        Existing Admins ({existing_admins.length})
      </button>
      
      <button
        type="button"
        on:click={() => { selection_mode = 'custom'; }}
        class="flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors
               {selection_mode === 'custom' 
                 ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm' 
                 : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'}"
      >
        <Plus class="w-4 h-4 inline mr-2" />
        Custom Email
      </button>
    </div>
  {/if}

  <!-- Existing Admins Selection -->
  {#if selection_mode === 'existing' && existing_admins.length > 0}
    <div class="space-y-2">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Choose existing admin:
      </label>
      
      <div class="space-y-2 max-h-48 overflow-y-auto">
        {#each existing_admins as admin, index}
          <button
            type="button"
            on:click={() => selected_admin_index = index}
            class="w-full p-3 text-left rounded-lg border transition-colors
                   {selected_admin_index === index
                     ? 'border-purple-300 bg-purple-50 dark:border-purple-600 dark:bg-purple-900/20'
                     : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                   }"
          >
            <div class="flex items-center justify-between">
              <div>
                <p class="font-medium text-gray-900 dark:text-white">{admin.name}</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">{admin.email}</p>
                {#if admin.title}
                  <p class="text-xs text-gray-400 dark:text-gray-500">{admin.title}</p>
                {/if}
              </div>
              
              {#if selected_admin_index === index}
                <Check class="w-5 h-5 text-purple-600" />
              {/if}
            </div>
          </button>
        {/each}
      </div>
    </div>
  {/if}

  <!-- Custom Email Input -->
  {#if selection_mode === 'custom'}
    <div class="space-y-3">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Enter custom admin details:
      </label>
      
      <div class="grid grid-cols-1 gap-3">
        <!-- Email Input -->
        <div>
          <label for="custom_email" class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
            Email Address *
          </label>
          <input
            type="email"
            id="custom_email"
            bind:value={custom_email}
            placeholder="<EMAIL>"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md 
                   bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                   focus:ring-2 focus:ring-purple-500 focus:border-purple-500
                   {!email_valid && custom_email ? 'border-red-300 dark:border-red-600' : ''}"
            required
          />
        </div>

        <!-- Name Input -->
        <div>
          <label for="custom_name" class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
            Full Name *
          </label>
          <input
            type="text"
            id="custom_name"
            bind:value={custom_name}
            placeholder="John Smith"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md 
                   bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                   focus:ring-2 focus:ring-purple-500 focus:border-purple-500
                   {!name_valid && custom_name ? 'border-red-300 dark:border-red-600' : ''}"
            required
          />
        </div>

        <!-- Title Input (Optional) -->
        <div>
          <label for="custom_title" class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
            Title / Position
          </label>
          <input
            type="text"
            id="custom_title"
            bind:value={custom_title}
            placeholder="CEO, HR Director, etc."
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md 
                   bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                   focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          />
        </div>
      </div>
    </div>
  {/if}

  <!-- Validation Feedback -->
  {#if !can_proceed}
    <div class="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
      <p class="text-sm text-yellow-800 dark:text-yellow-200">
        {#if selection_mode === 'existing'}
          Please select an admin from the list above.
        {:else}
          Please enter a valid email address and full name.
        {/if}
      </p>
    </div>
  {/if}

  <!-- Success Feedback -->
  {#if can_proceed}
    <div class="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
      <div class="flex items-center gap-2">
        <Check class="w-4 h-4 text-green-600" />
        <p class="text-sm text-green-800 dark:text-green-200">
          Activation email will be sent to: 
          <span class="font-medium">
            {selection_mode === 'existing' && selected_admin_index >= 0 
              ? existing_admins[selected_admin_index].email 
              : custom_email}
          </span>
        </p>
      </div>
    </div>
  {/if}
</div>
