<script lang="ts">
  import type { DomainDetectionResult } from '@psii/shared-types'
  import { Building2, Users, AlertCircle, CheckCircle } from 'lucide-svelte'

  export let detection_result: DomainDetectionResult
  export let domain: string
  export let size: 'sm' | 'md' | 'lg' = 'md'

  // Determine badge styling based on detection type
  $: badgeConfig = detection_result?.type === 'EXISTING_CUSTOMER' ? {
    icon: Users,
    label: 'Existing Customer',
    bgColor: 'bg-blue-100 dark:bg-blue-900',
    textColor: 'text-blue-700 dark:text-blue-300',
    borderColor: 'border-blue-200 dark:border-blue-700',
    company: detection_result.matched_company_name
  } : {
    icon: Building2,
    label: 'New Company',
    bgColor: 'bg-green-100 dark:bg-green-900',
    textColor: 'text-green-700 dark:text-green-300',
    borderColor: 'border-green-200 dark:border-green-700'
  }

  // Size configurations
  $: sizeConfig = {
    sm: {
      container: 'px-2 py-1 text-xs',
      icon: 'w-3 h-3',
      gap: 'gap-1'
    },
    md: {
      container: 'px-3 py-1.5 text-sm',
      icon: 'w-4 h-4', 
      gap: 'gap-2'
    },
    lg: {
      container: 'px-4 py-2 text-base',
      icon: 'w-5 h-5',
      gap: 'gap-2'
    }
  }[size]

  // Confidence indicator
  $: confidenceIcon = detection_result?.confidence_score >= 0.9 ? CheckCircle : AlertCircle
  $: confidenceColor = detection_result?.confidence_score >= 0.9 ? 'text-green-600' : 'text-yellow-600'
</script>

<div class="inline-flex items-center {sizeConfig.gap} {sizeConfig.container} font-medium 
           {badgeConfig.bgColor} {badgeConfig.textColor} {badgeConfig.borderColor} 
           border rounded-full">
  
  <!-- Detection Type Icon -->
  <svelte:component this={badgeConfig.icon} class="{sizeConfig.icon} flex-shrink-0" />
  
  <!-- Badge Text -->
  <span class="font-medium">{badgeConfig.label}</span>
  
  <!-- Domain Info -->
  <span class="opacity-75">({domain})</span>
  
  <!-- Confidence Indicator (for larger sizes) -->
  {#if size !== 'sm' && detection_result?.confidence_score}
    <svelte:component this={confidenceIcon} class="w-3 h-3 {confidenceColor}" />
  {/if}
</div>

<!-- Additional Info for Existing Customers -->
{#if detection_result?.type === 'EXISTING_CUSTOMER' && badgeConfig.company && size === 'lg'}
  <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
    Matches: <span class="font-medium">{badgeConfig.company}</span>
  </div>
{/if}
