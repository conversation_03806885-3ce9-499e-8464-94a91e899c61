// Import base database types
import type { Database as BaseDatabase } from '@psii/database-types'

// Console-specific table types
export interface ConsoleUser {
  id: string
  email: string
  role: 'super_admin' | 'company_admin' | 'company_manager'
  company_ids: string[] | null
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface ConsoleUserInvitation {
  id: string
  email: string
  role: 'super_admin' | 'company_admin' | 'company_manager'
  company_id: string | null
  invited_by: string
  token: string
  expires_at: string
  status: 'pending' | 'accepted' | 'expired'
  permissions: Record<string, string[]>
  created_at: string
}

export interface ConsoleRolePermission {
  id: string
  user_id: string
  resource: string
  action: string
  company_id: string | null
  created_at: string
}

export interface ConsoleSecurityEvent {
  id: string
  event_type: string
  user_id: string | null
  user_email: string | null
  user_role: string | null
  resource: string | null
  action: string | null
  success: boolean
  error_message: string | null
  ip_address: string | null
  user_agent: string | null
  timestamp: string
  metadata: Record<string, any> | null
}

// Extended database type that includes console-specific tables
export interface Database extends BaseDatabase {
  public: BaseDatabase['public'] & {
    Tables: BaseDatabase['public']['Tables'] & {
      console_users: {
        Row: ConsoleUser
        Insert: Omit<ConsoleUser, 'id' | 'created_at' | 'updated_at'> & {
          id?: string
          created_at?: string
          updated_at?: string
        }
        Update: Partial<Omit<ConsoleUser, 'id'>>
        Relationships: []
      }
      console_user_invitations: {
        Row: ConsoleUserInvitation
        Insert: Omit<ConsoleUserInvitation, 'id' | 'created_at'> & {
          id?: string
          created_at?: string
        }
        Update: Partial<Omit<ConsoleUserInvitation, 'id'>>
        Relationships: [
          {
            foreignKeyName: "console_user_invitations_invited_by_fkey"
            columns: ["invited_by"]
            isOneToOne: false
            referencedRelation: "console_users"
            referencedColumns: ["id"]
          }
        ]
      }
      console_role_permissions: {
        Row: ConsoleRolePermission
        Insert: Omit<ConsoleRolePermission, 'id' | 'created_at'> & {
          id?: string
          created_at?: string
        }
        Update: Partial<Omit<ConsoleRolePermission, 'id'>>
        Relationships: [
          {
            foreignKeyName: "console_role_permissions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "console_users"
            referencedColumns: ["id"]
          }
        ]
      }
      console_security_events: {
        Row: ConsoleSecurityEvent
        Insert: Omit<ConsoleSecurityEvent, 'id'> & {
          id?: string
        }
        Update: Partial<Omit<ConsoleSecurityEvent, 'id'>>
        Relationships: []
      }
    }
  }
}

// Export all the types that might be needed
export type {
  ConsoleUser,
  ConsoleUserInvitation,
  ConsoleRolePermission,
  ConsoleSecurityEvent
} 