// Application-wide enum caching utility for improved performance
// This provides cached access to enums for customer-app and candidate-app

import { createSupabaseAdminClient } from './supabase-admin'

// In-memory cache for application enums
let enumCache: Map<string, any> = new Map()
let cacheTimestamp: number = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Cache invalidation flag
let cacheInvalidated = false

export interface EnumValue {
  key: string
  label: string
  color?: string
  active?: boolean
  sort_order?: number
  subcategories?: EnumValue[]
}

export interface ApplicationEnum {
  id: string
  category: string
  display_name: string
  description?: string
  values: EnumValue[]
  is_system: boolean
  is_editable: boolean
  is_hierarchical: boolean
  version: number
  created_at: string
  updated_at: string
}

/**
 * Get all application enums with caching
 */
export async function getApplicationEnums(): Promise<ApplicationEnum[]> {
  const now = Date.now()
  
  // Check if cache is valid
  if (!cacheInvalidated && enumCache.size > 0 && (now - cacheTimestamp) < CACHE_DURATION) {
    return Array.from(enumCache.values())
  }

  try {
    const supabase = createSupabaseAdminClient()
    
    const { data: enums, error } = await supabase
      .from('application_enums')
      .select('*')
      .order('category')

    if (error) {
      console.error('Error fetching application enums:', error)
      throw new Error('Failed to fetch application enums')
    }

    // Update cache
    enumCache.clear()
    if (enums) {
      enums.forEach(enumItem => {
        enumCache.set(enumItem.category, enumItem)
      })
    }
    
    cacheTimestamp = now
    cacheInvalidated = false

    return enums || []

  } catch (error) {
    console.error('Error in getApplicationEnums:', error)
    
    // Return cached data if available, even if stale
    if (enumCache.size > 0) {
      console.warn('Returning stale enum cache due to fetch error')
      return Array.from(enumCache.values())
    }
    
    throw error
  }
}

/**
 * Get a specific enum by category with caching
 */
export async function getEnumByCategory(category: string): Promise<ApplicationEnum | null> {
  const now = Date.now()
  
  // Check cache first
  if (!cacheInvalidated && enumCache.has(category) && (now - cacheTimestamp) < CACHE_DURATION) {
    return enumCache.get(category)
  }

  try {
    // If cache is stale or doesn't have this category, refresh all enums
    await getApplicationEnums()
    return enumCache.get(category) || null

  } catch (error) {
    console.error(`Error getting enum for category ${category}:`, error)
    return null
  }
}

/**
 * Get enum values by category (most common use case)
 */
export async function getEnumValues(category: string): Promise<EnumValue[]> {
  try {
    const enumData = await getEnumByCategory(category)
    return enumData?.values || []
  } catch (error) {
    console.error(`Error getting enum values for ${category}:`, error)
    return []
  }
}

/**
 * Get active enum values only
 */
export async function getActiveEnumValues(category: string): Promise<EnumValue[]> {
  try {
    const values = await getEnumValues(category)
    return values.filter(value => value.active !== false)
  } catch (error) {
    console.error(`Error getting active enum values for ${category}:`, error)
    return []
  }
}

/**
 * Get enum values formatted for select dropdowns
 */
export async function getEnumOptions(category: string): Promise<Array<{value: string, label: string, color?: string}>> {
  try {
    const values = await getActiveEnumValues(category)
    return values.map(value => ({
      value: value.key,
      label: value.label,
      color: value.color
    }))
  } catch (error) {
    console.error(`Error getting enum options for ${category}:`, error)
    return []
  }
}

/**
 * Get hierarchical enum values (for work_authorization_types with EAD subcategories)
 */
export async function getHierarchicalEnumValues(category: string): Promise<EnumValue[]> {
  try {
    const enumData = await getEnumByCategory(category)
    if (!enumData?.is_hierarchical) {
      return enumData?.values || []
    }

    const flatValues: EnumValue[] = []
    
    enumData.values.forEach(value => {
      // Add main value
      flatValues.push(value)
      
      // Add subcategories if they exist
      if (value.subcategories && Array.isArray(value.subcategories)) {
        value.subcategories.forEach(subcategory => {
          flatValues.push({
            ...subcategory,
            label: `${value.label} - ${subcategory.label}`,
            key: `${value.key}_${subcategory.key}`
          })
        })
      }
    })
    
    return flatValues.filter(value => value.active !== false)

  } catch (error) {
    console.error(`Error getting hierarchical enum values for ${category}:`, error)
    return []
  }
}

/**
 * Get enum display name by category
 */
export async function getEnumDisplayName(category: string): Promise<string> {
  try {
    const enumData = await getEnumByCategory(category)
    return enumData?.display_name || category
  } catch (error) {
    console.error(`Error getting enum display name for ${category}:`, error)
    return category
  }
}

/**
 * Get enum value label by key
 */
export async function getEnumValueLabel(category: string, key: string): Promise<string> {
  try {
    const values = await getEnumValues(category)
    const value = values.find(v => v.key === key)
    return value?.label || key
  } catch (error) {
    console.error(`Error getting enum value label for ${category}.${key}:`, error)
    return key
  }
}

/**
 * Get enum value color by key
 */
export async function getEnumValueColor(category: string, key: string): Promise<string> {
  try {
    const values = await getEnumValues(category)
    const value = values.find(v => v.key === key)
    return value?.color || '#6b7280'
  } catch (error) {
    console.error(`Error getting enum value color for ${category}.${key}:`, error)
    return '#6b7280'
  }
}

/**
 * Invalidate cache (call this when enums are updated)
 */
export function invalidateEnumCache(): void {
  cacheInvalidated = true
  console.log('Enum cache invalidated')
}

/**
 * Clear cache completely
 */
export function clearEnumCache(): void {
  enumCache.clear()
  cacheTimestamp = 0
  cacheInvalidated = true
  console.log('Enum cache cleared')
}

/**
 * Get cache statistics for monitoring
 */
export function getEnumCacheStats() {
  return {
    size: enumCache.size,
    age: Date.now() - cacheTimestamp,
    maxAge: CACHE_DURATION,
    categories: Array.from(enumCache.keys()),
    isStale: (Date.now() - cacheTimestamp) > CACHE_DURATION,
    isInvalidated: cacheInvalidated
  }
}

/**
 * Preload commonly used enums for better performance
 */
export async function preloadCommonEnums(): Promise<void> {
  const commonCategories = [
    'job_statuses',
    'candidate_statuses', 
    'work_authorization_types',
    'employment_types',
    'skill_levels'
  ]

  try {
    await getApplicationEnums() // This will cache all enums
    console.log('Common enums preloaded successfully')
  } catch (error) {
    console.error('Error preloading common enums:', error)
  }
}
