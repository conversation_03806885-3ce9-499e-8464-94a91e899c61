import { createSupabaseAdminClient } from '$lib/server/supabase-admin'
import { PUBLIC_CONSOLE_APP_URL } from '$env/static/public'

export interface EmailInvitationData {
  email: string
  role: string
  inviterName: string
  token: string
  expiresAt: string
}

/**
 * Send console user invitation email using Supabase Auth
 * This leverages your existing Supabase SMTP configuration
 */
export async function sendInvitationEmail(data: EmailInvitationData) {
  const { email, role, inviterName, token, expiresAt } = data
  
  const supabase = createSupabaseAdminClient()
  const acceptUrl = `${PUBLIC_CONSOLE_APP_URL}/accept-invitation?token=${token}`
  
  console.log('Sending invitation email via Supabase Auth to:', email)
  console.log('Accept URL:', acceptUrl)

  try {
    // Use Supabase's invite user functionality
    // This will automatically send an email using your configured SMTP
    const { data: result, error } = await supabase.auth.admin.inviteUserByEmail(
      email,
      {
        redirectTo: acceptUrl,
        data: {
          role,
          invited_by: inviterName,
          console_access: true,
          invitation_token: token
        }
      }
    )

    if (error) {
      console.error('Failed to send invitation via Supabase:', error)
      throw new Error(`Supabase invitation failed: ${error.message}`)
    }

    console.log('Invitation sent successfully via Supabase:', result)
    return { success: true, messageId: result.user?.id }
  } catch (error) {
    console.error('Error sending invitation email via Supabase:', error)
    throw error
  }
}

/**
 * Send password reset email using Supabase Auth
 */
export async function sendPasswordResetEmail(data: { email: string }) {
  const supabase = createSupabaseAdminClient()
  
  try {
    const { error } = await supabase.auth.admin.generateLink({
      type: 'recovery',
      email: data.email,
      options: {
        redirectTo: `${PUBLIC_CONSOLE_APP_URL}/reset-password`
      }
    })

    if (error) {
      console.error('Failed to send password reset via Supabase:', error)
      throw new Error(`Password reset failed: ${error.message}`)
    }

    console.log('Password reset email sent successfully via Supabase')
    return { success: true }
  } catch (error) {
    console.error('Error sending password reset email via Supabase:', error)
    throw error
  }
}

/**
 * Test email configuration using Supabase
 */
export async function testEmailConfig() {
  const supabase = createSupabaseAdminClient()
  
  try {
    // Test by attempting to invite a test user
    const { data, error } = await supabase.auth.admin.inviteUserByEmail(
      '<EMAIL>',
      {
        redirectTo: `${PUBLIC_CONSOLE_APP_URL}/test`,
        data: { test: true }
      }
    )

    if (error) {
      throw new Error(`Supabase email test failed: ${error.message}`)
    }

    return { success: true, messageId: data.user?.id }
  } catch (error) {
    console.error('Supabase email configuration test failed:', error)
    throw error
  }
}
