import { createSupabaseAdminClient } from '../supabase-admin'
import { randomUUID } from 'crypto'
import type { ConsoleUser, Permission } from '$types/auth.types'

const DEBUG_SETUP = process.env.NODE_ENV === 'development'

function log(message: string, data?: any) {
  if (DEBUG_SETUP) {
    console.log(`[SETUP-MANAGER] ${message}`, data ? JSON.stringify(data, null, 2) : '')
  }
}

export interface SetupConfiguration {
  isSetupRequired: boolean
  isSetupEnabled: boolean
  setupToken: string | null
  adminEmail: string | null
}

export interface SetupRequest {
  email: string
  password: string
  token: string
}

export interface SetupResponse {
  success: boolean
  error?: string
  user?: ConsoleUser
}

export class SetupManager {
  private supabase = createSupabaseAdminClient()

  /**
   * Check if initial setup is required and enabled
   */
  async getSetupConfiguration(): Promise<SetupConfiguration> {
    try {
      log('Checking setup configuration')

      // Check if setup is enabled via environment
      const isSetupEnabled = process.env.ENABLE_INITIAL_SETUP === 'true'
      const setupToken = process.env.INITIAL_ADMIN_SETUP_TOKEN || null
      const adminEmail = process.env.INITIAL_ADMIN_EMAIL || null

      if (!isSetupEnabled || !setupToken || !adminEmail) {
        log('Setup is disabled or misconfigured', {
          isSetupEnabled,
          hasToken: !!setupToken,
          hasEmail: !!adminEmail
        })
        return {
          isSetupRequired: false,
          isSetupEnabled: false,
          setupToken: null,
          adminEmail: null
        }
      }

      // Check if any super admin exists
      const { data: existingAdmins, error } = await this.supabase
        .from('console_users')
        .select('id')
        .eq('role', 'super_admin')
        .eq('is_active', true)
        .limit(1)

      if (error) {
        log('Error checking for existing admins', error)
        throw error
      }

      const isSetupRequired = !existingAdmins || existingAdmins.length === 0

      log('Setup configuration determined', {
        isSetupRequired,
        isSetupEnabled,
        existingAdminCount: existingAdmins?.length || 0
      })

      return {
        isSetupRequired,
        isSetupEnabled,
        setupToken,
        adminEmail
      }

    } catch (error) {
      log('Error getting setup configuration', error)
      return {
        isSetupRequired: false,
        isSetupEnabled: false,
        setupToken: null,
        adminEmail: null
      }
    }
  }

  /**
   * Perform initial admin setup
   */
  async performInitialSetup(request: SetupRequest): Promise<SetupResponse> {
    try {
      log('Starting initial setup process', { email: request.email })

      // Get setup configuration
      const config = await this.getSetupConfiguration()

      // Validate setup is allowed
      if (!config.isSetupRequired || !config.isSetupEnabled) {
        return {
          success: false,
          error: 'Setup is not available or already completed'
        }
      }

      // Validate token
      if (request.token !== config.setupToken) {
        await this.logSecurityEvent({
          event_type: 'setup_failed',
          user_email: request.email,
          success: false,
          error_message: 'Invalid setup token',
          timestamp: new Date().toISOString()
        })

        return {
          success: false,
          error: 'Invalid setup token'
        }
      }

      // Validate email matches configuration
      if (request.email !== config.adminEmail) {
        await this.logSecurityEvent({
          event_type: 'setup_failed',
          user_email: request.email,
          success: false,
          error_message: 'Email does not match configuration',
          timestamp: new Date().toISOString()
        })

        return {
          success: false,
          error: 'Email does not match configuration'
        }
      }

      // Create user in Supabase Auth
      const { data: authData, error: authError } = await this.supabase.auth.admin.createUser({
        email: request.email,
        password: request.password,
        email_confirm: true,
        user_metadata: {
          setup_admin: true,
          created_via: 'initial_setup'
        }
      })

      if (authError || !authData.user) {
        log('Failed to create auth user', authError)
        return {
          success: false,
          error: 'Failed to create admin user'
        }
      }

      // Create console user record
      const consoleUserId = authData.user.id
      const { error: consoleUserError } = await this.supabase
        .from('console_users')
        .insert({
          id: consoleUserId,
          email: request.email,
          role: 'super_admin',
          company_ids: ['procureserve-internal'],
          is_active: true,
          mfa_enabled: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (consoleUserError) {
        log('Failed to create console user', consoleUserError)
        
        // Clean up auth user if console user creation failed
        await this.supabase.auth.admin.deleteUser(consoleUserId)
        
        return {
          success: false,
          error: 'Failed to create console user record'
        }
      }

      // Create super admin permissions
      const superAdminPermissions = await this.createSuperAdminPermissions(consoleUserId)
      
      if (!superAdminPermissions.success) {
        log('Failed to create super admin permissions')
        
        // Clean up on failure
        await this.supabase.auth.admin.deleteUser(consoleUserId)
        await this.supabase.from('console_users').delete().eq('id', consoleUserId)
        
        return {
          success: false,
          error: 'Failed to create admin permissions'
        }
      }

      // Log successful setup
      await this.logSecurityEvent({
        event_type: 'initial_setup_completed',
        user_id: consoleUserId,
        user_email: request.email,
        user_role: 'super_admin',
        success: true,
        metadata: {
          setup_timestamp: new Date().toISOString(),
          environment: process.env.NODE_ENV
        },
        timestamp: new Date().toISOString()
      })

      // Get the created user
      const { data: newUser } = await this.supabase
        .from('console_users')
        .select('*')
        .eq('id', consoleUserId)
        .single()

      const user: ConsoleUser = {
        id: newUser.id,
        email: newUser.email,
        role: newUser.role,
        company_ids: newUser.company_ids || [],
        permissions: superAdminPermissions.permissions || [],
        last_login: null,
        mfa_enabled: false,
        created_at: newUser.created_at,
        updated_at: newUser.updated_at,
        is_active: true
      }

      log('Initial setup completed successfully', { userId: consoleUserId })

      return {
        success: true,
        user
      }

    } catch (error) {
      log('Setup process failed', error)
      return {
        success: false,
        error: 'Setup process failed'
      }
    }
  }

  /**
   * Create super admin permissions
   */
  private async createSuperAdminPermissions(userId: string): Promise<{
    success: boolean
    permissions?: Permission[]
  }> {
    try {
      const permissions = [
        {
          user_id: userId,
          resource: 'console_users',
          actions: ['create', 'read', 'update', 'delete', 'invite', 'manage'],
          company_id: null
        },
        {
          user_id: userId,
          resource: 'companies',
          actions: ['create', 'read', 'update', 'delete', 'manage'],
          company_id: null
        },
        {
          user_id: userId,
          resource: 'enums',
          actions: ['create', 'read', 'update', 'delete', 'manage'],
          company_id: null
        },
        {
          user_id: userId,
          resource: 'settings',
          actions: ['read', 'update', 'manage'],
          company_id: null
        },
        {
          user_id: userId,
          resource: 'audit_logs',
          actions: ['read'],
          company_id: null
        },
        {
          user_id: userId,
          resource: 'analytics',
          actions: ['read'],
          company_id: null
        }
      ]

      const { error } = await this.supabase
        .from('console_user_permissions')
        .insert(permissions)

      if (error) {
        log('Failed to create super admin permissions', error)
        return { success: false }
      }

      const formattedPermissions: Permission[] = permissions.map(p => ({
        resource: p.resource,
        actions: p.actions as any,
        company_id: p.company_id
      }))

      return {
        success: true,
        permissions: formattedPermissions
      }

    } catch (error) {
      log('Error creating super admin permissions', error)
      return { success: false }
    }
  }

  /**
   * Log security events
   */
  private async logSecurityEvent(event: {
    event_type: string
    user_id?: string
    user_email?: string
    user_role?: string
    success: boolean
    error_message?: string
    metadata?: any
    timestamp: string
  }): Promise<void> {
    try {
      await this.supabase
        .from('console_security_events')
        .insert({
          id: randomUUID(),
          ...event
        })
    } catch (error) {
      console.error('Failed to log security event:', error)
    }
  }

  /**
   * Disable setup after completion
   */
  async disableSetup(): Promise<void> {
    // This would typically involve updating environment variables
    // For now, we'll log that setup should be disabled
    log('IMPORTANT: Disable setup by setting ENABLE_INITIAL_SETUP=false in environment')
  }
}