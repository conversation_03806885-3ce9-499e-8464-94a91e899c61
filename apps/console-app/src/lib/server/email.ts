import { Resend } from 'resend'
import { PUBLIC_CONSOLE_APP_URL } from '$env/static/public'

// Get environment variables directly from process.env
// This is more reliable in SvelteKit server context
const RESEND_API_KEY = process.env.RESEND_API_KEY || 're_8MBgcdYb_DaURr46BfLnvsymyY1jqxqjt'
const FROM_EMAIL = process.env.FROM_EMAIL || '<EMAIL>'

// Initialize Resend client
const resend = new Resend(RESEND_API_KEY)

// Debug logging
console.log('Email service initialization:')
console.log('RESEND_API_KEY:', RESEND_API_KEY ? 'Present' : 'Missing')
console.log('FROM_EMAIL:', FROM_EMAIL)
console.log('PUBLIC_CONSOLE_APP_URL:', PUBLIC_CONSOLE_APP_URL)

export interface EmailInvitationData {
  email: string
  role: string
  inviterName: string
  token: string
  expiresAt: string
}

export interface EmailPasswordResetData {
  email: string
  userName: string
  resetToken: string
  expiresAt: string
}

/**
 * Send console user invitation email
 */
export async function sendInvitationEmail(data: EmailInvitationData) {
  const { email, role, inviterName, token, expiresAt } = data
  
  // Use the correct activation route
  const acceptUrl = `${PUBLIC_CONSOLE_APP_URL}/activate-console?token=${token}`
  const expiryDate = new Date(expiresAt).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })

  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>ProcureServe Console Invitation</title>
    </head>
    <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #6366f1; margin: 0; font-size: 24px;">ProcureServe</h1>
        <p style="color: #6b7280; margin: 5px 0;">Internal Console Access</p>
      </div>

      <div style="background: #f8fafc; border-radius: 8px; padding: 30px; margin-bottom: 20px;">
        <h2 style="color: #1f2937; margin: 0 0 20px 0;">You're Invited to Join ProcureServe Console</h2>
        
        <p>Hello,</p>
        
        <p><strong>${inviterName}</strong> has invited you to join the ProcureServe internal console with the role of <strong>${role}</strong>.</p>
        
        <p>As a console user, you'll have access to:</p>
        <ul style="color: #4b5563;">
          <li>Client company management</li>
          <li>Platform administration tools</li>
          <li>Internal user management</li>
          <li>System configuration</li>
        </ul>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${acceptUrl}" style="background: #6366f1; color: white; padding: 12px 24px; border-radius: 6px; text-decoration: none; font-weight: 500; display: inline-block;">Accept Invitation</a>
        </div>

        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; padding: 15px; margin: 20px 0;">
          <p style="margin: 0; color: #92400e;"><strong>⚠️ Security Notice:</strong> This invitation is for ProcureServe internal staff only. Only accept if you are a verified ProcureServe employee.</p>
        </div>

        <p style="color: #6b7280; font-size: 14px; margin-top: 30px;">
          <strong>Important:</strong> This invitation expires on ${expiryDate}. If you don't accept it by then, you'll need to request a new invitation.
        </p>
      </div>

      <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; text-align: center;">
        <p style="color: #9ca3af; font-size: 12px; margin: 0;">
          This email was sent to ${email}. If you received this by mistake, please ignore it.
        </p>
        <p style="color: #9ca3af; font-size: 12px; margin: 5px 0 0 0;">
          © 2025 ProcureServe. All rights reserved.
        </p>
      </div>

    </body>
    </html>
  `

  try {
    const { data: result, error } = await resend.emails.send({
      from: `ProcureServe Console <${FROM_EMAIL}>`,
      to: [email],
      subject: `Invitation to join ProcureServe Console - ${role}`,
      html: htmlContent
    })

    if (error) {
      console.error('Failed to send invitation email:', error)
      throw new Error(`Email send failed: ${error.message}`)
    }

    console.log('Invitation email sent successfully:', result)
    return { success: true, messageId: result?.id }
  } catch (error) {
    console.error('Error sending invitation email:', error)
    throw error
  }
}

/**
 * Send password reset email for console users
 */
export async function sendPasswordResetEmail(data: EmailPasswordResetData) {
  const { email, userName, resetToken, expiresAt } = data
  
  const resetUrl = `${PUBLIC_CONSOLE_APP_URL}/reset-password?token=${resetToken}`
  const expiryDate = new Date(expiresAt).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })

  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>ProcureServe Console Password Reset</title>
    </head>
    <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #6366f1; margin: 0; font-size: 24px;">ProcureServe</h1>
        <p style="color: #6b7280; margin: 5px 0;">Internal Console Access</p>
      </div>

      <div style="background: #f8fafc; border-radius: 8px; padding: 30px; margin-bottom: 20px;">
        <h2 style="color: #1f2937; margin: 0 0 20px 0;">Password Reset Request</h2>
        
        <p>Hello ${userName},</p>
        
        <p>We received a request to reset your password for the ProcureServe console.</p>
        
        <p>If you made this request, click the button below to reset your password:</p>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}" style="background: #dc2626; color: white; padding: 12px 24px; border-radius: 6px; text-decoration: none; font-weight: 500; display: inline-block;">Reset Password</a>
        </div>

        <div style="background: #fef2f2; border: 1px solid #f87171; border-radius: 6px; padding: 15px; margin: 20px 0;">
          <p style="margin: 0; color: #991b1b;"><strong>⚠️ Security Alert:</strong> If you didn't request this password reset, please ignore this email or contact your system administrator immediately.</p>
        </div>

        <p style="color: #6b7280; font-size: 14px; margin-top: 30px;">
          <strong>Important:</strong> This reset link expires on ${expiryDate}. After that, you'll need to request a new password reset.
        </p>
      </div>

      <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; text-align: center;">
        <p style="color: #9ca3af; font-size: 12px; margin: 0;">
          This email was sent to ${email}. If you received this by mistake, please ignore it.
        </p>
        <p style="color: #9ca3af; font-size: 12px; margin: 5px 0 0 0;">
          © 2025 ProcureServe. All rights reserved.
        </p>
      </div>

    </body>
    </html>
  `

  try {
    const { data: result, error } = await resend.emails.send({
      from: `ProcureServe Security <${FROM_EMAIL}>`,
      to: [email],
      subject: 'Password Reset Request - ProcureServe Console',
      html: htmlContent
    })

    if (error) {
      console.error('Failed to send password reset email:', error)
      throw new Error(`Email send failed: ${error.message}`)
    }

    console.log('Password reset email sent successfully:', result)
    return { success: true, messageId: result?.id }
  } catch (error) {
    console.error('Error sending password reset email:', error)
    throw error
  }
}

/**
 * Test email configuration
 */
export async function testEmailConfig() {
  try {
    const { data, error } = await resend.emails.send({
      from: `ProcureServe Test <${FROM_EMAIL}>`,
      to: ['<EMAIL>'],
      subject: 'Email Configuration Test',
      html: '<p>Email configuration is working correctly!</p>'
    })

    if (error) {
      throw new Error(`Test email failed: ${error.message}`)
    }

    return { success: true, messageId: data?.id }
  } catch (error) {
    console.error('Email configuration test failed:', error)
    throw error
  }
}
