// Domain Detection System for Business Registration Email Routing
// Determines whether registration is for NEW_COMPANY or EXISTING_CUSTOMER

import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { getServiceRoleKey } from '$lib/env-config';

export interface DomainDetectionResult {
  type: 'NEW_COMPANY' | 'EXISTING_CUSTOMER';
  company_id?: string;
  company_name?: string;
  admin_emails?: string[];
  confidence_score: number;
  detection_timestamp: string;
}

export interface BusinessRegistration {
  id: string;
  contact_person_email: string;
  contact_person_name: string;
  company_name: string;
  company_domain: string;
  status: string;
  activated_company_id?: string;
}

/**
 * Extract domain from email address
 */
export function extractEmailDomain(email: string): string {
  const domain = email.split('@')[1];
  if (!domain) {
    throw new Error(`Invalid email format: ${email}`);
  }
  return domain.toLowerCase();
}

/**
 * Detect if registration domain matches existing company
 */
export async function detectDomainType(
  registrationEmail: string
): Promise<DomainDetectionResult> {
  const adminClient = createClient(PUBLIC_SUPABASE_URL, getServiceRoleKey(), {
    auth: { autoRefreshToken: false, persistSession: false }
  });

  try {
    // Extract domain from registration email
    const domain = extractEmailDomain(registrationEmail);
    
    console.log(`[DOMAIN_DETECTION] Analyzing domain: ${domain}`);

    // Check if domain exists in approved companies
    const { data: companies, error } = await adminClient
      .from('companies')
      .select('id, name, domain, primary_contact')
      .eq('domain', domain)
      .eq('registration_status', 'approved');

    if (error) {
      console.error('[DOMAIN_DETECTION] Database error:', error);
      throw error;
    }

    if (companies && companies.length > 0) {
      // EXISTING_CUSTOMER: Domain matches approved company
      const company = companies[0];
      
      console.log(`[DOMAIN_DETECTION] Found existing company: ${company.name}`);

      // Get admin emails for this company
      const { data: users, error: usersError } = await adminClient
        .from('users')
        .select('email, profile')
        .eq('company_id', company.id)
        .eq('role', 'admin')
        .eq('is_active', true);

      const adminEmails = users?.map(u => u.email) || [];
      
      // Add primary contact email if available
      if (company.primary_contact?.email) {
        adminEmails.push(company.primary_contact.email);
      }

      return {
        type: 'EXISTING_CUSTOMER',
        company_id: company.id,
        company_name: company.name,
        admin_emails: [...new Set(adminEmails)], // Remove duplicates
        confidence_score: 1.0, // Exact domain match = 100% confidence
        detection_timestamp: new Date().toISOString()
      };
    } else {
      // NEW_COMPANY: Domain not found in existing companies
      console.log(`[DOMAIN_DETECTION] New company domain: ${domain}`);
      
      return {
        type: 'NEW_COMPANY',
        confidence_score: 1.0, // No match = 100% confidence it's new
        detection_timestamp: new Date().toISOString()
      };
    }
  } catch (error) {
    console.error('[DOMAIN_DETECTION] Error during detection:', error);
    
    // Default to NEW_COMPANY on error for safety
    return {
      type: 'NEW_COMPANY',
      confidence_score: 0.5, // Low confidence due to error
      detection_timestamp: new Date().toISOString()
    };
  }
}

/**
 * Get console admin emails from environment/config
 */
export function getConsoleAdminEmails(): string[] {
  const consoleEmails = [
    process.env.CONSOLE_ADMIN_EMAIL || '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  return consoleEmails.filter(email => email && email.includes('@'));
}

/**
 * Determine email routing based on domain detection
 */
export async function determineEmailRouting(
  registration: BusinessRegistration
): Promise<{
  routing_type: 'CONSOLE_ADMIN' | 'COMPANY_ADMIN';
  recipient_emails: string[];
  detection_result: DomainDetectionResult;
}> {
  console.log(`[EMAIL_ROUTING] Determining routing for: ${registration.contact_person_email}`);

  const detectionResult = await detectDomainType(registration.contact_person_email);
  
  if (detectionResult.type === 'NEW_COMPANY') {
    // Route to console admins for new company approval
    const consoleEmails = getConsoleAdminEmails();
    
    return {
      routing_type: 'CONSOLE_ADMIN',
      recipient_emails: consoleEmails,
      detection_result: detectionResult
    };
  } else {
    // Route to company admins for existing customer approval
    const companyEmails = detectionResult.admin_emails || [];
    
    // If no company admin emails found, fallback to console admins
    if (companyEmails.length === 0) {
      console.warn(`[EMAIL_ROUTING] No admin emails found for company ${detectionResult.company_name}, routing to console`);
      
      return {
        routing_type: 'CONSOLE_ADMIN',
        recipient_emails: getConsoleAdminEmails(),
        detection_result: detectionResult
      };
    }
    
    return {
      routing_type: 'COMPANY_ADMIN',
      recipient_emails: companyEmails,
      detection_result: detectionResult
    };
  }
}
