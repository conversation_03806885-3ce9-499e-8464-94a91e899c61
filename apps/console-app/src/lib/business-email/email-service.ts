// Fixed Business Registration Email Service
// Handles all email notifications using direct Resend integration

import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { getServiceRoleKey } from '$lib/env-config';
import type { BusinessRegistration } from './domain-detection';

interface EmailSendResult {
  success: boolean;
  message_id?: string;
  error?: string;
  provider_used?: string;
}

export class BusinessRegistrationEmailService {
  private supabaseClient;

  constructor() {
    this.supabaseClient = createClient(
      PUBLIC_SUPABASE_URL,
      getServiceRoleKey(),
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
  }

  /**
   * Send registration confirmation to applicant
   */
  async sendRegistrationSubmitted(registration: BusinessRegistration): Promise<EmailSendResult> {
    console.log('[EMAIL] Sending registration confirmation to:', registration.contact_person_email);
    
    const emailData = {
      from: `${process.env.EMAIL_FROM_NAME || 'ProcureServe Team'} <${process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'}>`,
      to: [registration.contact_person_email],
      subject: '✅ Registration Received - ProcureServe',
      html: this.buildRegistrationSubmittedHtml(registration),
      text: this.buildRegistrationSubmittedText(registration)
    };

    return await this.sendWithResend(emailData);
  }

  /**
   * Send approval email with activation link
   */
  async sendApprovalEmail(registration: BusinessRegistration, activationUrl: string): Promise<EmailSendResult> {
    console.log('[EMAIL] Sending approval email to:', registration.contact_person_email);
    console.log('[EMAIL] Activation URL:', activationUrl);
    
    const emailData = {
      from: `${process.env.EMAIL_FROM_NAME || 'ProcureServe Team'} <${process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'}>`,
      to: [registration.contact_person_email],
      subject: '🎉 Registration Approved - Activate Your Account',
      html: this.buildApprovalEmailHtml(registration, activationUrl),
      text: this.buildApprovalEmailText(registration, activationUrl)
    };

    return await this.sendWithResend(emailData);
  }

  /**
   * Send rejection email with feedback
   */
  async sendRegistrationRejected(registration: BusinessRegistration, reason: string): Promise<EmailSendResult> {
    console.log('[EMAIL] Sending rejection email to:', registration.contact_person_email);
    
    const emailData = {
      from: `${process.env.EMAIL_FROM_NAME || 'ProcureServe Team'} <${process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'}>`,
      to: [registration.contact_person_email],
      subject: '📋 Registration Update - ProcureServe',
      html: this.buildRejectionEmailHtml(registration, reason),
      text: this.buildRejectionEmailText(registration, reason)
    };

    return await this.sendWithResend(emailData);
  }

  /**
   * Core email sending via Resend
   */
  private async sendWithResend(emailData: any): Promise<EmailSendResult> {
    if (!process.env.RESEND_API_KEY) {
      console.error('[EMAIL] RESEND_API_KEY not configured');
      return {
        success: false,
        error: 'Email service not configured',
        provider_used: 'none'
      };
    }

    try {
      const { Resend } = await import('resend');
      const resend = new Resend(process.env.RESEND_API_KEY);

      console.log('[EMAIL] Sending via Resend...');
      console.log('[EMAIL] From:', emailData.from);
      console.log('[EMAIL] To:', emailData.to);
      console.log('[EMAIL] Subject:', emailData.subject);

      const result = await resend.emails.send(emailData);

      if (result.data) {
        console.log('[EMAIL] ✅ Email sent successfully!');
        console.log('[EMAIL] Message ID:', result.data.id);
        
        // Log to database for audit
        await this.logEmailActivity(emailData, result.data.id, true);
        
        return {
          success: true,
          message_id: result.data.id,
          provider_used: 'resend'
        };
      } else {
        console.error('[EMAIL] ❌ Email send failed:', result.error);
        
        // Log failure to database
        await this.logEmailActivity(emailData, null, false, result.error?.message);
        
        return {
          success: false,
          error: result.error?.message || 'Unknown Resend error',
          provider_used: 'resend'
        };
      }

    } catch (error) {
      console.error('[EMAIL] ❌ Resend integration error:', error);
      
      // Log error to database
      await this.logEmailActivity(emailData, null, false, error instanceof Error ? error.message : 'Unknown error');
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider_used: 'resend'
      };
    }
  }

  /**
   * Build HTML for registration confirmation email
   */
  private buildRegistrationSubmittedHtml(registration: BusinessRegistration): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2563eb; margin: 0;">ProcureServe</h1>
          <p style="color: #64748b; margin: 5px 0;">Registration Received</p>
        </div>
        
        <div style="background: #dbeafe; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2563eb;">
          <h2 style="color: #1e40af; margin-top: 0;">✅ Thank You for Your Interest!</h2>
          <p style="color: #1e40af; line-height: 1.6;">
            We've received your business registration for <strong>${registration.company_name}</strong>.
          </p>
        </div>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h3 style="color: #1e293b; margin-top: 0;">What Happens Next?</h3>
          <ul style="color: #475569; line-height: 1.8; margin: 0; padding-left: 20px;">
            <li>Our team will review your application within 2-3 business days</li>
            <li>We'll verify your company information and requirements</li>
            <li>You'll receive an email with next steps and account activation</li>
            <li>Our support team may contact you for additional information</li>
          </ul>
        </div>
        
        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h3 style="color: #92400e; margin-top: 0;">⏰ Quick Response Guaranteed</h3>
          <p style="color: #92400e; line-height: 1.6; margin: 0;">
            We typically approve qualified applications within 24 hours during business days.
          </p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
          <p style="color: #64748b; font-size: 14px;">
            Questions? Contact us at 
            <a href="mailto:${process.env.EMAIL_REPLY_TO || '<EMAIL>'}" style="color: #2563eb;">
              ${process.env.EMAIL_REPLY_TO || '<EMAIL>'}
            </a>
          </p>
        </div>
      </div>
    `;
  }

  /**
   * Build text for registration confirmation email
   */
  private buildRegistrationSubmittedText(registration: BusinessRegistration): string {
    return `
      ProcureServe - Registration Received
      
      Thank you for your interest!
      
      We've received your business registration for ${registration.company_name}.
      
      What Happens Next?
      - Our team will review your application within 2-3 business days
      - We'll verify your company information and requirements
      - You'll receive an email with next steps and account activation
      - Our support team may contact you for additional information
      
      We typically approve qualified applications within 24 hours during business days.
      
      Questions? Contact us at ${process.env.EMAIL_REPLY_TO || '<EMAIL>'}
    `;
  }

  /**
   * Build HTML for approval email
   */
  private buildApprovalEmailHtml(registration: BusinessRegistration, activationUrl: string): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2563eb; margin: 0;">ProcureServe</h1>
          <p style="color: #64748b; margin: 5px 0;">Registration Approved</p>
        </div>
        
        <div style="background: #dcfce7; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #16a34a;">
          <h2 style="color: #15803d; margin-top: 0;">🎉 Welcome to ProcureServe!</h2>
          <p style="color: #15803d; line-height: 1.6;">
            Congratulations! Your business registration for <strong>${registration.company_name}</strong> has been approved.
          </p>
        </div>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h3 style="color: #1e293b; margin-top: 0;">Next Steps</h3>
          <ol style="color: #475569; line-height: 1.8; margin: 0; padding-left: 20px;">
            <li>Click the activation button below to set up your account</li>
            <li>Create a secure password for your account</li>
            <li>Complete your company profile setup</li>
            <li>Start posting jobs or managing candidates</li>
          </ol>
        </div>
        
        <div style="text-align: center; margin: 25px 0;">
          <a href="${activationUrl}" 
             style="background: #16a34a; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; font-size: 16px;">
            Activate Your Account
          </a>
        </div>
        
        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h3 style="color: #92400e; margin-top: 0;">⏰ Important</h3>
          <p style="color: #92400e; line-height: 1.6; margin: 0;">
            This activation link expires in 7 days. Please activate your account soon to avoid delays.
          </p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
          <p style="color: #64748b; font-size: 14px;">
            Need help? Contact us at 
            <a href="mailto:${process.env.EMAIL_REPLY_TO || '<EMAIL>'}" style="color: #2563eb;">
              ${process.env.EMAIL_REPLY_TO || '<EMAIL>'}
            </a>
          </p>
        </div>
      </div>
    `;
  }

  /**
   * Build text for approval email
   */
  private buildApprovalEmailText(registration: BusinessRegistration, activationUrl: string): string {
    return `
      ProcureServe - Registration Approved
      
      Welcome to ProcureServe!
      
      Congratulations! Your business registration for ${registration.company_name} has been approved.
      
      Next Steps:
      1. Click the activation link below to set up your account
      2. Create a secure password for your account
      3. Complete your company profile setup
      4. Start posting jobs or managing candidates
      
      Activate Your Account: ${activationUrl}
      
      Important: This activation link expires in 7 days. Please activate your account soon to avoid delays.
      
      Need help? Contact us at ${process.env.EMAIL_REPLY_TO || '<EMAIL>'}
    `;
  }

  /**
   * Build HTML for rejection email
   */
  private buildRejectionEmailHtml(registration: BusinessRegistration, reason: string): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2563eb; margin: 0;">ProcureServe</h1>
          <p style="color: #64748b; margin: 5px 0;">Registration Update</p>
        </div>
        
        <div style="background: #fef2f2; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #ef4444;">
          <h2 style="color: #dc2626; margin-top: 0;">📋 Registration Status Update</h2>
          <p style="color: #dc2626; line-height: 1.6;">
            Thank you for your interest in ProcureServe. After reviewing your application for <strong>${registration.company_name}</strong>, we're unable to approve it at this time.
          </p>
        </div>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h3 style="color: #1e293b; margin-top: 0;">Feedback</h3>
          <p style="color: #475569; line-height: 1.6; margin: 0;">
            ${reason}
          </p>
        </div>
        
        <div style="background: #dbeafe; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h3 style="color: #1e40af; margin-top: 0;">💡 What You Can Do</h3>
          <ul style="color: #1e40af; line-height: 1.8; margin: 0; padding-left: 20px;">
            <li>Address the feedback provided above</li>
            <li>Resubmit your application with additional information</li>
            <li>Contact our support team for clarification</li>
            <li>Consider alternative partnership opportunities</li>
          </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
          <p style="color: #64748b; font-size: 14px;">
            Questions? We're here to help: 
            <a href="mailto:${process.env.EMAIL_REPLY_TO || '<EMAIL>'}" style="color: #2563eb;">
              ${process.env.EMAIL_REPLY_TO || '<EMAIL>'}
            </a>
          </p>
        </div>
      </div>
    `;
  }

  /**
   * Build text for rejection email
   */
  private buildRejectionEmailText(registration: BusinessRegistration, reason: string): string {
    return `
      ProcureServe - Registration Update
      
      Thank you for your interest in ProcureServe. After reviewing your application for ${registration.company_name}, we're unable to approve it at this time.
      
      Feedback:
      ${reason}
      
      What You Can Do:
      - Address the feedback provided above
      - Resubmit your application with additional information
      - Contact our support team for clarification
      - Consider alternative partnership opportunities
      
      Questions? We're here to help: ${process.env.EMAIL_REPLY_TO || '<EMAIL>'}
    `;
  }

  /**
   * Log email activity for audit purposes
   */
  private async logEmailActivity(
    emailData: any,
    messageId: string | null,
    success: boolean,
    error?: string
  ): Promise<void> {
    try {
      // Create email_logs table if it doesn't exist
      await this.supabaseClient
        .from('email_logs')
        .insert({
          recipient: emailData.to[0],
          subject: emailData.subject,
          success: success,
          message_id: messageId,
          provider_used: 'resend',
          error_message: error,
          sent_at: new Date().toISOString()
        });
    } catch (logError) {
      console.error('[EMAIL] Failed to log email activity:', logError);
      // Don't throw error for logging failures
    }
  }
}

// Export singleton instance
export const businessEmailService = new BusinessRegistrationEmailService();
