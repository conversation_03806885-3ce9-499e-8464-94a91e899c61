// Business Registration Escalation Service
// Handles automatic escalation of registration requests

import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { getServiceRoleKey } from '$lib/env-config';
import { businessEmailService } from './email-service';
import type { BusinessRegistration, DomainDetectionResult } from './domain-detection';

export interface EscalationConfig {
  company_admin_timeout_days: number; // Default: 3 days
  auto_deny_timeout_days: number;     // Default: 14 days
  reminder_intervals: number[];       // Days to send reminders [1, 2]
}

export interface EscalationStatus {
  registration_id: string;
  status: 'pending' | 'escalated' | 'auto_denied' | 'approved' | 'rejected';
  company_admin_notified_at?: string;
  console_escalated_at?: string;
  auto_denied_at?: string;
  reminders_sent: number;
  last_reminder_at?: string;
  days_since_submission: number;
  action_required_by: 'company_admin' | 'console_admin' | 'none';
}

/**
 * Escalation Service for Business Registration Workflow
 * Handles automatic escalation and reminder logic
 */
export class BusinessRegistrationEscalationService {
  private supabaseClient;
  private config: EscalationConfig;

  constructor(config?: Partial<EscalationConfig>) {
    this.supabaseClient = createClient(
      PUBLIC_SUPABASE_URL,
      getServiceRoleKey(),
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    this.config = {
      company_admin_timeout_days: 3,
      auto_deny_timeout_days: 14,
      reminder_intervals: [1, 2],
      ...config
    };
  }

  /**
   * Check all registrations and process escalations
   */
  async processEscalations(): Promise<{
    processed: number;
    escalated: number;
    auto_denied: number;
    reminders_sent: number;
  }> {
    console.log('[ESCALATION] Starting escalation processing...');
    
    const pendingRegistrations = await this.getPendingRegistrations();
    console.log(`[ESCALATION] Found ${pendingRegistrations.length} pending registrations`);

    let escalated = 0;
    let auto_denied = 0;
    let reminders_sent = 0;

    for (const registration of pendingRegistrations) {
      const status = this.calculateEscalationStatus(registration);
      console.log(`[ESCALATION] Registration ${registration.id}: ${status.status}, ${status.days_since_submission} days old`);

      try {
        if (status.status === 'pending' && status.action_required_by === 'company_admin') {
          // Check if reminders need to be sent
          if (this.shouldSendReminder(registration, status)) {
            await this.sendReminderEmail(registration);
            reminders_sent++;
          }

          // Check if escalation to console is needed
          if (status.days_since_submission >= this.config.company_admin_timeout_days) {
            await this.escalateToConsole(registration);
            escalated++;
          }
        } else if (status.status === 'escalated' && status.days_since_submission >= this.config.auto_deny_timeout_days) {
          // Auto-deny after total timeout
          await this.autoDenyRegistration(registration);
          auto_denied++;
        }
      } catch (error) {
        console.error(`[ESCALATION] Error processing registration ${registration.id}:`, error);
      }
    }

    console.log(`[ESCALATION] Processing complete: ${escalated} escalated, ${auto_denied} auto-denied, ${reminders_sent} reminders sent`);
    
    return {
      processed: pendingRegistrations.length,
      escalated,
      auto_denied,
      reminders_sent
    };
  }

  /**
   * Get escalation status for a specific registration
   */
  async getRegistrationEscalationStatus(registrationId: string): Promise<EscalationStatus | null> {
    const { data: registration } = await this.supabaseClient
      .from('business_registrations')
      .select('*')
      .eq('id', registrationId)
      .single();

    if (!registration) {
      return null;
    }

    return this.calculateEscalationStatus(registration);
  }

  /**
   * Manually escalate a registration to console
   */
  async manualEscalation(registrationId: string, reason: string): Promise<boolean> {
    const { data: registration } = await this.supabaseClient
      .from('business_registrations')
      .select('*')
      .eq('id', registrationId)
      .single();

    if (!registration) {
      throw new Error('Registration not found');
    }

    return await this.escalateToConsole(registration, reason);
  }

  /**
   * Get all pending registrations
   */
  private async getPendingRegistrations(): Promise<BusinessRegistration[]> {
    const { data, error } = await this.supabaseClient
      .from('business_registrations')
      .select('*')
      .eq('status', 'pending_review')
      .order('created_at', { ascending: true });

    if (error) {
      console.error('[ESCALATION] Error fetching pending registrations:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Calculate the current escalation status for a registration
   */
  private calculateEscalationStatus(registration: BusinessRegistration): EscalationStatus {
    const now = new Date();
    const submittedAt = new Date(registration.created_at);
    const daysSinceSubmission = Math.floor((now.getTime() - submittedAt.getTime()) / (1000 * 60 * 60 * 24));

    // Check if it's been escalated to console
    const isEscalated = !!registration.console_escalated_at;
    const isAutoDenied = !!registration.auto_denied_at;

    // Determine action required by
    let actionRequiredBy: 'company_admin' | 'console_admin' | 'none' = 'none';
    let status: EscalationStatus['status'] = 'pending';

    if (isAutoDenied) {
      status = 'auto_denied';
    } else if (registration.status === 'approved') {
      status = 'approved';
    } else if (registration.status === 'rejected') {
      status = 'rejected';
    } else if (isEscalated) {
      status = 'escalated';
      actionRequiredBy = 'console_admin';
    } else if (registration.domain_detection_result?.type === 'EXISTING_CUSTOMER') {
      actionRequiredBy = 'company_admin';
    } else {
      actionRequiredBy = 'console_admin';
    }

    return {
      registration_id: registration.id,
      status,
      company_admin_notified_at: registration.company_admin_notified_at,
      console_escalated_at: registration.console_escalated_at,
      auto_denied_at: registration.auto_denied_at,
      reminders_sent: registration.reminders_sent || 0,
      last_reminder_at: registration.last_reminder_at,
      days_since_submission: daysSinceSubmission,
      action_required_by: actionRequiredBy
    };
  }

  /**
   * Check if a reminder should be sent
   */
  private shouldSendReminder(registration: BusinessRegistration, status: EscalationStatus): boolean {
    // Only send reminders for EXISTING_CUSTOMER registrations
    if (registration.domain_detection_result?.type !== 'EXISTING_CUSTOMER') {
      return false;
    }

    // Check if it's time for the next reminder
    const nextReminderDay = this.config.reminder_intervals[status.reminders_sent];
    if (!nextReminderDay || status.days_since_submission < nextReminderDay) {
      return false;
    }

    // Check if we already sent a reminder today
    if (status.last_reminder_at) {
      const lastReminder = new Date(status.last_reminder_at);
      const today = new Date();
      const isSameDay = lastReminder.toDateString() === today.toDateString();
      if (isSameDay) {
        return false;
      }
    }

    return true;
  }

  /**
   * Send reminder email to company admins
   */
  private async sendReminderEmail(registration: BusinessRegistration): Promise<void> {
    console.log(`[ESCALATION] Sending reminder email for registration ${registration.id}`);

    try {
      // Get company admin emails (assuming this is stored or can be derived)
      const companyAdminEmails = ['<EMAIL>']; // TODO: Get actual admin emails

      if (companyAdminEmails.length === 0) {
        console.warn(`[ESCALATION] No company admin emails found for registration ${registration.id}`);
        return;
      }

      // Send reminder email using the business email service
      const detectionResult: DomainDetectionResult = registration.domain_detection_result || {
        type: 'EXISTING_CUSTOMER',
        confidence_score: 1.0,
        detection_timestamp: new Date().toISOString()
      };

      await businessEmailService.sendCompanyAdminNotification(
        registration,
        detectionResult,
        companyAdminEmails
      );

      // Update reminder tracking
      await this.supabaseClient
        .from('business_registrations')
        .update({
          reminders_sent: (registration.reminders_sent || 0) + 1,
          last_reminder_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', registration.id);

      console.log(`[ESCALATION] Reminder sent successfully for registration ${registration.id}`);
    } catch (error) {
      console.error(`[ESCALATION] Failed to send reminder for registration ${registration.id}:`, error);
      throw error;
    }
  }

  /**
   * Escalate registration to console admins
   */
  private async escalateToConsole(registration: BusinessRegistration, reason?: string): Promise<boolean> {
    console.log(`[ESCALATION] Escalating registration ${registration.id} to console`);

    try {
      // Update registration status
      const { error: updateError } = await this.supabaseClient
        .from('business_registrations')
        .update({
          console_escalated_at: new Date().toISOString(),
          escalation_reason: reason || 'Automatic escalation due to timeout',
          updated_at: new Date().toISOString()
        })
        .eq('id', registration.id);

      if (updateError) {
        console.error('[ESCALATION] Failed to update registration:', updateError);
        return false;
      }

      // Send notification to console admins
      const detectionResult: DomainDetectionResult = registration.domain_detection_result || {
        type: 'EXISTING_CUSTOMER',
        confidence_score: 1.0,
        detection_timestamp: new Date().toISOString()
      };

      await businessEmailService.sendConsoleAdminNotification(
        registration,
        detectionResult
      );

      console.log(`[ESCALATION] Successfully escalated registration ${registration.id} to console`);
      return true;
    } catch (error) {
      console.error(`[ESCALATION] Failed to escalate registration ${registration.id}:`, error);
      return false;
    }
  }

  /**
   * Auto-deny registration after maximum timeout
   */
  private async autoDenyRegistration(registration: BusinessRegistration): Promise<void> {
    console.log(`[ESCALATION] Auto-denying registration ${registration.id} after timeout`);

    try {
      // Update registration status
      const { error: updateError } = await this.supabaseClient
        .from('business_registrations')
        .update({
          status: 'rejected',
          auto_denied_at: new Date().toISOString(),
          rejection_reason: `Automatically denied after ${this.config.auto_deny_timeout_days} days of no response`,
          updated_at: new Date().toISOString()
        })
        .eq('id', registration.id);

      if (updateError) {
        console.error('[ESCALATION] Failed to auto-deny registration:', updateError);
        return;
      }

      // Send rejection email to applicant
      await businessEmailService.sendRejectionEmail(
        registration,
        `Your registration was automatically rejected after ${this.config.auto_deny_timeout_days} days of no response. Please resubmit your application if you still wish to join ProcureServe.`
      );

      console.log(`[ESCALATION] Successfully auto-denied registration ${registration.id}`);
    } catch (error) {
      console.error(`[ESCALATION] Failed to auto-deny registration ${registration.id}:`, error);
      throw error;
    }
  }

  /**
   * Get escalation analytics
   */
  async getEscalationAnalytics(): Promise<{
    total_pending: number;
    awaiting_company_admin: number;
    escalated_to_console: number;
    auto_denied_count: number;
    average_resolution_days: number;
    overdue_registrations: BusinessRegistration[];
  }> {
    try {
      // Get all registrations for analytics
      const { data: allRegistrations } = await this.supabaseClient
        .from('business_registrations')
        .select('*')
        .order('created_at', { ascending: false });

      if (!allRegistrations) {
        return {
          total_pending: 0,
          awaiting_company_admin: 0,
          escalated_to_console: 0,
          auto_denied_count: 0,
          average_resolution_days: 0,
          overdue_registrations: []
        };
      }

      const now = new Date();
      const pending = allRegistrations.filter(r => r.status === 'pending_review');
      const awaitingCompanyAdmin = pending.filter(r => 
        r.domain_detection_result?.type === 'EXISTING_CUSTOMER' && !r.console_escalated_at
      );
      const escalatedToConsole = pending.filter(r => !!r.console_escalated_at);
      const autoDenied = allRegistrations.filter(r => !!r.auto_denied_at);
      
      // Get overdue registrations (older than 3 days for company admin, 14 days for console)
      const overdueRegistrations = pending.filter(r => {
        const daysSinceSubmission = Math.floor((now.getTime() - new Date(r.created_at).getTime()) / (1000 * 60 * 60 * 24));
        const isCompanyAdminResponsible = r.domain_detection_result?.type === 'EXISTING_CUSTOMER' && !r.console_escalated_at;
        
        if (isCompanyAdminResponsible) {
          return daysSinceSubmission >= this.config.company_admin_timeout_days;
        } else {
          return daysSinceSubmission >= this.config.auto_deny_timeout_days;
        }
      });

      // Calculate average resolution time for resolved registrations
      const resolvedRegistrations = allRegistrations.filter(r => 
        r.status === 'approved' || r.status === 'rejected'
      );
      
      let totalResolutionDays = 0;
      resolvedRegistrations.forEach(r => {
        const createdAt = new Date(r.created_at);
        const resolvedAt = new Date(r.updated_at);
        const resolutionDays = Math.floor((resolvedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24));
        totalResolutionDays += resolutionDays;
      });

      const averageResolutionDays = resolvedRegistrations.length > 0 
        ? Math.round(totalResolutionDays / resolvedRegistrations.length) 
        : 0;

      return {
        total_pending: pending.length,
        awaiting_company_admin: awaitingCompanyAdmin.length,
        escalated_to_console: escalatedToConsole.length,
        auto_denied_count: autoDenied.length,
        average_resolution_days: averageResolutionDays,
        overdue_registrations: overdueRegistrations
      };
    } catch (error) {
      console.error('[ESCALATION] Error getting analytics:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const escalationService = new BusinessRegistrationEscalationService();
