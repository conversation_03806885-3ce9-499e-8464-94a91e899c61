// Automated Escalation Processor
// Runs escalation checks automatically via cron or scheduled tasks

import { escalationService } from '$lib/business-email/escalation-service';

/**
 * Main escalation processor function
 * This should be called by a cron job or scheduled task
 */
export async function runEscalationProcessor(): Promise<{
  success: boolean;
  timestamp: string;
  results?: any;
  error?: string;
}> {
  const timestamp = new Date().toISOString();
  
  try {
    console.log(`[ESCALATION PROCESSOR] Starting automated escalation processing at ${timestamp}`);
    
    // Run the escalation processing
    const results = await escalationService.processEscalations();
    
    console.log(`[ESCALATION PROCESSOR] Completed successfully:`, results);
    
    // Log results to console for monitoring
    if (results.escalated > 0 || results.auto_denied > 0 || results.reminders_sent > 0) {
      console.log(`[ESCALATION PROCESSOR] Action taken: ${results.escalated} escalated, ${results.auto_denied} auto-denied, ${results.reminders_sent} reminders sent`);
    } else {
      console.log(`[ESCALATION PROCESSOR] No actions needed for ${results.processed} pending registrations`);
    }
    
    return {
      success: true,
      timestamp,
      results
    };
  } catch (error) {
    console.error(`[ESCALATION PROCESSOR] Error during processing:`, error);
    
    return {
      success: false,
      timestamp,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Health check for the escalation system
 */
export async function escalationHealthCheck(): Promise<{
  healthy: boolean;
  analytics: any;
  issues: string[];
}> {
  const issues: string[] = [];
  
  try {
    // Get current analytics
    const analytics = await escalationService.getEscalationAnalytics();
    
    // Check for potential issues
    if (analytics.overdue_registrations.length > 10) {
      issues.push(`${analytics.overdue_registrations.length} overdue registrations need immediate attention`);
    }
    
    if (analytics.average_resolution_days > 7) {
      issues.push(`Average resolution time of ${analytics.average_resolution_days} days is above recommended 7 days`);
    }
    
    if (analytics.total_pending > 50) {
      issues.push(`${analytics.total_pending} total pending registrations may indicate processing delays`);
    }
    
    return {
      healthy: issues.length === 0,
      analytics,
      issues
    };
  } catch (error) {
    return {
      healthy: false,
      analytics: null,
      issues: [`Escalation service error: ${error instanceof Error ? error.message : 'Unknown error'}`]
    };
  }
}

// For testing purposes - can be called directly
if (import.meta.hot) {
  // Only run in development mode for testing
  console.log('[ESCALATION PROCESSOR] Development mode - not running automatically');
}
