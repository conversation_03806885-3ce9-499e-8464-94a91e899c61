// Business Email System Entry Point
// Provides clean imports for all email functionality

export * from './domain-detection';
export * from './email-service';
export * from './workflow';

// Convenience exports for common use cases
export { 
  detectDomainType,
  determineEmailRouting,
  extractEmailDomain,
  getConsoleAdminEmails
} from './domain-detection';

export { 
  businessEmailService 
} from './email-service';

export { 
  handleRegistrationSubmitted,
  handleRegistrationApproved, 
  handleRegistrationRejected,
  testEmailWorkflow
} from './workflow';
