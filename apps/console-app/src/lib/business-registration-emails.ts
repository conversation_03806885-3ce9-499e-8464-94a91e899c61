// Business registration email notifications for console-app
import { createEmailService, renderEmailTemplate } from '@psii/email-service'
import { createSupabaseAdminClient } from './server/supabase-admin'
import type { BusinessApprovalEmailData, BusinessRejectionEmailData } from '@psii/email-service'

export class ConsoleBusinessRegistrationEmailService {
  private emailService
  private supabaseClient

  constructor() {
    this.supabaseClient = createSupabaseAdminClient()
    this.emailService = createEmailService(this.supabaseClient)
  }

  async sendRegistrationApprovedEmail(data: BusinessApprovalEmailData): Promise<void> {
    try {
      const emailContent = renderEmailTemplate('registration_approved', {
        company_name: data.company_name,
        contact_name: data.contact_name,
        contact_email: data.contact_email,
        submission_date: data.submission_date,
        registration_id: data.registration_id,
        activation_link: data.activation_link,
        company_id: data.company_id
      })

      await this.emailService.sendEmail({
        to: data.contact_email,
        template_type: 'user_invitation',
        company_id: data.company_id,
        variables: {
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text,
          company_name: data.company_name,
          contact_name: data.contact_name,
          activation_link: data.activation_link
        }
      })

      console.log(`[EMAIL] Registration approved email sent to ${data.contact_email}`)
    } catch (error) {
      console.error('[EMAIL] Failed to send registration approved email:', error)
      throw error // Approval email is critical
    }
  }

  async sendRegistrationRejectedEmail(data: BusinessRejectionEmailData): Promise<void> {
    try {
      const emailContent = renderEmailTemplate('registration_rejected', {
        company_name: data.company_name,
        contact_name: data.contact_name,
        contact_email: data.contact_email,
        submission_date: data.submission_date,
        registration_id: data.registration_id,
        rejection_reason: data.rejection_reason,
        support_email: data.support_email
      })

      await this.emailService.sendEmail({
        to: data.contact_email,
        template_type: 'notification',
        company_id: 'system', // System email, no specific company  
        variables: {
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text,
          company_name: data.company_name,
          contact_name: data.contact_name,
          rejection_reason: data.rejection_reason,
          support_email: data.support_email
        }
      })

      console.log(`[EMAIL] Registration rejected email sent to ${data.contact_email}`)
    } catch (error) {
      console.error('[EMAIL] Failed to send registration rejected email:', error)
      // Don't throw error - email failure shouldn't break rejection process
    }
  }
}

// Singleton instance
export const consoleBusinessRegistrationEmails = new ConsoleBusinessRegistrationEmailService() 