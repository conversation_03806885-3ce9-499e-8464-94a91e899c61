{"name": "console-app", "version": "0.0.1", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite dev --port 3008", "preview": "vite preview --port 4174", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "eslint .", "format": "prettier --write .", "test": "vitest run", "test:watch": "vitest", "setup:check": "node ../../scripts/check-setup.js", "admin:create": "node ../../scripts/create-admin.js"}, "devDependencies": {"@sveltejs/adapter-vercel": "^5.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^4.0.0", "@types/node": "^22.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "autoprefixer": "^10.4.14", "eslint": "^9.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0", "postcss": "^8.4.24", "prettier": "^3.1.0", "prettier-plugin-svelte": "^3.1.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^5.0.3", "vitest": "^2.0.0"}, "dependencies": {"@psii/shared-types": "1.0.0", "@psii/shared-utils": "1.0.0", "@psii/security-service": "1.0.0", "@psii/audit-service": "1.0.0", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-svelte": "^0.445.0", "nanoid": "^5.0.7", "playwright": "^1.53.0", "recharts": "^2.15.4", "resend": "^4.6.0", "shadcn-svelte": "^1.0.2", "tailwind-merge": "^3.3.1", "zod": "^3.25.63"}}