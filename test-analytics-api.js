// Test the actual analytics API endpoints
import fetch from 'node-fetch'

const BASE_URL = 'http://localhost:3008'

async function testAnalyticsAPI() {
  console.log('=== TESTING ANALYTICS API ENDPOINTS ===\n')

  const endpoints = [
    '/api/registrations/analytics?metric=overview&timeframe=30d',
    '/api/registrations/analytics?metric=trends&timeframe=30d',
    '/api/registrations/analytics?metric=domains&timeframe=30d',
    '/api/registrations/analytics?metric=performance&timeframe=30d',
    '/api/registrations/analytics?metric=workflow&timeframe=30d',
    '/api/registrations/analytics?metric=funnel&timeframe=30d'
  ]

  for (const endpoint of endpoints) {
    try {
      console.log(`🔍 Testing: ${endpoint}`)
      const response = await fetch(`${BASE_URL}${endpoint}`)
      
      if (!response.ok) {
        console.log(`❌ HTTP ${response.status}: ${response.statusText}`)
        continue
      }

      const data = await response.json()
      console.log(`✅ Success:`, JSON.stringify(data, null, 2))
      
    } catch (error) {
      console.log(`❌ Error:`, error.message)
    }
    console.log('---')
  }
}

// Note: This test requires the console app to be running on port 3008
console.log('Note: This test requires the console app to be running on port 3008')
console.log('Run: npm run dev:console\n')

testAnalyticsAPI()
