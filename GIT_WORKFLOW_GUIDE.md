# Generated Files and Git Management

## 🚫 Files That Should NEVER Be Committed

### SvelteKit Generated Files
```bash
apps/*/
├── .svelte-kit/                    # ❌ NEVER commit
│   ├── generated/                  # ❌ Auto-generated code
│   ├── runtime/                    # ❌ Runtime files
│   ├── ambient.d.ts               # ❌ Generated types
│   └── ...                        # ❌ All other .svelte-kit files
```

### Why These Files Are Ignored:
1. **Generated**: Created automatically during `npm run dev` or `npm run build`
2. **Environment-specific**: Contain absolute paths to your machine
3. **Large**: Can be several MB and change frequently
4. **Recreatable**: Always regenerated from source code
5. **Security**: May expose local file system paths

## ✅ Correct Git Workflow

### Adding Files to Git
```bash
# ✅ Good - Add source files
git add src/
git add package.json
git add .env.example

# ❌ Bad - Don't force generated files  
git add -f .svelte-kit/        # NEVER do this
git add -f node_modules/       # NEVER do this
```

### Safe Git Commands
```bash
# Add all files (respects .gitignore)
git add .

# Add specific files only
git add src/ package.json

# Check what will be committed (excludes ignored files)
git status

# See what's being ignored
git status --ignored
```

## 🔧 Development Workflow

### Starting Development
```bash
# 1. Install dependencies (creates node_modules)
npm install

# 2. Start dev server (creates .svelte-kit)
npm run dev

# 3. These folders are created automatically:
#    ├── node_modules/     (from npm install)
#    └── .svelte-kit/      (from npm run dev)
```

### Working with Environment Files
```bash
# ✅ Commit these
git add .env.example          # Template
git add .env.local.example    # Template

# ❌ Never commit these (they contain secrets)
.env.local                    # Contains API keys
.env.production              # Contains production secrets
```

## 🛠️ If You Accidentally Added Generated Files

### Remove from staging
```bash
# Remove specific files from staging
git reset HEAD .svelte-kit/

# Remove all ignored files from staging
git reset HEAD "**/.svelte-kit/"
```

### Clean up committed generated files (if needed)
```bash
# Remove from Git but keep locally
git rm -r --cached .svelte-kit/
git commit -m "Remove generated .svelte-kit files from Git"
```

## 📋 What SHOULD Be in Git

### ✅ Source Files
- `src/` - All source code
- `static/` - Static assets
- `package.json` - Dependencies
- `svelte.config.js` - SvelteKit configuration
- `vite.config.js` - Vite configuration
- `tsconfig.json` - TypeScript configuration

### ✅ Configuration Templates
- `.env.example` - Environment variable template
- `README.md` - Documentation
- `.gitignore` - Git ignore rules

### ✅ Database & Infrastructure
- `supabase/migrations/` - Database migrations
- `docs/` - Documentation
- Configuration files (without secrets)

## 🎯 Current Project Status

Your `.gitignore` is correctly configured with:
```gitignore
**/.svelte-kit/
node_modules/
.env.local
```

This ensures generated files are automatically excluded from Git.

## 💡 Pro Tip

If you see Git warnings about ignored files, it usually means:
1. Your `.gitignore` is working correctly ✅
2. You're trying to add files that shouldn't be in Git ❌

**Solution**: Only add the source files you actually need, not the generated ones.
