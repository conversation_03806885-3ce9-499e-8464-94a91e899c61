#!/usr/bin/env node

import dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Load environment from console app
dotenv.config({ path: join(__dirname, 'apps/console-app/.env.local') })
dotenv.config({ path: join(__dirname, '.env.local') })

async function testEmailUrlGeneration() {
  console.log('🔍 Testing Email URL Generation...\n')
  
  // Check environment variables
  const publicConsoleUrl = process.env.PUBLIC_CONSOLE_APP_URL
  const consoleUrl = process.env.CONSOLE_APP_URL
  
  console.log('1. Environment Variables:')
  console.log('   PUBLIC_CONSOLE_APP_URL:', publicConsoleUrl || '❌ Missing')
  console.log('   CONSOLE_APP_URL:', consoleUrl || '❌ Missing')
  
  // Test URL generation
  const testToken = 'test123456789'
  const activationUrl = `${publicConsoleUrl}/auth/activate-console?token=${testToken}`
  
  console.log('\n2. Generated URLs:')
  console.log('   Base URL:', publicConsoleUrl)
  console.log('   Activation URL:', activationUrl)
  
  if (activationUrl.includes('undefined')) {
    console.log('\n❌ Problem detected: URL contains "undefined"')
    console.log('   This means PUBLIC_CONSOLE_APP_URL is not set properly')
    console.log('\n🔧 Solutions:')
    console.log('   1. Add PUBLIC_CONSOLE_APP_URL=http://localhost:3008 to console app .env.local')
    console.log('   2. Restart the console app: npm run dev')
    console.log('   3. Make sure SvelteKit picks up the PUBLIC_ environment variable')
    return false
  } else {
    console.log('\n✅ URL generation looks correct!')
    return true
  }
}

testEmailUrlGeneration().then(success => {
  if (success) {
    console.log('\n🎉 Email URL generation should work properly now.')
    console.log('Restart your console app and try creating an invitation again.')
  } else {
    console.log('\n⚠️  Fix the environment variables and restart the console app.')
  }
})
