{"name": "procureserve-ii", "version": "1.0.0", "type": "module", "description": "ProcureServe II - Enterprise Staffing Platform", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "npm run dev:customer", "dev:customer": "cd apps/customer-app && npm run dev", "dev:candidate": "cd apps/candidate-app && npm run dev", "dev:console": "cd apps/console-app && npm run dev", "dev:supabase": "npx supabase start", "build": "npm run build:customer && npm run build:console", "build:customer": "cd apps/customer-app && npm run build", "build:candidate": "cd apps/candidate-app && npm run build", "build:console": "cd apps/console-app && npm run build", "supabase:start": "npx supabase start", "supabase:stop": "npx supabase stop", "supabase:reset": "npx supabase db reset", "supabase:status": "npx supabase status", "setup": "npm run supabase:start"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "dotenv": "^16.5.0", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3"}, "devDependencies": {"@playwright/test": "^1.53.1", "@types/nodemailer": "^6.4.17"}}