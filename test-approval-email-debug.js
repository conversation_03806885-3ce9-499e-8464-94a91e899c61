#!/usr/bin/env node

// Debug the approval email sending issue
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('🔍 Debugging Approval Email Issue');
console.log('📧 Checking Resend Configuration...');

async function debugApprovalEmail() {
  const supabase = createClient(supabaseUrl, serviceRoleKey, {
    auth: { autoRefreshToken: false, persistSession: false }
  });

  try {
    // 1. Check environment variables
    console.log('\n1️⃣ Environment Variables Check:');
    console.log('RESEND_API_KEY:', process.env.RESEND_API_KEY ? 'SET ✅' : 'MISSING ❌');
    console.log('EMAIL_FROM_ADDRESS:', process.env.EMAIL_FROM_ADDRESS || 'NOT SET');
    console.log('EMAIL_FROM_NAME:', process.env.EMAIL_FROM_NAME || 'NOT SET');
    console.log('EMAIL_REPLY_TO:', process.env.EMAIL_REPLY_TO || 'NOT SET');

    // 2. Check if there's a business registration to test with
    console.log('\n2️⃣ Looking for test business registration...');
    const { data: registrations, error: regError } = await supabase
      .from('business_registrations')
      .select('*')
      .eq('contact_person_email', '<EMAIL>')
      .limit(1);

    if (regError) {
      console.error('❌ Error fetching registrations:', regError);
      return;
    }

    if (!registrations || registrations.length === 0) {
      console.log('❌ No registration <NAME_EMAIL>');
      return;
    }

    const registration = registrations[0];
    console.log('✅ Found registration:', registration.id);
    console.log('   Status:', registration.status);
    console.log('   Company:', registration.company_name);

    // 3. Test direct Resend email send
    console.log('\n3️⃣ Testing Direct Resend Email Send...');
    
    if (!process.env.RESEND_API_KEY) {
      console.log('❌ RESEND_API_KEY not found, cannot test');
      return;
    }

    try {
      const { Resend } = await import('resend');
      const resend = new Resend(process.env.RESEND_API_KEY);

      const testEmail = {
        from: `${process.env.EMAIL_FROM_NAME || 'ProcureServe'} <${process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'}>`,
        to: [registration.contact_person_email],
        subject: '🧪 Test Approval Email - PSII Debug',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #2563eb;">Registration Approved!</h1>
            <p>Dear ${registration.contact_person_name},</p>
            <p>Your business registration for <strong>${registration.company_name}</strong> has been approved!</p>
            <p>This is a test email to debug the approval flow.</p>
            <div style="text-align: center; margin: 25px 0;">
              <a href="https://app.procureserve.com/activate?token=debug-test-token" 
                 style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;">
                Activate Your Account
              </a>
            </div>
            <p style="color: #64748b; font-size: 14px;">
              This email was sent during debugging on ${new Date().toISOString()}
            </p>
          </div>
        `,
        text: `
          Registration Approved!
          
          Dear ${registration.contact_person_name},
          
          Your business registration for ${registration.company_name} has been approved!
          
          Activate your account: https://app.procureserve.com/activate?token=debug-test-token
          
          This email was sent during debugging on ${new Date().toISOString()}
        `
      };

      console.log('📧 Sending test email via Resend...');
      console.log('   From:', testEmail.from);
      console.log('   To:', testEmail.to);
      console.log('   Subject:', testEmail.subject);

      const result = await resend.emails.send(testEmail);

      if (result.data) {
        console.log('✅ Email sent successfully!');
        console.log('   Message ID:', result.data.id);
        console.log('   Check your Resend dashboard for delivery status');
      } else {
        console.log('❌ Email send failed:', result.error);
      }

    } catch (emailError) {
      console.error('❌ Direct email test failed:', emailError);
    }

    // 4. Check business registration data structure
    console.log('\n4️⃣ Registration Data Structure:');
    console.log(JSON.stringify(registration, null, 2));

    // 5. Check if activation tokens table exists and has proper structure
    console.log('\n5️⃣ Checking activation tokens table...');
    const { data: tokenTest, error: tokenError } = await supabase
      .from('activation_tokens')
      .select('*')
      .limit(1);

    if (tokenError) {
      console.log('❌ Activation tokens table issue:', tokenError.message);
      console.log('   This might be why activation emails are not working');
    } else {
      console.log('✅ Activation tokens table accessible');
      console.log('   Records found:', tokenTest?.length || 0);
    }

  } catch (error) {
    console.error('❌ Debug process failed:', error);
  }
}

debugApprovalEmail().catch(console.error);
