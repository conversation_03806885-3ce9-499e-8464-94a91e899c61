import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '/Users/<USER>/Desktop/PSII/.env.local' });

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Service Role Key exists:', !!serviceRoleKey);

const serviceClient = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

console.log('Testing company_blacklist table access...');

async function testBlacklistAccess() {
  try {
    // Test 1: Simple select count
    console.log('\n1. Testing COUNT(*) from company_blacklist...');
    const { data: countData, error: countError } = await serviceClient
      .from('company_blacklist')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('COUNT error:', countError);
    } else {
      console.log('COUNT success:', countData);
    }

    // Test 2: Test the same query as in the registration code
    console.log('\n2. Testing domain query (same as registration code)...');
    const testDomain = 'boyarcc.com';
    const { data: domainData, error: domainError } = await serviceClient
      .from('company_blacklist')
      .select('domain')
      .eq('domain', testDomain)
      .maybeSingle();
    
    if (domainError) {
      console.error('Domain query error:', domainError);
    } else {
      console.log('Domain query success:', domainData);
    }

    // Test 3: Raw SQL query
    console.log('\n3. Testing raw SQL query...');
    const { data: sqlData, error: sqlError } = await serviceClient.rpc('exec_sql', {
      sql: 'SELECT COUNT(*) FROM public.company_blacklist'
    });
    
    if (sqlError) {
      console.error('SQL query error:', sqlError);
    } else {
      console.log('SQL query success:', sqlData);
    }

  } catch (error) {
    console.error('Test error:', error);
  }
}

testBlacklistAccess();
