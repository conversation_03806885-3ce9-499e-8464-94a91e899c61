import fetch from 'node-fetch';

const testRegistration = async () => {
  console.log('Testing business registration form submission...');
  
  // Test data
  const formData = new URLSearchParams({
    'company_name': 'Test Tech Solutions',
    'legal_entity_type': 'LLC',
    'tax_id': '12-3456789',
    'business_type': 'staffing_agency',
    'estimated_annual_volume': '1-5_million',
    'recruitment_enabled': 'true',
    'bench_sales_enabled': 'false',
    'time_zone': 'America/New_York',
    'street_address': '123 Test Street',
    'city': 'New York',
    'state': 'NY',
    'zip_code': '10001',
    'first_name': '<PERSON>',
    'last_name': 'Doe',
    'email': '<EMAIL>',
    'phone': '************',
    'title': 'CEO',
    'work_start': '09:00',
    'work_end': '17:00'
  });

  try {
    console.log('Submitting registration form...');
    const response = await fetch('http://localhost:3004/register/business', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData.toString()
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers));
    
    const responseText = await response.text();
    
    if (response.status === 303 || response.status === 302) {
      console.log('✅ Registration successful - got redirect');
      console.log('Redirect location:', response.headers.get('location'));
    } else if (response.status === 200) {
      // Check if there's an error message in the response
      if (responseText.includes('error') || responseText.includes('Unable to verify')) {
        console.log('❌ Registration failed with error on page');
        // Extract error message if possible
        const errorMatch = responseText.match(/error["\s]*:?\s*["']([^"']*)/i);
        if (errorMatch) {
          console.log('Error message:', errorMatch[1]);
        }
      } else {
        console.log('🤔 Got 200 response but unclear if successful');
      }
    } else {
      console.log('❌ Registration failed with status:', response.status);
    }
    
    // Log first 500 chars of response for debugging
    console.log('\nResponse preview:');
    console.log(responseText.substring(0, 500) + '...');
    
  } catch (error) {
    console.error('❌ Request failed:', error.message);
  }
};

testRegistration();
