// Test script to validate the business registration workflow
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '/Users/<USER>/Desktop/PSII/.env.local' });

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('🧪 Testing Business Registration Workflow Components');
console.log('===============================================');

const serviceClient = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testWorkflowComponents() {
  console.log('\n1️⃣ Testing Company Blacklist Query...');
  try {
    const testDomain = 'testdomain.com';
    const { data, error } = await serviceClient
      .from('company_blacklist')
      .select('domain')
      .eq('domain', testDomain)
      .maybeSingle();
    
    if (error) {
      console.log('❌ Blacklist query failed:', error.message);
      return false;
    } else {
      console.log('✅ Blacklist query successful:', data ? 'Domain found' : 'Domain not found');
    }
  } catch (error) {
    console.log('❌ Blacklist query error:', error.message);
    return false;
  }

  console.log('\n2️⃣ Testing Business Registration Insert...');
  try {
    const testRegistration = {
      contact_person_name: 'John Test',
      contact_person_title: 'CEO',
      contact_person_email: 'john@testcompany-' + Date.now() + '.com',
      contact_person_phone: '************',
      company_name: 'Test Company ' + Date.now(),
      company_domain: 'testcompany-' + Date.now() + '.com',
      status: 'pending',
      domain_detection_result: {
        type: 'NEW_COMPANY',
        domain: 'testcompany-' + Date.now() + '.com',
        confidence: 100,
        detection_timestamp: new Date().toISOString()
      },
      routing_authority: 'console_admin',
      detected_company_id: null
    };

    const { data, error } = await serviceClient
      .from('business_registrations')
      .insert(testRegistration)
      .select();

    if (error) {
      console.log('❌ Registration insert failed:', error.message);
      console.log('Error details:', error);
      return false;
    } else {
      console.log('✅ Registration insert successful, ID:', data[0]?.id);
      
      // Clean up test data
      if (data[0]?.id) {
        await serviceClient
          .from('business_registrations')
          .delete()
          .eq('id', data[0].id);
        console.log('🧹 Test registration cleaned up');
      }
    }
  } catch (error) {
    console.log('❌ Registration insert error:', error.message);
    return false;
  }

  console.log('\n3️⃣ Testing Companies Table Query...');
  try {
    const { data, error } = await serviceClient
      .from('companies')
      .select('id, name, domain, registration_status')
      .eq('registration_status', 'approved')
      .limit(3);

    if (error) {
      console.log('❌ Companies query failed:', error.message);
      return false;
    } else {
      console.log('✅ Companies query successful, found', data?.length || 0, 'approved companies');
      data?.forEach(company => {
        console.log(`   - ${company.name} (${company.domain})`);
      });
    }
  } catch (error) {
    console.log('❌ Companies query error:', error.message);
    return false;
  }

  return true;
}

async function runTests() {
  const success = await testWorkflowComponents();
  
  console.log('\n📊 Test Summary');
  console.log('===============');
  if (success) {
    console.log('🎉 All core workflow components are working correctly!');
    console.log('✅ The business registration form should work end-to-end');
    console.log('✅ Database tables are accessible and functional');
    console.log('✅ Domain detection and routing logic can execute');
  } else {
    console.log('⚠️ Some components have issues that need to be addressed');
  }
}

runTests().catch(error => {
  console.error('💥 Test execution failed:', error);
});
