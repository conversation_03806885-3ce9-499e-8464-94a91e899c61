// Test business_leads table access
import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: './apps/console-app/.env.local' })

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

console.log('Testing business_leads table access...')
console.log('Supabase URL:', supabaseUrl)
console.log('Service Role Key available:', !!serviceRoleKey)

const adminClient = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function testLeadsData() {
  try {
    console.log('\n1. Testing business_leads table...')
    const { data: leads, error: leadsError, count } = await adminClient
      .from('business_leads')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })
      .limit(10)

    if (leadsError) {
      console.error('❌ Error fetching business_leads:', leadsError)
    } else {
      console.log('✅ business_leads table - Total records:', count)
      console.log('✅ Sample records:', leads?.length || 0)
      
      if (leads && leads.length > 0) {
        console.log('\nSample lead:')
        console.log({
          id: leads[0].id,
          company_name: leads[0].company_name,
          status: leads[0].status,
          domain_extracted: leads[0].domain_extracted,
          domain_detection_result: leads[0].domain_detection_result,
          created_at: leads[0].created_at
        })
      }
    }

    console.log('\n2. Testing business_registrations table...')
    const { data: regs, error: regsError, count: regCount } = await adminClient
      .from('business_registrations')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })
      .limit(10)

    if (regsError) {
      console.error('❌ Error fetching business_registrations:', regsError)
    } else {
      console.log('✅ business_registrations table - Total records:', regCount)
      console.log('✅ Sample records:', regs?.length || 0)
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error)
  }
}

testLeadsData()
