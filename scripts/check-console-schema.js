#!/usr/bin/env node

// <PERSON>ript to check console app database schema in cloud Supabase
// This helps verify if all required columns exist

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env') })

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY in environment variables')
  process.exit(1)
}

console.log('🔍 Checking cloud Supabase schema...')
console.log(`📡 Connected to: ${supabaseUrl}\n`)

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function checkSchema() {
  const tablesToCheck = [
    {
      name: 'console_user_invitations',
      requiredColumns: ['id', 'email', 'role', 'token', 'expires_at', 'status', 'updated_at', 'created_at']
    },
    {
      name: 'console_users',
      requiredColumns: ['id', 'email', 'role', 'first_name', 'last_name', 'is_active', 'created_at']
    },
    {
      name: 'console_security_events',
      requiredColumns: ['id', 'event_type', 'user_id', 'resource', 'resource_id', 'success', 'timestamp']
    }
  ]

  for (const table of tablesToCheck) {
    console.log(`\n📋 Checking table: ${table.name}`)
    console.log('─'.repeat(50))
    
    try {
      // Try to query the table to see if it exists
      const { data, error } = await supabase
        .from(table.name)
        .select('*')
        .limit(0)
      
      if (error) {
        if (error.message.includes('does not exist')) {
          console.error(`❌ Table ${table.name} does not exist!`)
          continue
        } else {
          console.error(`⚠️  Error querying ${table.name}: ${error.message}`)
        }
      } else {
        console.log(`✅ Table ${table.name} exists`)
      }
      
      // Check for specific columns by trying to query them
      for (const column of table.requiredColumns) {
        const { error: colError } = await supabase
          .from(table.name)
          .select(column)
          .limit(0)
        
        if (colError) {
          if (colError.message.includes(`Could not find the '${column}' column`)) {
            console.log(`   ❌ Missing column: ${column}`)
          } else if (!colError.message.includes('does not exist')) {
            console.log(`   ⚠️  Error checking ${column}: ${colError.message}`)
          }
        } else {
          console.log(`   ✅ Column exists: ${column}`)
        }
      }
      
    } catch (error) {
      console.error(`❌ Unexpected error checking ${table.name}:`, error)
    }
  }
  
  console.log('\n' + '='.repeat(50))
  console.log('📌 Schema check complete!')
  console.log('\nIf you see missing columns above, run the migration SQL in your Supabase Dashboard.')
  console.log('Migration file: supabase/migrations/20250625000001_fix_console_invitations_schema.sql')
}

// Run the check
checkSchema()
  .then(() => {
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n❌ Schema check failed:', error)
    process.exit(1)
  }) 