#!/bin/bash
# Quick Email Setup Test
# Usage: ./scripts/test-registration-flow.sh

echo "🧪 Testing PSII Email Registration Flow..."
echo "=====================================|"

# 1. Test environment variables
echo "1️⃣ Checking environment variables..."
if [ -z "$SMTP_PASS" ]; then
  echo "❌ SMTP_PASS not set"
  exit 1
else
  echo "✅ SMTP credentials configured"
fi

# 2. Test direct SMTP
echo ""
echo "2️⃣ Testing direct SMTP connection..."
node scripts/test-email.js
if [ $? -eq 0 ]; then
  echo "✅ SMTP test passed"
else
  echo "❌ SMTP test failed"
  exit 1
fi

# 3. Check Supabase connection
echo ""
echo "3️⃣ Verifying Supabase configuration..."
node scripts/verify-email-setup.js

echo ""
echo "🎯 Next Step: Configure SMTP in Supabase Dashboard"
echo "   URL: https://supabase.com/dashboard/project/hfzhvrknjgwtrkgyinjf/auth/settings"
echo ""
echo "🚀 Then test registration at: http://localhost:3006/register"
