#!/usr/bin/env node

/**
 * Test script for console-app user invitation system
 * Run: node scripts/test-console-invite.js
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Load environment variables
dotenv.config({ path: join(__dirname, '..', '.env.local') })

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { persistSession: false }
})

async function testConsoleInvitation() {
  console.log('🧪 Testing Console User Invitation System\n')

  try {
    // 1. Get the first super admin
    const { data: superAdmin, error: adminError } = await supabase
      .from('console_users')
      .select('*')
      .eq('role', 'super_admin')
      .eq('is_active', true)
      .single()

    if (adminError || !superAdmin) {
      console.error('❌ No active super admin found. Run the initial setup first.')
      return
    }

    console.log('✅ Found super admin:', superAdmin.email)

    // 2. Get a company for the invitation
    const { data: companies, error: companyError } = await supabase
      .from('companies')
      .select('id, name')
      .limit(1)

    if (companyError || !companies || companies.length === 0) {
      console.error('❌ No companies found. Create a company first.')
      return
    }

    const company = companies[0]
    console.log('✅ Using company:', company.name)

    // 3. Create a test invitation
    const testEmail = `test-manager-${Date.now()}@example.com`
    const invitationToken = crypto.randomUUID()
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7)

    const { data: invitation, error: inviteError } = await supabase
      .from('console_user_invitations')
      .insert({
        email: testEmail,
        role: 'company_manager',
        company_id: company.id,
        permissions: [
          { resource: 'enums', actions: ['read'], company_id: company.id },
          { resource: 'users', actions: ['read'], company_id: company.id },
          { resource: 'settings', actions: ['read'], company_id: company.id }
        ],
        invited_by: superAdmin.id,
        token: invitationToken,
        expires_at: expiresAt.toISOString(),
        status: 'pending'
      })
      .select()
      .single()

    if (inviteError) {
      console.error('❌ Failed to create invitation:', inviteError)
      return
    }

    console.log('✅ Created invitation for:', testEmail)
    console.log('   Role: company_manager')
    console.log('   Company:', company.name)
    console.log('   Expires:', expiresAt.toLocaleDateString())

    // 4. Generate activation URL
    const activationUrl = `http://localhost:3008/auth/activate-console?token=${invitationToken}`
    console.log('\n📧 Activation URL (normally sent via email):')
    console.log('   ', activationUrl)

    // 5. Show pending invitations
    const { data: pendingInvites, error: pendingError } = await supabase
      .from('console_user_invitations')
      .select('*')
      .eq('status', 'pending')
      .order('created_at', { ascending: false })

    if (!pendingError && pendingInvites) {
      console.log('\n📋 Current pending invitations:', pendingInvites.length)
      pendingInvites.forEach(invite => {
        console.log(`   - ${invite.email} (${invite.role}) - Expires: ${new Date(invite.expires_at).toLocaleDateString()}`)
      })
    }

    console.log('\n✨ Test completed successfully!')
    console.log('\nNext steps:')
    console.log('1. Start the console app: cd apps/console-app && npm run dev')
    console.log('2. Sign in as super admin')
    console.log('3. Go to /users to see the pending invitation')
    console.log('4. Or use the activation URL to test user activation')
    console.log('5. You can also go to /users/invite to create more invitations through the UI')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testConsoleInvitation() 