// Email Verification Script
// Run this after configuring SMTP in Supabase Dashboard
// Usage: node scripts/verify-email-setup.js

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

config()

async function verifyEmailSetup() {
  console.log('🔍 Verifying Email Configuration...\n')
  
  console.log('📋 Configuration Check:')
  console.log(`✓ Supabase URL: ${process.env.PUBLIC_SUPABASE_URL}`)
  console.log(`✓ SMTP Host: ${process.env.SMTP_HOST}`)
  console.log(`✓ SMTP Port: ${process.env.SMTP_PORT}`)
  console.log(`✓ From Email: ${process.env.EMAIL_FROM_ADDRESS}`)
  console.log(`✓ From Name: ${process.env.EMAIL_FROM_NAME}\n`)
  
  try {
    const supabase = createClient(
      process.env.PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )

    console.log('🧪 Testing Supabase Connection...')
    
    // Test basic connection
    const { data: healthCheck, error: healthError } = await supabase
      .from('companies')
      .select('id')
      .limit(1)

    if (healthError) {
      throw new Error(`Supabase connection failed: ${healthError.message}`)
    }

    console.log('✅ Supabase connection successful\n')

    console.log('📧 Email Configuration Status:')
    console.log('')
    console.log('   ⚠️  Manual setup required in Supabase Dashboard:')
    console.log('   📍 https://supabase.com/dashboard/project/hfzhvrknjgwtrkgyinjf/auth/settings')
    console.log('')
    console.log('   Required SMTP Settings:')
    console.log('   📮 Host: smtp.zeptomail.in')
    console.log('   🔌 Port: 587')
    console.log('   👤 User: emailapikey')
    console.log('   🔑 Password: [ZeptoMail API Key]')
    console.log('   📨 Sender: <EMAIL>')
    console.log('')
    console.log('✅ Local configuration is ready')
    console.log('✅ Environment variables are set')
    console.log('✅ Database migration applied')
    console.log('✅ Email service packages configured')
    console.log('')
    console.log('🎯 Next Steps:')
    console.log('1. Configure SMTP in Supabase Dashboard (see docs/supabase-smtp-setup.md)')
    console.log('2. Run: node scripts/test-auth-emails.js')
    console.log('3. Test user registration in all apps')
    console.log('4. Verify emails are received successfully')
    
  } catch (error) {
    console.error('❌ Verification failed:')
    console.error('Error:', error.message)
    process.exit(1)
  }
}

verifyEmailSetup()
