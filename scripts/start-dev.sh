#!/bin/bash

# ProcureServe II - Cloud Development with Smart Port Management
# Kills conflicting processes and starts all 3 apps cleanly

echo "🚀 Starting ProcureServe II Cloud Development Environment..."

# Color codes for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_customer() { echo -e "${CYAN}[CUSTOMER]${NC} $1"; }
print_console() { echo -e "${PURPLE}[CONSOLE]${NC} $1"; }
print_candidate() { echo -e "${GREEN}[CANDIDATE]${NC} $1"; }

# App configuration
CUSTOMER_PORT=3004
CONSOLE_PORT=3008
CANDIDATE_PORT=3006

# Function to kill process on port
kill_port() {
    local port=$1
    local app_name=$2
    local pids=$(lsof -ti:$port 2>/dev/null)
    
    if [ ! -z "$pids" ]; then
        print_warning "Port $port occupied by $app_name. Killing processes: $pids"
        echo $pids | xargs kill -9 2>/dev/null
        sleep 2
        
        # Verify port is free
        if lsof -ti:$port >/dev/null 2>&1; then
            print_error "Failed to free port $port. Please manually kill the process."
            return 1
        else
            print_success "Port $port freed successfully"
        fi
    else
        print_status "Port $port is available"
    fi
    return 0
}

# Function to wait for app to start
wait_for_app() {
    local port=$1
    local app_name=$2
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for $app_name to start on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://localhost:$port" >/dev/null 2>&1; then
            print_success "$app_name is running at http://localhost:$port"
            return 0
        fi
        
        if [ $((attempt % 5)) -eq 0 ]; then
            print_status "Still waiting for $app_name... (${attempt}s)"
        fi
        
        sleep 1
        attempt=$((attempt + 1))
    done
    
    print_warning "$app_name may still be starting at http://localhost:$port (timeout after ${max_attempts}s)"
    return 1
}

# Check if we're in the right directory
if [ ! -f ".env" ]; then
    print_error "Please run this script from the PSII project root directory"
    exit 1
fi

# Step 1: Verify Supabase Cloud connection
print_status "Verifying Supabase Cloud configuration..."
if grep -q "hfzhvrknjgwtrkgyinjf.supabase.co" .env; then
    print_success "Supabase Cloud configuration detected (hfzhvrknjgwtrkgyinjf)"
else
    print_error "Supabase Cloud configuration not found in .env"
    print_warning "Expected: https://hfzhvrknjgwtrkgyinjf.supabase.co"
    exit 1
fi

# Step 2: Free up ports by killing existing processes
print_status "Managing application ports..."
kill_port $CUSTOMER_PORT "Customer App" || exit 1
kill_port $CONSOLE_PORT "Console App" || exit 1  
kill_port $CANDIDATE_PORT "Candidate App" || exit 1

# Step 3: Install dependencies for all apps
print_status "Installing dependencies for all applications..."

# Customer App
print_customer "Installing Customer App dependencies..."
cd apps/customer-app
if npm install >/dev/null 2>&1; then
    print_success "Customer App dependencies installed"
else
    print_error "Failed to install Customer App dependencies"
    exit 1
fi
cd ../..

# Console App
print_console "Installing Console App dependencies..."
cd apps/console-app
if npm install >/dev/null 2>&1; then
    print_success "Console App dependencies installed"
else
    print_error "Failed to install Console App dependencies"
    exit 1
fi
cd ../..

# Candidate App
print_candidate "Installing Candidate App dependencies..."
cd apps/candidate-app
if npm install >/dev/null 2>&1; then
    print_success "Candidate App dependencies installed"
else
    print_error "Failed to install Candidate App dependencies"
    exit 1
fi
cd ../..

# Step 4: Start all applications with explicit ports
print_status "Starting all applications..."

# Start Customer App (Port 3004)
print_customer "Starting Customer App on port $CUSTOMER_PORT..."
cd apps/customer-app
npm run dev -- --port $CUSTOMER_PORT --host 0.0.0.0 >/dev/null 2>&1 &
CUSTOMER_PID=$!
cd ../..

# Start Console App (Port 3008)  
print_console "Starting Console App on port $CONSOLE_PORT..."
cd apps/console-app
npm run dev -- --port $CONSOLE_PORT --host 0.0.0.0 >/dev/null 2>&1 &
CONSOLE_PID=$!
cd ../..

# Start Candidate App (Port 3006)
print_candidate "Starting Candidate App on port $CANDIDATE_PORT..."
cd apps/candidate-app
npm run dev -- --port $CANDIDATE_PORT --host 0.0.0.0 >/dev/null 2>&1 &
CANDIDATE_PID=$!
cd ../..

# Step 5: Wait for all applications to start
print_status "Verifying application startup..."
wait_for_app $CUSTOMER_PORT "Customer App"
wait_for_app $CONSOLE_PORT "Console App"
wait_for_app $CANDIDATE_PORT "Candidate App"

# Step 6: Display environment status
echo ""
print_success "✨ ProcureServe II Cloud Development Environment is Ready!"
echo ""
print_customer "📱 Customer App (Business): http://localhost:$CUSTOMER_PORT"
print_console "🎛️  Console App (Internal):  http://localhost:$CONSOLE_PORT"
print_candidate "👥 Candidate App (Jobs):     http://localhost:$CANDIDATE_PORT"
echo ""
print_status "🌐 Supabase Cloud: https://hfzhvrknjgwtrkgyinjf.supabase.co"
print_status "🎛️  Supabase Dashboard: https://supabase.com/dashboard/project/hfzhvrknjgwtrkgyinjf"
print_status "📊 Database: Cloud Postgres with pgvector, auth, storage"
print_status "📧 Email: Supabase Auth emails"
print_status "🔄 Real-time: Supabase real-time subscriptions"
echo ""
print_warning "Press Ctrl+C to stop all services"
echo ""

# Store PIDs for cleanup
echo "$CUSTOMER_PID" > .customer_pid
echo "$CONSOLE_PID" > .console_pid  
echo "$CANDIDATE_PID" > .candidate_pid

# Cleanup function
cleanup() {
    print_status "Shutting down all applications..."
    
    # Kill all PIDs
    kill $CUSTOMER_PID $CONSOLE_PID $CANDIDATE_PID 2>/dev/null
    
    # Double-check ports are freed
    kill_port $CUSTOMER_PORT "Customer App" >/dev/null 2>&1
    kill_port $CONSOLE_PORT "Console App" >/dev/null 2>&1
    kill_port $CANDIDATE_PORT "Candidate App" >/dev/null 2>&1
    
    # Clean up PID files
    rm -f .customer_pid .console_pid .candidate_pid
    
    print_success "Development environment stopped cleanly"
    exit 0
}

# Trap Ctrl+C to run cleanup
trap cleanup INT

# Keep script running and monitor apps
print_status "Monitoring applications... (Press Ctrl+C to stop)"
while true; do
    sleep 10
    
    # Check if any app stopped unexpectedly
    if ! kill -0 $CUSTOMER_PID 2>/dev/null; then
        print_error "Customer App stopped unexpectedly"
        cleanup
    fi
    
    if ! kill -0 $CONSOLE_PID 2>/dev/null; then
        print_error "Console App stopped unexpectedly"  
        cleanup
    fi
    
    if ! kill -0 $CANDIDATE_PID 2>/dev/null; then
        print_error "Candidate App stopped unexpectedly"
        cleanup
    fi
done
