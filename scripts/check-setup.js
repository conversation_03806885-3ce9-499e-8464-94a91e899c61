#!/usr/bin/env node

/**
 * Setup Helper Script for ProcureServe II Console
 * Checks setup status and provides setup URL
 */

import { createClient } from '@supabase/supabase-js'
import { config } from 'dotenv'
import { join } from 'path'

// Load environment variables
config({ path: join(process.cwd(), '.env.local') })

const SUPABASE_URL = process.env.PUBLIC_SUPABASE_URL
const SUPABASE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY
const SETUP_TOKEN = process.env.INITIAL_ADMIN_SETUP_TOKEN
const ADMIN_EMAIL = process.env.INITIAL_ADMIN_EMAIL
const SETUP_ENABLED = process.env.ENABLE_INITIAL_SETUP === 'true'
const APP_PORT = process.env.PUBLIC_APP_PORT || '3008'

async function main() {
  console.log('🔧 ProcureServe II Console Setup Helper\n')

  // Validate environment
  if (!SUPABASE_URL || !SUPABASE_KEY) {
    console.error('❌ Missing Supabase configuration in .env.local')
    console.log('   Required: PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
    process.exit(1)
  }

  const supabase = createClient(SUPABASE_URL, SUPABASE_KEY)

  try {
    // Check for existing super admins
    const { data: admins, error } = await supabase
      .from('console_users')
      .select('id, email, role, created_at')
      .eq('role', 'super_admin')
      .eq('is_active', true)

    if (error) {
      console.error('❌ Database connection failed:', error.message)
      process.exit(1)
    }

    const hasAdmins = admins && admins.length > 0

    console.log('📊 Setup Status:')
    console.log(`   Super Admins: ${admins?.length || 0}`)
    console.log(`   Setup Required: ${hasAdmins ? '❌ No' : '✅ Yes'}`)
    console.log(`   Setup Enabled: ${SETUP_ENABLED ? '✅ Yes' : '❌ No'}`)
    console.log()

    if (hasAdmins) {
      console.log('👥 Existing Super Admins:')
      admins.forEach((admin, index) => {
        console.log(`   ${index + 1}. ${admin.email} (created: ${new Date(admin.created_at).toLocaleDateString()})`)
      })
      console.log()
      console.log('✅ Setup is already complete!')
      
      if (SETUP_ENABLED) {
        console.log('⚠️  SECURITY NOTICE: Disable setup by setting ENABLE_INITIAL_SETUP=false')
      }
    } else {
      console.log('🚀 Initial Setup Required')
      
      if (!SETUP_ENABLED) {
        console.log('❌ Setup is disabled. Enable by setting ENABLE_INITIAL_SETUP=true')
      } else if (!SETUP_TOKEN || !ADMIN_EMAIL) {
        console.log('❌ Setup configuration incomplete:')
        if (!ADMIN_EMAIL) console.log('   Missing: INITIAL_ADMIN_EMAIL')
        if (!SETUP_TOKEN) console.log('   Missing: INITIAL_ADMIN_SETUP_TOKEN')
      } else {
        console.log('✅ Setup is ready!')
        console.log()
        console.log('🔗 Setup URL:')
        console.log(`   http://localhost:${APP_PORT}/setup?token=${encodeURIComponent(SETUP_TOKEN)}`)
        console.log()
        console.log('📧 Admin Email:', ADMIN_EMAIL)
        console.log()
        console.log('📋 Next Steps:')
        console.log('   1. Visit the setup URL above')
        console.log('   2. Create a strong password for the admin account')
        console.log('   3. After setup, set ENABLE_INITIAL_SETUP=false for security')
      }
    }

    console.log()
    console.log('🔒 Security Notes:')
    console.log('   • Setup URLs contain sensitive tokens - keep them private')
    console.log('   • Always disable setup after completion')
    console.log('   • All setup actions are logged in console_security_events')

  } catch (error) {
    console.error('❌ Setup check failed:', error)
    process.exit(1)
  }
}

// Handle command line execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}