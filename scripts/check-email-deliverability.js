// Email Deliverability Checker
// File: scripts/check-email-deliverability.js
// Usage: node scripts/check-email-deliverability.js <email>

import { config } from 'dotenv'
import nodemailer from 'nodemailer'
import { createClient } from '@supabase/supabase-js'

config()

async function checkEmailDeliverability(targetEmail) {
  console.log(`🔍 Checking email deliverability for: ${targetEmail}\n`)
  
  const results = {
    smtp_test: false,
    supabase_test: false,
    domain_check: false,
    recommendations: []
  }

  try {
    // 1. Test SMTP Configuration
    console.log('1️⃣ Testing SMTP configuration...')
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || "smtp.zeptomail.in",
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: false,
      auth: {
        user: process.env.SMTP_USER || "emailapikey",
        pass: process.env.SMTP_PASS
      }
    })

    await transporter.verify()
    console.log('✅ SMTP connection verified')
    results.smtp_test = true

    // Send test email
    const testEmail = await transporter.sendMail({
      from: `"${process.env.EMAIL_FROM_NAME || 'ProcureServe Team'}" <${process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'}>`,
      to: targetEmail,
      subject: 'Email Deliverability Test',
      html: `
        <h2>📧 Email Deliverability Test</h2>
        <p>This email confirms that your ZeptoMail SMTP configuration is working correctly.</p>
        <p><strong>Test Details:</strong></p>
        <ul>
          <li>Sent at: ${new Date().toISOString()}</li>
          <li>From: ${process.env.EMAIL_FROM_ADDRESS}</li>
          <li>Via: ZeptoMail SMTP</li>
        </ul>
        <div style="background-color: #f0f9ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <p style="margin: 0; color: #1e40af;"><strong>✅ Success!</strong> Your email configuration is working properly.</p>
        </div>
      `,
      text: `Email Deliverability Test - Sent at ${new Date().toISOString()}`
    })

    console.log(`✅ Test email sent successfully (ID: ${testEmail.messageId})`)

  } catch (error) {
    console.error('❌ SMTP test failed:', error.message)
    results.recommendations.push('Check SMTP credentials and configuration')
  }

  try {
    // 2. Test Supabase Auth Email
    console.log('\n2️⃣ Testing Supabase Auth email...')
    const supabase = createClient(
      process.env.PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )

    const { error } = await supabase.auth.resetPasswordForEmail(targetEmail, {
      redirectTo: `${process.env.CANDIDATE_APP_URL}/reset-password`
    })

    if (error) {
      console.error('❌ Supabase auth email failed:', error.message)
      results.recommendations.push('Configure SMTP in Supabase Dashboard')
    } else {
      console.log('✅ Supabase auth email sent')
      results.supabase_test = true
    }

  } catch (error) {
    console.error('❌ Supabase test failed:', error.message)
    results.recommendations.push('Verify Supabase configuration')
  }

  // 3. Check email domain
  console.log('\n3️⃣ Checking email domain...')
  const domain = targetEmail.split('@')[1]
  
  try {
    // Basic domain validation
    if (domain && domain.includes('.')) {
      console.log(`✅ Domain appears valid: ${domain}`)
      results.domain_check = true
      
      // Check for common email providers
      const commonProviders = ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com', 'icloud.com']
      if (commonProviders.includes(domain.toLowerCase())) {
        console.log(`📧 Using popular email provider: ${domain}`)
        results.recommendations.push('Check spam folder for popular email providers')
      }
    } else {
      console.log('❌ Invalid email domain')
      results.recommendations.push('Verify email address format')
    }
  } catch (error) {
    console.error('❌ Domain check failed:', error.message)
  }

  // 4. Generate recommendations
  console.log('\n📋 Email Deliverability Report:')
  console.log('=' .repeat(50))
  console.log(`SMTP Test: ${results.smtp_test ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Supabase Test: ${results.supabase_test ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Domain Check: ${results.domain_check ? '✅ PASS' : '❌ FAIL'}`)

  if (results.recommendations.length > 0) {
    console.log('\n🔧 Recommendations:')
    results.recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`)
    })
  }

  // 5. Troubleshooting tips
  console.log('\n💡 Troubleshooting Tips:')
  console.log('• Check spam/junk folder')
  console.log('• Verify sender domain (procureserve.com) is trusted')
  console.log('• Ensure ZeptoMail domain verification is complete')
  console.log('• Test with different email addresses')
  console.log('• Monitor ZeptoMail dashboard for delivery status')

  console.log('\n🔗 Useful Links:')
  console.log('• ZeptoMail Console: https://www.zoho.com/zeptomail/')
  console.log('• Supabase Dashboard: https://supabase.com/dashboard')
  console.log('• Email Test Results: Check both SMTP and auth emails')

  const overallScore = [results.smtp_test, results.supabase_test, results.domain_check].filter(Boolean).length
  console.log(`\n🎯 Overall Score: ${overallScore}/3 ${overallScore === 3 ? '🎉' : overallScore >= 2 ? '👍' : '⚠️'}`)

  return results
}

// Run the checker
const email = process.argv[2] || '<EMAIL>'
checkEmailDeliverability(email)
  .then(() => {
    console.log('\n✅ Email deliverability check complete!')
  })
  .catch((error) => {
    console.error('\n❌ Check failed:', error)
    process.exit(1)
  })
