// Test business registrations access with service role
import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables from console app
dotenv.config({ path: './apps/console-app/.env.local' })

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

console.log('Testing business registrations access...')
console.log('Supabase URL:', supabaseUrl)
console.log('Service Role Key available:', !!serviceRoleKey)

if (!supabaseUrl || !serviceRoleKey) {
  console.error('Missing required environment variables')
  process.exit(1)
}

const adminClient = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function testAccess() {
  try {
    console.log('\n1. Testing connection...')
    const { count, error: testError } = await adminClient
      .from('business_registrations')
      .select('*', { count: 'exact', head: true })

    if (testError) {
      console.error('Connection test failed:', testError)
      return
    }

    console.log('✅ Connection successful, total registrations:', count)

    console.log('\n2. Fetching registrations...')
    const { data: leads, error: leadsError } = await adminClient
      .from('business_registrations')
      .select('*')
      .order('created_at', { ascending: false })

    if (leadsError) {
      console.error('❌ Error fetching registrations:', leadsError)
      return
    }

    console.log('✅ Successfully fetched registrations:', leads?.length || 0)
    
    if (leads && leads.length > 0) {
      console.log('\nSample registration:')
      console.log({
        id: leads[0].id,
        company_name: leads[0].company_name,
        contact_email: leads[0].contact_person_email,
        status: leads[0].status,
        created_at: leads[0].created_at
      })
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error)
  }
}

testAccess()
