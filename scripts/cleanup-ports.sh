#!/bin/bash

# ProcureServe II - Port Cleanup Utility
# Kills all processes on our development ports

echo "🧹 ProcureServe II Port Cleanup..."

# Color codes
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

# App ports
CUSTOMER_PORT=3004
CONSOLE_PORT=3008
CANDIDATE_PORT=3006

# Function to kill process on port
kill_port() {
    local port=$1
    local app_name=$2
    
    print_status "Checking port $port ($app_name)..."
    local pids=$(lsof -ti:$port 2>/dev/null)
    
    if [ ! -z "$pids" ]; then
        print_warning "Found processes on port $port: $pids"
        echo $pids | xargs kill -9 2>/dev/null
        sleep 1
        
        # Verify port is free
        if lsof -ti:$port >/dev/null 2>&1; then
            print_error "Failed to free port $port"
            return 1
        else
            print_success "Port $port freed successfully"
        fi
    else
        print_success "Port $port is already free"
    fi
    return 0
}

# Clean up all development ports
echo ""
print_status "Cleaning up ProcureServe II development ports..."
echo ""

kill_port $CUSTOMER_PORT "Customer App"
kill_port $CONSOLE_PORT "Console App"
kill_port $CANDIDATE_PORT "Candidate App"

# Clean up PID files if they exist
print_status "Removing PID files..."
rm -f .customer_pid .console_pid .candidate_pid

echo ""
print_success "✨ Port cleanup complete!"
echo ""
print_status "Safe to run:"
print_status "  ./scripts/start-dev.sh          (All 3 apps)"
print_status "  ./scripts/start-console-dev.sh  (Console focused)"
echo ""
