#!/usr/bin/env node

// <PERSON>rip<PERSON> to fix console_user_invitations table schema
// Run this script to add missing columns

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env') })

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY in environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function fixInvitationsTable() {
  console.log('🔧 Fixing console_user_invitations table schema...\n')

  try {
    // Read and execute the migration SQL
    const migrationSQL = `
      -- Add status column if it doesn't exist
      DO $$ 
      BEGIN
          IF NOT EXISTS (
              SELECT 1 
              FROM information_schema.columns 
              WHERE table_schema = 'public' 
              AND table_name = 'console_user_invitations'
              AND column_name = 'status'
          ) THEN
              ALTER TABLE console_user_invitations 
              ADD COLUMN status TEXT DEFAULT 'pending' 
              CHECK (status IN ('pending', 'accepted', 'expired', 'cancelled'));
          END IF;
      END $$;

      -- Add updated_at column if it doesn't exist
      DO $$ 
      BEGIN
          IF NOT EXISTS (
              SELECT 1 
              FROM information_schema.columns 
              WHERE table_schema = 'public' 
              AND table_name = 'console_user_invitations'
              AND column_name = 'updated_at'
          ) THEN
              ALTER TABLE console_user_invitations 
              ADD COLUMN updated_at TIMESTAMPTZ DEFAULT NOW();
          END IF;
      END $$;

      -- Create trigger to automatically update updated_at
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
      END;
      $$ language 'plpgsql';

      -- Drop trigger if exists and recreate
      DROP TRIGGER IF EXISTS update_console_user_invitations_updated_at ON console_user_invitations;
      CREATE TRIGGER update_console_user_invitations_updated_at 
      BEFORE UPDATE ON console_user_invitations 
      FOR EACH ROW 
      EXECUTE FUNCTION update_updated_at_column();
    `

    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL })
    
    if (error) {
      console.error('❌ Error applying migration:', error)
      
      // Try a simpler approach by running individual statements
      console.log('\n🔄 Trying alternative approach...')
      
      // Check current schema
      const { data: columns, error: schemaError } = await supabase
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_schema', 'public')
        .eq('table_name', 'console_user_invitations')
      
      if (schemaError) {
        console.error('❌ Error checking schema:', schemaError)
      } else {
        console.log('✅ Current columns:', columns?.map(c => c.column_name).join(', '))
      }
    } else {
      console.log('✅ Migration applied successfully!')
    }

    // Test the table by fetching invitations
    const { data: invitations, error: fetchError } = await supabase
      .from('console_user_invitations')
      .select('*')
      .eq('status', 'pending')
      .limit(5)

    if (fetchError) {
      console.error('❌ Error fetching invitations:', fetchError)
    } else {
      console.log(`\n✅ Successfully fetched ${invitations?.length || 0} pending invitations`)
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error)
  }
}

// Run the fix
fixInvitationsTable()
  .then(() => {
    console.log('\n✅ Script completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n❌ Script failed:', error)
    process.exit(1)
  }) 