// Test individual registration access
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://hfzhvrknjgwtrkgyinjf.supabase.co'
const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhmemh2cmtuamd3dHJrZ3lpbmpmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA2ODI4MiwiZXhwIjoyMDY1NjQ0MjgyfQ._scXNK1IgQuVok-iIBVxQYmcWntrI2VLInx1_Cz8knI'

const adminClient = createClient(supabaseUrl, serviceRoleKey, {
  auth: { autoRefreshToken: false, persistSession: false }
})

async function testIndividualAccess() {
  console.log('Testing individual registration access...')
  
  // Test with the known ID from the logs
  const testId = '40dd259a-ff66-4b40-8cea-651ea46e284c'
  
  try {
    console.log(`Fetching registration with ID: ${testId}`)
    
    const { data: lead, error: leadError } = await adminClient
      .from('business_registrations')
      .select('*')
      .eq('id', testId)
      .single()

    if (leadError) {
      console.error('❌ Database error:', leadError)
      return
    }

    if (!lead) {
      console.error('❌ No registration found')
      return
    }

    console.log('✅ Successfully fetched registration:')
    console.log({
      id: lead.id,
      company_name: lead.company_name,
      contact_email: lead.contact_person_email,
      status: lead.status,
      created_at: lead.created_at
    })

  } catch (error) {
    console.error('❌ Unexpected error:', error)
  }
}

testIndividualAccess()
