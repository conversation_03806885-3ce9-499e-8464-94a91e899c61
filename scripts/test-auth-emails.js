// Email Integration Test
// Test Supabase Auth with ZeptoMail SMTP
// Usage: node scripts/test-auth-emails.js

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

config()

async function testSupabaseAuthEmails() {
  console.log('🧪 Testing Supabase Auth with ZeptoMail SMTP...\n')
  
  try {
    // Initialize Supabase client
    const supabase = createClient(
      process.env.PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )

    console.log('⏳ Testing password reset email...')
    
    // Test password reset email
    const { data, error } = await supabase.auth.resetPasswordForEmail(
      '<EMAIL>',
      {
        redirectTo: `${process.env.CUSTOMER_APP_URL}/reset-password`
      }
    )

    if (error) {
      throw error
    }

    console.log('✅ Password reset email sent successfully!')
    console.log('📧 Email should be delivered via ZeptoMail SMTP')
    console.log('📬 Check the email inbox for the reset link\n')
    
    console.log('🎊 Supabase Auth integration with ZeptoMail is working!')
    console.log('💡 Users will now receive verification emails when registering')
    
  } catch (error) {
    console.error('❌ Auth email test failed:')
    console.error('Error:', error.message)
    
    console.log('\n🔧 Troubleshooting tips:')
    console.log('1. Verify Supabase project has SMTP configured')
    console.log('2. Check that email confirmations are enabled')
    console.log('3. Ensure the test email is valid and accessible')
    console.log('4. Verify ZeptoMail SMTP credentials are correct')
    
    process.exit(1)
  }
}

testSupabaseAuthEmails()
