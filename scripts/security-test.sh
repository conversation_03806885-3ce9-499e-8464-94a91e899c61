#!/bin/bash

# PSII Security Verification Script
# Tests all three applications for security compliance

echo "🔒 ProcureServe II Security Verification"
echo "========================================"
echo "Testing all three applications for security vulnerabilities..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
PASSED=0
FAILED=0
WARNINGS=0

# Function to print test results
print_result() {
    local test_name="$1"
    local status="$2"
    local details="$3"
    
    case $status in
        "PASS")
            echo -e "${GREEN}✅ PASS${NC}: $test_name"
            [ -n "$details" ] && echo -e "   $details"
            ((PASSED++))
            ;;
        "FAIL")
            echo -e "${RED}❌ FAIL${NC}: $test_name"
            [ -n "$details" ] && echo -e "   ${RED}$details${NC}"
            ((FAILED++))
            ;;
        "WARN")
            echo -e "${YELLOW}⚠️  WARN${NC}: $test_name"
            [ -n "$details" ] && echo -e "   ${YELLOW}$details${NC}"
            ((WARNINGS++))
            ;;
    esac
}

# Test 1: Check if all required security files exist
echo -e "${BLUE}📁 Testing File Structure Security${NC}"
echo "-----------------------------------"

# Customer App Security Files
test_file() {
    local file_path="$1"
    local description="$2"
    
    if [ -f "$file_path" ]; then
        print_result "$description" "PASS" "File exists: $file_path"
    else
        print_result "$description" "FAIL" "Missing file: $file_path"
    fi
}

# Customer App
test_file "apps/customer-app/src/routes/(app)/+layout.server.ts" "Customer App Authentication Guard"
test_file "apps/customer-app/src/lib/components/layout/SidebarContent.svelte" "Customer App Role-based Navigation"

# Console App  
test_file "apps/console-app/src/lib/server/auth/console-auth.ts" "Console App Authentication Manager"
test_file "apps/console-app/src/routes/companies/+page.server.ts" "Console App Route Protection"

# Candidate App (New)
test_file "apps/candidate-app/src/routes/(app)/+layout.server.ts" "Candidate App Authentication Guard"
test_file "apps/candidate-app/src/lib/components/layout/CandidateLayout.svelte" "Candidate App Secure Navigation"
test_file "apps/candidate-app/src/hooks.server.ts" "Candidate App Server Hooks"

echo ""

# Test 2: Check Supabase Configuration
echo -e "${BLUE}🔧 Testing Supabase Configuration${NC}"
echo "-----------------------------------"

if [ -f ".env" ]; then
    # Check for required environment variables
    if grep -q "PUBLIC_SUPABASE_URL=https://hfzhvrknjgwtrkgyinjf.supabase.co" .env; then
        print_result "Supabase Cloud URL Configuration" "PASS" "Using production Supabase Cloud"
    else
        print_result "Supabase Cloud URL Configuration" "FAIL" "Incorrect or missing Supabase URL"
    fi
    
    if grep -q "PUBLIC_SUPABASE_ANON_KEY=" .env; then
        print_result "Supabase Anonymous Key" "PASS" "Anonymous key configured"
    else
        print_result "Supabase Anonymous Key" "FAIL" "Missing anonymous key"
    fi
    
    if grep -q "SUPABASE_SERVICE_ROLE_KEY=" .env; then
        print_result "Supabase Service Role Key" "PASS" "Service role key configured"
    else
        print_result "Supabase Service Role Key" "WARN" "Service role key not found (may be optional)"
    fi
else
    print_result "Environment Configuration" "FAIL" "Missing .env file"
fi

echo ""

# Test 3: Check Application Port Configuration
echo -e "${BLUE}🌐 Testing Application Port Security${NC}"
echo "-------------------------------------"

check_port_config() {
    local app_name="$1"
    local expected_port="$2"
    local package_json_path="$3"
    
    if [ -f "$package_json_path" ]; then
        if grep -q "\"dev\": \".*--port $expected_port\"" "$package_json_path"; then
            print_result "$app_name Port Configuration" "PASS" "Configured for port $expected_port"
        else
            print_result "$app_name Port Configuration" "WARN" "Port configuration may be different"
        fi
    else
        print_result "$app_name Package Configuration" "FAIL" "Missing package.json"
    fi
}

check_port_config "Customer App" "3004" "apps/customer-app/package.json"
check_port_config "Console App" "3008" "apps/console-app/package.json" 
check_port_config "Candidate App" "3006" "apps/candidate-app/package.json"

echo ""

# Test 4: Authentication Implementation Check
echo -e "${BLUE}🔐 Testing Authentication Implementation${NC}"
echo "----------------------------------------"

# Check for proper auth patterns in code
check_auth_pattern() {
    local file_path="$1"
    local pattern="$2"
    local description="$3"
    
    if [ -f "$file_path" ]; then
        if grep -q "$pattern" "$file_path"; then
            print_result "$description" "PASS" "Pattern found in $file_path"
        else
            print_result "$description" "FAIL" "Auth pattern missing in $file_path"
        fi
    else
        print_result "$description" "FAIL" "File not found: $file_path"
    fi
}

# Customer App Auth Checks
check_auth_pattern "apps/customer-app/src/routes/(app)/+layout.server.ts" "redirect.*login" "Customer App Login Redirect"
check_auth_pattern "apps/customer-app/src/lib/components/layout/SidebarContent.svelte" "process_permissions" "Customer App Process Permissions"

# Console App Auth Checks  
check_auth_pattern "apps/console-app/src/lib/server/auth/console-auth.ts" "validatePermission" "Console App Permission Validation"
check_auth_pattern "apps/console-app/src/routes/companies/+page.server.ts" "validatePermission" "Console App Route Protection"

# Candidate App Auth Checks
check_auth_pattern "apps/candidate-app/src/routes/(app)/+layout.server.ts" "redirect.*login" "Candidate App Login Redirect"
check_auth_pattern "apps/candidate-app/src/lib/components/layout/CandidateLayout.svelte" "signOut" "Candidate App Secure Logout"

echo ""

# Test 5: Security Header and Configuration
echo -e "${BLUE}🛡️  Testing Security Headers${NC}"
echo "-------------------------------"

# Check for security-related configurations
check_security_config() {
    local file_path="$1"
    local pattern="$2" 
    local description="$3"
    
    if [ -f "$file_path" ]; then
        if grep -q "$pattern" "$file_path"; then
            print_result "$description" "PASS"
        else
            print_result "$description" "WARN" "Pattern not found, may need manual verification"
        fi
    fi
}

# Check for HTTPS enforcement patterns
check_security_config "apps/customer-app/src/lib/supabase.ts" "supabase.co" "Customer App HTTPS Configuration"
check_security_config "apps/console-app/src/lib/supabase.ts" "supabase.co" "Console App HTTPS Configuration"  
check_security_config "apps/candidate-app/src/lib/supabase.ts" "supabase.co" "Candidate App HTTPS Configuration"

echo ""

# Test 6: Check for hardcoded secrets (security anti-pattern)
echo -e "${BLUE}🔍 Testing for Security Anti-patterns${NC}"
echo "---------------------------------------"

check_no_secrets() {
    local search_path="$1"
    local app_name="$2"
    
    # Look for potential hardcoded secrets (excluding .env files)
    if find "$search_path" -name "*.ts" -o -name "*.js" -o -name "*.svelte" | xargs grep -l "eyJ" 2>/dev/null | grep -v ".env" >/dev/null; then
        print_result "$app_name Hardcoded Secrets Check" "FAIL" "Potential hardcoded JWT tokens found"
    else
        print_result "$app_name Hardcoded Secrets Check" "PASS" "No hardcoded secrets detected"
    fi
}

check_no_secrets "apps/customer-app/src" "Customer App"
check_no_secrets "apps/console-app/src" "Console App"
check_no_secrets "apps/candidate-app/src" "Candidate App"

echo ""

# Summary
echo -e "${BLUE}📊 Security Test Summary${NC}"
echo "========================"
echo -e "✅ Passed: ${GREEN}$PASSED${NC}"
echo -e "❌ Failed: ${RED}$FAILED${NC}"
echo -e "⚠️  Warnings: ${YELLOW}$WARNINGS${NC}"
echo ""

if [ $FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 Security Status: READY FOR PRODUCTION${NC}"
    echo -e "${GREEN}All critical security checks passed!${NC}"
    exit 0
elif [ $FAILED -le 2 ]; then
    echo -e "${YELLOW}⚠️  Security Status: MINOR ISSUES DETECTED${NC}"
    echo -e "${YELLOW}Review failed tests and address before production deployment.${NC}"
    exit 1
else
    echo -e "${RED}🚨 Security Status: CRITICAL ISSUES DETECTED${NC}"
    echo -e "${RED}Multiple security failures detected. DO NOT DEPLOY to production.${NC}"
    exit 2
fi
