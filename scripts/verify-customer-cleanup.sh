#!/bin/bash

# Customer App Cleanup Verification Script
echo "🔍 Verifying Customer App Cleanup..."

cd /Users/<USER>/Desktop/PSII/apps/customer-app

echo "✅ Checking removed candidate files are gone..."
echo "- Candidate API endpoints:"
test ! -d "src/routes/api/register/candidate" && echo "  ✅ candidate registration API removed" || echo "  ❌ candidate registration API still exists"
test ! -d "src/routes/api/profile/complete" && echo "  ✅ profile complete API removed" || echo "  ❌ profile complete API still exists"
test ! -d "src/routes/api/profile/update" && echo "  ✅ profile update API removed" || echo "  ❌ profile update API still exists"

echo "- Candidate routes:"
test ! -d "src/routes/complete-profile" && echo "  ✅ complete-profile route removed" || echo "  ❌ complete-profile route still exists"
test ! -d "src/routes/profile" && echo "  ✅ profile edit route removed" || echo "  ❌ profile edit route still exists"

echo "- Candidate components:"
test ! -d "src/lib/components/profile" && echo "  ✅ profile components removed" || echo "  ❌ profile components still exist"

echo ""
echo "✅ Checking business functionality is intact..."
echo "- Core business routes:"
test -d "src/routes/recruitment" && echo "  ✅ recruitment routes exist" || echo "  ❌ recruitment routes missing"
test -d "src/routes/bench-sales" && echo "  ✅ bench-sales routes exist" || echo "  ❌ bench-sales routes missing"
test -d "src/routes/register/business" && echo "  ✅ business registration exists" || echo "  ❌ business registration missing"
test -d "src/routes/login" && echo "  ✅ login route exists" || echo "  ❌ login route missing"

echo "- Core business components:"
test -d "src/lib/components/layout" && echo "  ✅ layout components exist" || echo "  ❌ layout components missing"
test -d "src/lib/components/ui" && echo "  ✅ UI components exist" || echo "  ❌ UI components missing"
test -d "src/lib/components/jobs" && echo "  ✅ jobs components exist" || echo "  ❌ jobs components missing"

echo ""
echo "✅ Checking cleanup documentation..."
test -f "../../docs/cleanup/completion-report.md" && echo "  ✅ completion report exists" || echo "  ❌ completion report missing"
test -d "../../docs/cleanup/removed-candidate-api" && echo "  ✅ removed files backed up" || echo "  ❌ removed files not backed up"

echo ""
echo "🎉 Customer App Cleanup Verification Complete!"
echo ""
echo "Next Steps:"
echo "1. Test business registration: npm run dev"
echo "2. Test business login flow"
echo "3. Test recruitment dashboard access"
echo "4. Test bench sales dashboard access"
echo "5. Begin candidate-app development"
