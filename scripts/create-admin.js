#!/usr/bin/env node

/**
 * Direct Admin User Creation Script
 * Creates admin user directly in Supabase Auth and console_users table
 */

import { createClient } from '@supabase/supabase-js'
import { config } from 'dotenv'
import { join } from 'path'
import { randomUUID } from 'crypto'

// Load environment variables with proper hierarchy
config({ path: join(process.cwd(), '.env.local') })
config({ path: join(process.cwd(), '.env') })

const SUPABASE_URL = process.env.PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY

// Get admin credentials from environment variables for security
// In production, these should be set in your deployment platform (Vercel, etc.)
const ADMIN_EMAIL = process.env.CONSOLE_ADMIN_EMAIL
const ADMIN_PASSWORD = process.env.CONSOLE_ADMIN_PASSWORD

// Detect environment
const NODE_ENV = process.env.NODE_ENV || 'development'
const isProduction = NODE_ENV === 'production'

// Validate password strength
function validatePassword(password) {
  if (!password) return false
  if (password.length < 12) return false
  if (!/[A-Z]/.test(password)) return false
  if (!/[a-z]/.test(password)) return false
  if (!/[0-9]/.test(password)) return false
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) return false
  return true
}

async function createAdminUser() {
  console.log('🔧 Creating Console Admin User')
  console.log('===============================\n')

  // Validate environment variables
  if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
    console.error('❌ Missing Supabase configuration')
    console.error('   Required: PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
    process.exit(1)
  }

  if (!ADMIN_EMAIL) {
    console.error('❌ Missing CONSOLE_ADMIN_EMAIL environment variable')
    console.error('   Required for both development and production')
    process.exit(1)
  }

  if (!ADMIN_PASSWORD) {
    console.error('❌ Missing CONSOLE_ADMIN_PASSWORD environment variable')
    if (isProduction) {
      console.error('   Set in your deployment platform (Vercel, Railway, etc.)')
    } else {
      console.error('   Set in .env.local: CONSOLE_ADMIN_PASSWORD=YourStrongPassword123!')
    }
    process.exit(1)
  }

  if (!validatePassword(ADMIN_PASSWORD)) {
    console.error('❌ Password does not meet security requirements:')
    console.error('   • Minimum 12 characters')
    console.error('   • At least 1 uppercase letter (A-Z)')
    console.error('   • At least 1 lowercase letter (a-z)')
    console.error('   • At least 1 number (0-9)')
    console.error('   • At least 1 special character (!@#$%^&*...)')
    process.exit(1)
  }

  console.log('✅ Environment validation passed')
  console.log(`📧 Admin Email: ${ADMIN_EMAIL}`)
  console.log('🔒 Password meets security requirements\n')

  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })

  try {
    // Step 1: Check if auth user already exists
    console.log('1️⃣ Checking for existing Auth user...')
    const { data: existingAuthUsers } = await supabase.auth.admin.listUsers()
    const existingAuthUser = existingAuthUsers?.users?.find(u => u.email === ADMIN_EMAIL)
    
    let authUserId

    if (existingAuthUser) {
      console.log('   ✅ Auth user already exists:', existingAuthUser.id)
      authUserId = existingAuthUser.id
    } else {
      // Create auth user
      console.log('   🔨 Creating Auth user...')
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD,
        email_confirm: true,
        user_metadata: {
          created_via: 'admin_script',
          role: 'super_admin'
        }
      })

      if (authError || !authData.user) {
        console.error('   ❌ Failed to create Auth user:', authError)
        process.exit(1)
      }

      authUserId = authData.user.id
      console.log('   ✅ Auth user created:', authUserId)
    }

    // Step 2: Check if console user exists
    console.log('\n2️⃣ Checking for existing Console user...')
    const { data: existingConsoleUser } = await supabase
      .from('console_users')
      .select('*')
      .eq('email', ADMIN_EMAIL)
      .single()

    if (existingConsoleUser) {
      console.log('   ✅ Console user already exists:', existingConsoleUser.id)
    } else {
      // Create console user
      console.log('   🔨 Creating Console user...')
      const { error: consoleError } = await supabase
        .from('console_users')
        .insert({
          id: authUserId,
          email: ADMIN_EMAIL,
          role: 'super_admin',
          company_ids: ['procureserve-internal'],
          is_active: true,
          mfa_enabled: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (consoleError) {
        console.error('   ❌ Failed to create Console user:', consoleError)
        process.exit(1)
      }
      console.log('   ✅ Console user created')
    }

    // Step 3: Create/update permissions
    console.log('\n3️⃣ Setting up permissions...')
    
    // Delete existing permissions first
    await supabase
      .from('console_user_permissions')
      .delete()
      .eq('user_id', authUserId)

    const permissions = [
      {
        user_id: authUserId,
        resource: 'console_users',
        actions: ['create', 'read', 'update', 'delete', 'invite', 'manage'],
        company_id: null
      },
      {
        user_id: authUserId,
        resource: 'companies',
        actions: ['create', 'read', 'update', 'delete', 'manage'],
        company_id: null
      },
      {
        user_id: authUserId,
        resource: 'enums',
        actions: ['create', 'read', 'update', 'delete', 'manage'],
        company_id: null
      },
      {
        user_id: authUserId,
        resource: 'settings',
        actions: ['read', 'update', 'manage'],
        company_id: null
      },
      {
        user_id: authUserId,
        resource: 'audit_logs',
        actions: ['read'],
        company_id: null
      },
      {
        user_id: authUserId,
        resource: 'analytics',
        actions: ['read'],
        company_id: null
      }
    ]

    const { error: permissionsError } = await supabase
      .from('console_user_permissions')
      .insert(permissions)

    if (permissionsError) {
      console.error('   ❌ Failed to create permissions:', permissionsError)
    } else {
      console.log('   ✅ Permissions created')
    }

    // Step 4: Log security event
    console.log('\n4️⃣ Logging security event...')
    const { error: securityLogError } = await supabase
      .from('console_security_events')
      .insert({
        id: randomUUID(),
        event_type: 'initial_setup_completed',
        user_id: authUserId,
        user_email: ADMIN_EMAIL,
        user_role: 'super_admin',
        success: true,
        metadata: {
          created_via: 'admin_script',
          timestamp: new Date().toISOString()
        },
        timestamp: new Date().toISOString()
      })

    if (securityLogError) {
      console.error('   ⚠️ Failed to log security event:', securityLogError)
    } else {
      console.log('   ✅ Security event logged')
    }

    // Success summary
    console.log('\n🎉 Console Admin User Creation Complete!')
    console.log('==========================================')
    console.log(`📧 Email: ${ADMIN_EMAIL}`)
    console.log(`👤 User ID: ${authUserId}`)
    console.log(`🌐 Login URL: http://localhost:3008/login`)
    console.log('\n✅ Next Steps:')
    console.log('   • Login with your admin credentials')
    console.log('   • Remove CONSOLE_ADMIN_PASSWORD from .env.local after first login')
    console.log('   • Change password in console settings for enhanced security')
    console.log('   • Test all console functionality')
    console.log('\n🔒 Security Notes:')
    console.log('   • Password meets enterprise security requirements')
    console.log('   • All actions are logged in console_security_events')
    console.log('   • Admin has super_admin role with full permissions')

  } catch (error) {
    console.error('❌ Script failed:', error)
    process.exit(1)
  }
}

createAdminUser().catch(console.error)