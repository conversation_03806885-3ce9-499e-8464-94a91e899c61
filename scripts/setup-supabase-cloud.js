#!/usr/bin/env node

/**
 * ProcureServe II - Supabase Cloud Setup Verification
 * Verifies cloud configuration and connectivity for hfzhvrknjgwtrkgyinjf project
 */

import https from 'https';
import fs from 'fs';

// Colors for output
const colors = {
  green: '\x1b[32m',
  blue: '\x1b[34m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  purple: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(color, label, message) {
  console.log(`${color}[${label}]${colors.reset} ${message}`);
}

function success(message) { log(colors.green, 'SUCCESS', message); }
function info(message) { log(colors.blue, 'INFO', message); }
function error(message) { log(colors.red, 'ERROR', message); }
function warning(message) { log(colors.yellow, 'WARNING', message); }

const EXPECTED_PROJECT_ID = 'hfzhvrknjgwtrkgyinjf';
const EXPECTED_URL = `https://${EXPECTED_PROJECT_ID}.supabase.co`;

async function verifySupabaseConnection(url, key) {
  return new Promise((resolve) => {
    const testUrl = `${url}/rest/v1/`;
    const options = {
      method: 'GET',
      headers: {
        'apikey': key,
        'Authorization': `Bearer ${key}`,
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(testUrl, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          success: res.statusCode === 200,
          status: res.statusCode,
          response: data
        });
      });
    });

    req.on('error', (err) => resolve({ success: false, error: err.message }));
    req.setTimeout(5000, () => resolve({ success: false, error: 'Timeout' }));
    req.end();
  });
}

function loadEnvFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const env = {};
    content.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && !key.startsWith('#') && valueParts.length > 0) {
        env[key.trim()] = valueParts.join('=').trim();
      }
    });
    return env;
  } catch (err) {
    return null;
  }
}

async function main() {
  console.log('🌐 ProcureServe II - Supabase Cloud Verification');
  console.log(`🎯 Target Project: ${EXPECTED_PROJECT_ID}\n`);

  // Check root .env
  info('Checking root environment configuration...');
  const rootEnv = loadEnvFile('.env');
  if (!rootEnv) {
    error('Root .env file not found');
    return;
  }

  // Verify correct project
  if (!rootEnv.PUBLIC_SUPABASE_URL?.includes(EXPECTED_PROJECT_ID)) {
    error(`Root .env not configured for project ${EXPECTED_PROJECT_ID}`);
    error(`Found: ${rootEnv.PUBLIC_SUPABASE_URL}`);
    error(`Expected: ${EXPECTED_URL}`);
    return;
  }

  success(`Root .env configured for project ${EXPECTED_PROJECT_ID}`);

  // Verify anon key format
  if (!rootEnv.PUBLIC_SUPABASE_ANON_KEY?.startsWith('eyJ')) {
    error('Invalid anon key format in root .env');
    return;
  }

  success('Valid anon key format detected');

  // Check app configurations
  const apps = [
    { name: 'Customer App', path: 'apps/customer-app/.env.local', port: '3004' },
    { name: 'Console App', path: 'apps/console-app/.env.local', port: '3008' },
    { name: 'Candidate App', path: 'apps/candidate-app/.env.local', port: '3006' }
  ];

  let allAppsConfigured = true;

  for (const app of apps) {
    info(`Checking ${app.name} configuration...`);
    const appEnv = loadEnvFile(app.path);
    
    if (!appEnv) {
      error(`${app.name} .env.local not found`);
      allAppsConfigured = false;
      continue;
    }

    if (!appEnv.PUBLIC_SUPABASE_URL?.includes(EXPECTED_PROJECT_ID)) {
      error(`${app.name} not configured for project ${EXPECTED_PROJECT_ID}`);
      allAppsConfigured = false;
      continue;
    }

    // Check port configuration
    const expectedPort = `http://localhost:${app.port}`;
    if (appEnv.PUBLIC_APP_URL && !appEnv.PUBLIC_APP_URL.includes(app.port)) {
      warning(`${app.name} port mismatch. Expected: ${expectedPort}`);
    }

    success(`${app.name} configured correctly for project ${EXPECTED_PROJECT_ID}`);
  }

  // Test Supabase connectivity
  info('Testing Supabase Cloud connectivity...');
  const connectionResult = await verifySupabaseConnection(
    rootEnv.PUBLIC_SUPABASE_URL,
    rootEnv.PUBLIC_SUPABASE_ANON_KEY
  );

  if (connectionResult.success) {
    success('Supabase Cloud connection successful!');
    success(`Response status: ${connectionResult.status}`);
  } else {
    error('Unable to connect to Supabase Cloud');
    if (connectionResult.error) {
      error(`Error: ${connectionResult.error}`);
    }
    if (connectionResult.status) {
      error(`HTTP Status: ${connectionResult.status}`);
    }
    warning('Please verify your credentials and network connection');
  }

  // Verify required keys exist
  info('Checking required environment variables...');
  const requiredKeys = ['PUBLIC_SUPABASE_URL', 'PUBLIC_SUPABASE_ANON_KEY', 'SUPABASE_SERVICE_ROLE_KEY'];
  let allKeysPresent = true;

  for (const key of requiredKeys) {
    if (rootEnv[key]) {
      success(`✓ ${key} present`);
    } else {
      error(`✗ ${key} missing`);
      allKeysPresent = false;
    }
  }

  // Summary
  console.log('\n📊 Cloud Configuration Summary:');
  console.log(`${colors.blue}Project ID:${colors.reset}   ${EXPECTED_PROJECT_ID}`);
  console.log(`${colors.blue}Supabase URL:${colors.reset} ${rootEnv.PUBLIC_SUPABASE_URL}`);
  console.log(`${colors.cyan}Customer App:${colors.reset} http://localhost:3004`);
  console.log(`${colors.purple}Console App:${colors.reset}  http://localhost:3008`);
  console.log(`${colors.green}Candidate App:${colors.reset} http://localhost:3006`);
  console.log(`${colors.yellow}Environment:${colors.reset}  Cloud (Production Ready)`);

  console.log('\n🎯 Quick Links:');
  console.log(`${colors.blue}Dashboard:${colors.reset} https://supabase.com/dashboard/project/${EXPECTED_PROJECT_ID}`);
  console.log(`${colors.blue}Database:${colors.reset}  https://supabase.com/dashboard/project/${EXPECTED_PROJECT_ID}/editor`);
  console.log(`${colors.blue}Auth:${colors.reset}      https://supabase.com/dashboard/project/${EXPECTED_PROJECT_ID}/auth/users`);

  console.log('\n✨ Setup Status:');
  if (connectionResult.success && allAppsConfigured && allKeysPresent) {
    success('🎉 All systems configured and connected!');
    console.log('\n🚀 Ready to run: ./scripts/start-dev.sh');
  } else {
    warning('⚠️  Some configuration issues detected');
    console.log('\n🔧 Review the errors above and update your configuration');
  }
}

main().catch(console.error);
