#!/bin/bash

# ProcureServe II - Console Development with Smart Port Management
# For Console App development with Customer App for testing

echo "🎛️  Starting ProcureServe II Console Development..."

# Color codes
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_console() { echo -e "${PURPLE}[CONSOLE]${NC} $1"; }
print_customer() { echo -e "${CYAN}[CUSTOMER]${NC} $1"; }

# Port configuration
CONSOLE_PORT=3008
CUSTOMER_PORT=3004

# Function to kill process on port
kill_port() {
    local port=$1
    local app_name=$2
    local pids=$(lsof -ti:$port 2>/dev/null)
    
    if [ ! -z "$pids" ]; then
        print_warning "Port $port occupied. Killing processes: $pids"
        echo $pids | xargs kill -9 2>/dev/null
        sleep 2
        
        if lsof -ti:$port >/dev/null 2>&1; then
            print_error "Failed to free port $port"
            return 1
        else
            print_success "Port $port freed"
        fi
    fi
    return 0
}

# Function to wait for app
wait_for_app() {
    local port=$1
    local app_name=$2
    local max_attempts=20
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://localhost:$port" >/dev/null 2>&1; then
            print_success "$app_name running at http://localhost:$port"
            return 0
        fi
        sleep 1
        attempt=$((attempt + 1))
    done
    
    print_warning "$app_name may still be starting"
    return 1
}

# Verify cloud configuration
if ! grep -q "hfzhvrknjgwtrkgyinjf.supabase.co" .env; then
    print_error "Supabase Cloud configuration not found"
    exit 1
fi

# Free up ports
print_status "Managing ports..."
kill_port $CONSOLE_PORT "Console App" || exit 1
kill_port $CUSTOMER_PORT "Customer App" || exit 1

# Install dependencies
print_status "Installing dependencies..."
cd apps/console-app && npm install >/dev/null 2>&1
cd ../customer-app && npm install >/dev/null 2>&1  
cd ../..

# Start Console App (primary focus)
print_console "Starting Console App on port $CONSOLE_PORT..."
cd apps/console-app
npm run dev -- --port $CONSOLE_PORT --host 0.0.0.0 >/dev/null 2>&1 &
CONSOLE_PID=$!
cd ../..

# Start Customer App (for testing)
print_customer "Starting Customer App on port $CUSTOMER_PORT..."
cd apps/customer-app
npm run dev -- --port $CUSTOMER_PORT --host 0.0.0.0 >/dev/null 2>&1 &
CUSTOMER_PID=$!
cd ../..

# Wait and verify
wait_for_app $CONSOLE_PORT "Console App"
wait_for_app $CUSTOMER_PORT "Customer App"

echo ""
print_success "✨ Console Development Environment Ready!"
echo ""
print_console "🎛️  Console App (Primary): http://localhost:$CONSOLE_PORT"
print_customer "📱 Customer App (Testing): http://localhost:$CUSTOMER_PORT"
echo ""
print_status "🌐 Supabase Cloud: https://hfzhvrknjgwtrkgyinjf.supabase.co"
print_status "🎛️  Dashboard: https://supabase.com/dashboard/project/hfzhvrknjgwtrkgyinjf"
print_status "🎯 Focus: Console app configuration, settings, user management"
print_status "📋 Test: Use Customer app to verify business user flows"
echo ""
print_warning "Press Ctrl+C to stop all services"
echo ""

# Store PIDs
echo "$CONSOLE_PID" > .console_pid
echo "$CUSTOMER_PID" > .customer_pid

# Cleanup function
cleanup() {
    print_status "Shutting down..."
    kill $CONSOLE_PID $CUSTOMER_PID 2>/dev/null
    kill_port $CONSOLE_PORT "Console App" >/dev/null 2>&1
    kill_port $CUSTOMER_PORT "Customer App" >/dev/null 2>&1
    rm -f .console_pid .customer_pid
    print_success "Console development stopped"
    exit 0
}

trap cleanup INT

# Monitor
while true; do
    sleep 10
    if ! kill -0 $CONSOLE_PID 2>/dev/null; then
        print_error "Console App stopped"
        cleanup
    fi
    if ! kill -0 $CUSTOMER_PID 2>/dev/null; then
        print_error "Customer App stopped"
        cleanup
    fi
done
