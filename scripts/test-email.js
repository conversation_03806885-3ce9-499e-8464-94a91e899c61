// Email Test Utility for PSII
// File: scripts/test-email.js
// Usage: node scripts/test-email.js

import { config } from 'dotenv'
import nodemailer from 'nodemailer'

config() // Load environment variables

async function testZeptoMailSMTP() {
  console.log('🧪 Testing ZeptoMail SMTP Configuration...\n')
  
  try {
    // Create transporter with ZeptoMail SMTP settings
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || "smtp.zeptomail.in",
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: false, // Use STARTTLS
      auth: {
        user: process.env.SMTP_USER || "emailapikey",
        pass: process.env.SMTP_PASS
      }
    })

    // Verify connection
    console.log('⏳ Verifying SMTP connection...')
    await transporter.verify()
    console.log('✅ SMTP connection verified successfully!\n')

    // Send test email
    const testEmail = {
      from: `"${process.env.EMAIL_FROM_NAME || 'ProcureServe Team'}" <${process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'}>`,
      to: '<EMAIL>', // Test email address
      subject: 'PSII Email Configuration Test',
      html: `
        <h2>🎉 Email Configuration Successful!</h2>
        <p>This is a test email to confirm that your ZeptoMail SMTP configuration is working correctly.</p>
        <p><strong>Configuration Details:</strong></p>
        <ul>
          <li>SMTP Host: ${process.env.SMTP_HOST}</li>
          <li>SMTP Port: ${process.env.SMTP_PORT}</li>
          <li>From: ${process.env.EMAIL_FROM_ADDRESS}</li>
          <li>From Name: ${process.env.EMAIL_FROM_NAME}</li>
        </ul>
        <p>Sent at: ${new Date().toISOString()}</p>
        <p>Best regards,<br>The ProcureServe II Team</p>
      `,
      text: `
        Email Configuration Successful!
        
        This is a test email to confirm that your ZeptoMail SMTP configuration is working correctly.
        
        Configuration Details:
        - SMTP Host: ${process.env.SMTP_HOST}
        - SMTP Port: ${process.env.SMTP_PORT}  
        - From: ${process.env.EMAIL_FROM_ADDRESS}
        - From Name: ${process.env.EMAIL_FROM_NAME}
        
        Sent at: ${new Date().toISOString()}
        
        Best regards,
        The ProcureServe II Team
      `
    }

    console.log('📧 Sending test email...')
    const info = await transporter.sendMail(testEmail)
    
    console.log('✅ Test email sent successfully!')
    console.log(`📬 Message ID: ${info.messageId}`)
    console.log(`📤 Email sent to: ${testEmail.to}`)
    console.log(`📝 Subject: ${testEmail.subject}\n`)
    
    console.log('🎊 ZeptoMail SMTP configuration is working perfectly!')
    console.log('💡 You can now register users and they will receive verification emails.')
    
  } catch (error) {
    console.error('❌ Email test failed:')
    console.error('Error:', error.message)
    
    if (error.code) {
      console.error('Error Code:', error.code)
    }
    
    console.log('\n🔧 Troubleshooting tips:')
    console.log('1. Check that SMTP_PASS is correctly set in .env file')
    console.log('2. Verify the ZeptoMail API key is valid and active')
    console.log('3. Ensure the from email domain is verified in ZeptoMail')
    console.log('4. Check firewall settings allow SMTP traffic on port 587')
    
    process.exit(1)
  }
}

// Run the test
testZeptoMailSMTP()
