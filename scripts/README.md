# ProcureServe II - Scripts Directory

## 🧹 Cleaned and Optimized for Supabase Cloud

This directory contains essential development scripts for ProcureServe II's cloud-first architecture with smart port management.

## Available Scripts

### 🚀 Development Startup

#### `start-dev.sh` ⭐ **Main Development Script**
**Starts all 3 applications with smart port management**
```bash
./scripts/start-dev.sh
```
**Features:**
- ✅ **Smart Port Management** - Automatically kills conflicting processes
- ✅ **Customer App** (Business) → http://localhost:3004
- ✅ **Console App** (Internal) → http://localhost:3008  
- ✅ **Candidate App** (Jobs) → http://localhost:3006
- ✅ **Automatic Dependency Installation** for all apps
- ✅ **Health Checks** with startup verification
- ✅ **Real-time Monitoring** with graceful shutdown
- ✅ **Supabase Cloud Verification** before startup

#### `start-console-dev.sh`
**Console-focused development** - For console app development with customer app for testing
```bash
./scripts/start-console-dev.sh
```
**Features:**
- 🎛️ **Console App** (Primary) → http://localhost:3008
- 📱 **Customer App** (Testing) → http://localhost:3004
- ⚡ **Faster startup** - Only 2 apps
- 🎯 **Focused workflow** for console development

#### `cleanup-ports.sh`
**Port cleanup utility** - Kills all processes on development ports
```bash
./scripts/cleanup-ports.sh
```
**Features:**
- 🧹 **Cleans ports** 3004, 3008, 3006
- 🔍 **Smart detection** of conflicting processes
- 💪 **Force kill** stubborn processes
- 📝 **Clear reporting** of actions taken

### 🌐 Cloud Configuration

#### `setup-supabase-cloud.js`
**Cloud setup verification** - Comprehensive Supabase cloud validation
```bash
node scripts/setup-supabase-cloud.js
```
**Features:**
- ✅ **Project verification** for hfzhvrknjgwtrkgyinjf
- ✅ **Connectivity testing** with real API calls
- ✅ **Environment validation** across all 3 apps
- ✅ **Key verification** (anon, service role)
- ✅ **Quick links** to Supabase dashboard
- ✅ **Port configuration** validation

## Smart Port Management

### How It Works
All startup scripts now include intelligent port management:

1. **Detection** - Scans ports 3004, 3008, 3006 for existing processes
2. **Termination** - Forcefully kills conflicting processes with `kill -9`
3. **Verification** - Confirms ports are actually freed
4. **Startup** - Launches apps with explicit port assignment
5. **Monitoring** - Continuously monitors app health
6. **Cleanup** - Graceful shutdown on Ctrl+C

### Port Assignments
```
📱 Customer App:  3004  (Business users)
🎛️ Console App:   3008  (ProcureServe internal)  
👥 Candidate App: 3006  (Job seekers)
```

### Troubleshooting Ports
If you encounter port conflicts:
```bash
# Manual cleanup
./scripts/cleanup-ports.sh

# Check what's using a port
lsof -ti:3004

# Kill specific process
kill -9 <PID>
```

## Supabase Cloud Architecture

### Current Configuration
- **Project ID:** `hfzhvrknjgwtrkgyinjf`
- **URL:** `https://hfzhvrknjgwtrkgyinjf.supabase.co`
- **Environment:** Production-ready cloud instance
- **Region:** Auto-selected optimal region

### Services Used
- ✅ **Database:** PostgreSQL with pgvector extension
- ✅ **Auth:** JWT-based authentication with RLS
- ✅ **Storage:** File uploads and management
- ✅ **Real-time:** Live subscriptions and notifications
- ✅ **Email:** Auth emails via Supabase
- ✅ **API:** Auto-generated REST and GraphQL

### Dashboard Links
- 🎛️ **Main Dashboard:** https://supabase.com/dashboard/project/hfzhvrknjgwtrkgyinjf
- 📊 **Database Editor:** https://supabase.com/dashboard/project/hfzhvrknjgwtrkgyinjf/editor
- 👥 **Auth Users:** https://supabase.com/dashboard/project/hfzhvrknjgwtrkgyinjf/auth/users
- 📁 **Storage:** https://supabase.com/dashboard/project/hfzhvrknjgwtrkgyinjf/storage/buckets
- 📈 **Analytics:** https://supabase.com/dashboard/project/hfzhvrknjgwtrkgyinjf/analytics

## Usage Examples

### Daily Development Workflow
```bash
# 1. Verify cloud setup
node scripts/setup-supabase-cloud.js

# 2. Start all apps for full testing
./scripts/start-dev.sh

# 3. Open in browser
open http://localhost:3004  # Customer App
open http://localhost:3008  # Console App  
open http://localhost:3006  # Candidate App
```

### Console Development Focus
```bash
# Start console-focused environment
./scripts/start-console-dev.sh

# Work on console features
open http://localhost:3008

# Test with customer app  
open http://localhost:3004
```

### Port Issues Resolution
```bash
# Clean up all ports
./scripts/cleanup-ports.sh

# Verify ports are free
lsof -ti:3004 -ti:3008 -ti:3006

# Start fresh
./scripts/start-dev.sh
```

## Supabase MCP Integration (Optional)

### What is Supabase MCP?
The **Model Context Protocol** allows AI tools like Claude to directly interact with your Supabase database through natural language commands.

### Setup Instructions
1. **Generate Personal Access Token**
   - Go to https://supabase.com/dashboard/account/tokens
   - Create token named "Claude MCP Server"
   - Copy the token

2. **Configure Claude Desktop** (if available)
   ```json
   {
     "mcpServers": {
       "supabase": {
         "command": "npx",
         "args": [
           "-y",
           "@supabase/mcp-server-supabase@latest",
           "--access-token",
           "your_personal_access_token_here"
         ]
       }
     }
   }
   ```

3. **Available MCP Tools**
   - `execute_sql` - Run SQL queries
   - `list_tables` - View database schema
   - `create_project` - Manage Supabase projects
   - `get_project_url` - Fetch configuration
   - `generate_typescript_types` - Generate types
   - `search_docs` - Search Supabase documentation

### MCP Benefits
- 🤖 **AI Database Management** - Let Claude manage your database
- 📝 **Natural Language SQL** - Describe queries in plain English
- 🔄 **Schema Evolution** - AI-assisted database design
- 📊 **Data Analysis** - Quick insights and reporting

## Cleanup History

### Scripts Removed (90+ files)
- ❌ **Docker-related scripts** - Cloud-first approach
- ❌ **Local Supabase management** - Using cloud instance
- ❌ **Phase-specific tests** - Legacy development artifacts
- ❌ **Port management utilities** - Built into main scripts
- ❌ **Authentication debugging** - Issues resolved
- ❌ **Manual setup scripts** - Automated in main scripts

### Benefits of Cleanup
- 🚀 **95% fewer scripts** to maintain and understand
- 🌐 **Cloud-first** - No local complexity or Docker issues
- ⚡ **Faster development** - Smart automation built-in
- 🎯 **Clear purpose** - Each script has specific, current use
- 📚 **Better documentation** - Focused on current needs
- 🔧 **Production-ready** - Same environment dev to prod

## Environment Variables

### Required in All Apps
```bash
# Supabase Cloud Configuration
PUBLIC_SUPABASE_URL=https://hfzhvrknjgwtrkgyinjf.supabase.co
PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# App-specific
PUBLIC_APP_URL=http://localhost:PORT
PUBLIC_APP_PORT=PORT
NODE_ENV=development
DEBUG_AUTH=true
SUPABASE_ENVIRONMENT=cloud
SKIP_LOCAL_MIGRATIONS=true
```

### File Locations
- 📄 **Root:** `.env` (shared configuration)
- 📱 **Customer:** `apps/customer-app/.env.local`
- 🎛️ **Console:** `apps/console-app/.env.local`
- 👥 **Candidate:** `apps/candidate-app/.env.local`

## Production Deployment

### Vercel Configuration
The same environment variables work seamlessly with Vercel:

1. **Customer App** → `app.procureserve.com`
2. **Console App** → `console.procureserve.com`  
3. **Candidate App** → `candidates.procureserve.com`

### Environment Promotion
- ✅ **Development:** Current Supabase cloud (hfzhvrknjgwtrkgyinjf)
- 🚀 **Production:** Separate Supabase project (when ready)
- 🔄 **CI/CD:** Automatic deployment via Vercel + GitHub

## Support

### Common Issues
1. **Port conflicts** → Run `./scripts/cleanup-ports.sh`
2. **Supabase connection** → Run `node scripts/setup-supabase-cloud.js`
3. **Dependencies** → Scripts auto-install, but `npm install` in each app if needed
4. **Environment** → Check `.env.local` files match project ID

### Getting Help
- 📚 **Supabase Docs:** https://supabase.com/docs
- 🎛️ **Dashboard:** https://supabase.com/dashboard/project/hfzhvrknjgwtrkgyinjf
- 🔧 **Scripts:** All scripts include verbose logging and error reporting

---

**Status: ✅ PRODUCTION READY**
Clean, cloud-first development environment with smart automation and zero local dependencies.
