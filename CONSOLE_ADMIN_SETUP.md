# Console Admin Setup Guide

## 🔒 Secure Admin User Creation

### Prerequisites
1. **Supabase Cloud** configured with environment variables
2. **Strong password** prepared (12+ chars, mixed case, numbers, symbols)

### Step 1: Set Environment Variables

#### For Development (`.env.local`):
```bash
# Console Admin Credentials (for setup only - DO NOT COMMIT)
CONSOLE_ADMIN_EMAIL=<EMAIL>
CONSOLE_ADMIN_PASSWORD=YourVeryStrongPassword123!@#
```

#### For Production (Deployment Platform):
Set these in your deployment platform (Vercel, Railway, etc.):
- `CONSOLE_ADMIN_EMAIL`
- `CONSOLE_ADMIN_PASSWORD`
- `PUBLIC_SUPABASE_URL` (production Supabase URL)
- `SUPABASE_SERVICE_ROLE_KEY` (production service key)

### Step 2: Password Requirements
Your `CONSOLE_ADMIN_PASSWORD` must have:
- ✅ Minimum 12 characters
- ✅ At least 1 uppercase letter (A-Z)
- ✅ At least 1 lowercase letter (a-z)  
- ✅ At least 1 number (0-9)
- ✅ At least 1 special character (!@#$%^&*...)

### Step 3: Create Admin User

From the console-app directory:
```bash
cd apps/console-app
npm run admin:create
```

### Step 4: Login and Secure

1. **Login** at `http://localhost:3008/login`
2. **Remove credentials** from `.env.local` after successful login:
   ```bash
   # Remove these lines after first login
   # CONSOLE_ADMIN_EMAIL=<EMAIL>
   # CONSOLE_ADMIN_PASSWORD=YourVeryStrongPassword123!@#
   ```
3. **Change password** in console settings
4. **Test functionality** to ensure everything works

### Step 5: Additional Security

- [ ] Enable MFA in console settings (when available)
- [ ] Create additional admin users as needed
- [ ] Review audit logs regularly
- [ ] Rotate passwords periodically

## 🛡️ Security Features

### What's Protected:
- ✅ Strong password enforcement
- ✅ No hardcoded credentials
- ✅ Secure error messages (no credential leakage)
- ✅ All actions logged to `console_security_events`
- ✅ Super admin role with granular permissions

### Admin Permissions:
- Console users management (create, read, update, delete, invite)
- Companies management (full CRUD)
- Enums management (full CRUD)  
- Settings access (read, update)
- Audit logs (read access)
- Analytics (read access)

## 🚨 Security Notes

1. **Never commit** admin credentials to version control
2. **Use environment variables** for all sensitive data
3. **Remove setup credentials** after first login
4. **Monitor audit logs** for suspicious activity
5. **Use strong, unique passwords** for all admin accounts

## 🔧 Troubleshooting

### "Missing CONSOLE_ADMIN_PASSWORD"
```bash
# Add to .env.local
CONSOLE_ADMIN_PASSWORD=YourStrongPassword123!
```

### "Password does not meet security requirements"
Ensure your password has:
- 12+ characters
- Mixed case letters
- Numbers and symbols

### "Missing Supabase configuration"  
Verify these exist in `.env.local`:
- `PUBLIC_SUPABASE_URL`
- `SUPABASE_SERVICE_ROLE_KEY`

### "Access denied" after login
The user was created in auth but not in console_users table. Re-run the script.