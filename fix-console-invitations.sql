-- Fix Console User Invitations - Create Missing Table
-- Run this in your Supabase Cloud SQL Editor

-- Create console_user_invitations table
CREATE TABLE IF NOT EXISTS public.console_user_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('super_admin', 'company_admin', 'company_manager')),
    permissions JSONB,
    invited_by UUID NOT NULL REFERENCES public.console_users(id) ON DELETE CASCADE,
    token TEXT NOT NULL UNIQUE,
    expires_at TIMESTAMPTZ NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired', 'revoked')),
    used_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_console_user_invitations_email ON public.console_user_invitations(email);
CREATE INDEX IF NOT EXISTS idx_console_user_invitations_token ON public.console_user_invitations(token);
CREATE INDEX IF NOT EXISTS idx_console_user_invitations_status ON public.console_user_invitations(status);
CREATE INDEX IF NOT EXISTS idx_console_user_invitations_expires_at ON public.console_user_invitations(expires_at);
CREATE INDEX IF NOT EXISTS idx_console_user_invitations_invited_by ON public.console_user_invitations(invited_by);

-- Enable RLS
ALTER TABLE public.console_user_invitations ENABLE ROW LEVEL SECURITY;

-- RLS Policy for service role
CREATE POLICY "Service role can manage console invitations" 
ON public.console_user_invitations
FOR ALL 
TO service_role
USING (true);

-- Grant permissions
GRANT ALL ON public.console_user_invitations TO service_role;

-- Add comments
COMMENT ON TABLE public.console_user_invitations IS 'Tracks pending invitations for new console users';
COMMENT ON COLUMN public.console_user_invitations.permissions IS 'JSON array of default permissions for the invited user';
COMMENT ON COLUMN public.console_user_invitations.token IS 'Unique token for activation link';
COMMENT ON COLUMN public.console_user_invitations.status IS 'Current status of the invitation';

-- Create function to automatically update updated_at
CREATE OR REPLACE FUNCTION update_console_invitation_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS console_invitations_updated_at ON public.console_user_invitations;
CREATE TRIGGER console_invitations_updated_at
    BEFORE UPDATE ON public.console_user_invitations
    FOR EACH ROW
    EXECUTE FUNCTION update_console_invitation_updated_at();

SELECT 'Console user invitations table created successfully!' as status;