#!/usr/bin/env node

// Test authentication flows
import { config } from 'dotenv'
import fetch from 'node-fetch'

config()

const BASE_URL = 'http://localhost:3006'

console.log('🧪 Testing Authentication Flows...\n')

async function testRegistration() {
  console.log('📝 Testing Registration...')
  
  const formData = new URLSearchParams({
    email: '<EMAIL>',
    password: 'SecurePassword123!',
    first_name: 'Test',
    last_name: 'Candidate',
    phone: '555-0123'
  })

  try {
    const response = await fetch(`${BASE_URL}/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData
    })

    console.log('Registration Response Status:', response.status)
    const responseText = await response.text()
    
    if (response.status === 500) {
      console.error('❌ 500 Error in Registration:')
      console.error(responseText)
      return false
    } else if (response.status >= 200 && response.status < 400) {
      console.log('✅ Registration request processed')
      return true
    } else {
      console.log('⚠️  Registration response:', response.status)
      console.log(responseText.substring(0, 500) + '...')
      return false
    }
  } catch (error) {
    console.error('❌ Registration test failed:', error.message)
    return false
  }
}

async function testForgotPassword() {
  console.log('\n🔐 Testing Forgot Password...')
  
  const formData = new URLSearchParams({
    email: '<EMAIL>'
  })

  try {
    const response = await fetch(`${BASE_URL}/forgot-password`, {
      method: 'POST',  
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData
    })

    console.log('Forgot Password Response Status:', response.status)
    const responseText = await response.text()
    
    if (response.status === 500) {
      console.error('❌ 500 Error in Forgot Password:')
      console.error(responseText)
      return false
    } else if (response.status >= 200 && response.status < 400) {
      console.log('✅ Forgot Password request processed')
      return true
    } else {
      console.log('⚠️  Forgot Password response:', response.status)
      console.log(responseText.substring(0, 500) + '...')
      return false
    }
  } catch (error) {
    console.error('❌ Forgot Password test failed:', error.message)
    return false
  }
}

async function testLogin() {
  console.log('\n🔑 Testing Login...')
  
  const formData = new URLSearchParams({
    email: '<EMAIL>',
    password: 'SecurePassword123!'
  })

  try {
    const response = await fetch(`${BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData
    })

    console.log('Login Response Status:', response.status)
    const responseText = await response.text()
    
    if (response.status === 500) {
      console.error('❌ 500 Error in Login:')
      console.error(responseText)
      return false
    } else if (response.status >= 200 && response.status < 400) {
      console.log('✅ Login request processed')
      return true
    } else {
      console.log('⚠️  Login response:', response.status)
      console.log(responseText.substring(0, 500) + '...')
      return false
    }
  } catch (error) {
    console.error('❌ Login test failed:', error.message)
    return false
  }
}

async function runTests() {
  const results = []
  
  results.push(await testRegistration())
  results.push(await testForgotPassword()) 
  results.push(await testLogin())
  
  console.log('\n📊 Test Results:')
  console.log('Registration:', results[0] ? '✅ PASS' : '❌ FAIL')
  console.log('Forgot Password:', results[1] ? '✅ PASS' : '❌ FAIL') 
  console.log('Login:', results[2] ? '✅ PASS' : '❌ FAIL')
  
  const passCount = results.filter(r => r).length
  console.log(`\n${passCount}/3 tests passed`)
  
  if (passCount === 3) {
    console.log('🎉 All authentication flows are working!')
  } else {
    console.log('🔧 Some issues need to be fixed.')
  }
}

// Check if app is running first
console.log('🔍 Checking if candidate app is running...')
try {
  const healthCheck = await fetch(`${BASE_URL}`)
  if (healthCheck.ok) {
    console.log('✅ Candidate app is running at', BASE_URL)
    await runTests()
  } else {
    console.log('❌ Candidate app is not responding properly')
  }
} catch (error) {
  console.log('❌ Cannot connect to candidate app at', BASE_URL)
  console.log('Please make sure the app is running with: npm run dev:candidate')
}