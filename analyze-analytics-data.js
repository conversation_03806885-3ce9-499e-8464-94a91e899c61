// Test what data the analytics API actually receives
import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: './apps/console-app/.env.local' })

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const adminClient = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function analyzeRegistrationsData() {
  try {
    console.log('=== REGISTRATION ANALYTICS DATA ANALYSIS ===\n')

    // Fetch all business_registrations data
    const { data: registrations, error } = await adminClient
      .from('business_registrations')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ Error:', error)
      return
    }

    console.log('📊 OVERVIEW:')
    console.log('Total registrations:', registrations?.length || 0)

    if (!registrations || registrations.length === 0) {
      console.log('❌ No registration data found')
      return
    }

    // Analyze the data structure
    console.log('\n📋 SAMPLE RECORD STRUCTURE:')
    const sample = registrations[0]
    console.log('Available fields:', Object.keys(sample))
    console.log('Sample record:', {
      id: sample.id,
      company_name: sample.company_name,
      contact_person_email: sample.contact_person_email,
      status: sample.status,
      created_at: sample.created_at,
      activated_company_id: sample.activated_company_id,
      company_domain: sample.company_domain
    })

    // Analytics calculations
    console.log('\n📈 ANALYTICS BREAKDOWN:')
    const total = registrations.length
    const pending = registrations.filter(r => r.status === 'pending').length
    const approved = registrations.filter(r => r.status === 'approved').length
    const rejected = registrations.filter(r => r.status === 'rejected').length
    const withExistingCompany = registrations.filter(r => r.activated_company_id).length

    console.log(`Total: ${total}`)
    console.log(`Pending: ${pending}`)
    console.log(`Approved: ${approved}`)
    console.log(`Rejected: ${rejected}`)
    console.log(`Existing Company IDs: ${withExistingCompany}`)

    // Domain analysis
    console.log('\n🌐 DOMAIN ANALYSIS:')
    const domains = {}
    registrations.forEach(reg => {
      if (reg.contact_person_email) {
        const domain = reg.contact_person_email.split('@')[1]
        if (!domains[domain]) {
          domains[domain] = { total: 0, approved: 0, pending: 0, rejected: 0 }
        }
        domains[domain].total++
        domains[domain][reg.status]++
      }
    })

    console.log('Unique domains:', Object.keys(domains).length)
    Object.entries(domains).slice(0, 5).forEach(([domain, stats]) => {
      console.log(`${domain}: ${JSON.stringify(stats)}`)
    })

    // Check for missing analytics-critical fields
    console.log('\n⚠️  ANALYTICS COMPATIBILITY CHECK:')
    const hasUpdatedAt = sample.updated_at !== undefined
    const hasCompanyDomain = sample.company_domain !== undefined
    const hasDomainDetectionResult = sample.domain_detection_result !== undefined
    const hasActivatedCompanyId = sample.activated_company_id !== undefined

    console.log(`updated_at field: ${hasUpdatedAt ? '✅' : '❌'}`)
    console.log(`company_domain field: ${hasCompanyDomain ? '✅' : '❌'}`)
    console.log(`domain_detection_result field: ${hasDomainDetectionResult ? '✅' : '❌'}`)
    console.log(`activated_company_id field: ${hasActivatedCompanyId ? '✅' : '❌'}`)

    console.log('\n🔍 POTENTIAL ISSUES:')
    if (!hasUpdatedAt) {
      console.log('- Missing updated_at: Cannot calculate processing time')
    }
    if (!hasCompanyDomain) {
      console.log('- Missing company_domain: Domain analytics will extract from email')
    }
    if (!hasDomainDetectionResult) {
      console.log('- Missing domain_detection_result: No advanced domain detection data')
    }

  } catch (error) {
    console.error('❌ Analysis error:', error)
  }
}

analyzeRegistrationsData()
