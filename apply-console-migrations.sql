-- Console <PERSON>pp Schema for Supabase Cloud
-- Run this in your Supabase Cloud SQL Editor before creating admin user

-- 1. Create console_users table
CREATE TABLE IF NOT EXISTS public.console_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT NOT NULL UNIQUE,
    role TEXT NOT NULL CHECK (role IN ('super_admin', 'company_admin', 'company_manager')),
    company_ids TEXT[],
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMPTZ,
    mfa_enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Create console_user_permissions table
CREATE TABLE IF NOT EXISTS public.console_user_permissions (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    user_id UUID NOT NULL REFERENCES public.console_users(id) ON DELETE CASCADE,
    resource TEXT NOT NULL,
    actions TEXT[] NOT NULL,
    company_id UUID,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, resource, company_id)
);

-- 3. Create console_security_events table
CREATE TABLE IF NOT EXISTS public.console_security_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type TEXT NOT NULL,
    user_id TEXT,
    user_email TEXT,
    user_role TEXT,
    success BOOLEAN NOT NULL,
    error_message TEXT,
    ip_address TEXT,
    metadata JSONB,
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- 4. Create console_user_invitations table
CREATE TABLE IF NOT EXISTS public.console_user_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('super_admin', 'company_admin', 'company_manager')),
    company_ids TEXT[],
    invited_by UUID NOT NULL REFERENCES public.console_users(id),
    token TEXT NOT NULL UNIQUE,
    expires_at TIMESTAMPTZ NOT NULL,
    used_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_console_users_email ON public.console_users(email);
CREATE INDEX IF NOT EXISTS idx_console_user_permissions_user_id ON public.console_user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_console_security_events_user_id ON public.console_security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_console_security_events_timestamp ON public.console_security_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_console_user_invitations_token ON public.console_user_invitations(token);
CREATE INDEX IF NOT EXISTS idx_console_user_invitations_email ON public.console_user_invitations(email);

-- Enable RLS
ALTER TABLE public.console_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.console_user_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.console_security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.console_user_invitations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for service role
CREATE POLICY "Service role can manage console users" ON public.console_users
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage console permissions" ON public.console_user_permissions
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage security events" ON public.console_security_events
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage invitations" ON public.console_user_invitations
  FOR ALL USING (auth.role() = 'service_role');

-- Grant permissions
GRANT ALL ON public.console_users TO service_role;
GRANT ALL ON public.console_user_permissions TO service_role;
GRANT ALL ON public.console_security_events TO service_role;
GRANT ALL ON public.console_user_invitations TO service_role;

-- Comments for documentation
COMMENT ON TABLE public.console_users IS 'Stores authentication and role data for console administrators';
COMMENT ON TABLE public.console_user_permissions IS 'Defines specific permissions for console users on various resources';
COMMENT ON TABLE public.console_security_events IS 'Audit trail for security-sensitive actions within the console';
COMMENT ON TABLE public.console_user_invitations IS 'Tracks pending invitations for new console users';

SELECT 'Console app schema setup complete!' as status;