// Main notification service exports
export { NotificationService } from './core.js'
export { NotificationHelpers } from './common-notifications.js'

// Re-export types
export type {
  CreateNotificationRequest,
  Notification,
  NotificationFilters,
  NotificationEvent,
  NotificationType,
  UserNotificationPreference
} from '@psii/shared-types'

// Factory function to create notification service
import type { SupabaseClient } from '@supabase/supabase-js'

export function createNotificationService(supabase: SupabaseClient) {
  const service = new NotificationService(supabase)
  const helpers = new NotificationHelpers(service)
  
  return {
    service,
    helpers
  }
}
