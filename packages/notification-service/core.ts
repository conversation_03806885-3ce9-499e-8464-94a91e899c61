// Notification Service Core
// Handles creating, managing, and delivering notifications

import type { SupabaseClient } from '@supabase/supabase-js'
import type { 
  CreateNotificationRequest, 
  Notification, 
  NotificationFilters,
  NotificationEvent,
  UserNotificationPreference 
} from '@psii/shared-types'

export class NotificationService {
  private supabase: SupabaseClient
  private realtimeChannel: any

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase
  }

  // Create a new notification
  async createNotification(request: CreateNotificationRequest): Promise<{ data: Notification | null; error: string | null }> {
    try {
      // Get user's company_id
      const { data: user } = await this.supabase
        .from('users')
        .select('company_id')
        .eq('id', request.user_id)
        .single()

      if (!user) {
        return { data: null, error: 'User not found' }
      }

      // Get notification type
      const { data: notificationType } = await this.supabase
        .from('notification_types')
        .select('*')
        .eq('company_id', user.company_id)
        .eq('type_key', request.type_key)
        .single()

      if (!notificationType) {
        return { data: null, error: 'Notification type not found' }
      }

      // Create notification
      const { data: notification, error } = await this.supabase
        .from('notifications')
        .insert({
          company_id: user.company_id,
          user_id: request.user_id,
          notification_type_id: notificationType.id,
          title: request.title,
          message: request.message,
          action_url: request.action_url,
          action_label: request.action_label,
          entity_type: request.entity_type,
          entity_id: request.entity_id,
          priority: request.priority || 'normal',
          metadata: request.metadata || {}
        })
        .select('*, notification_type(*)')
        .single()

      if (error) {
        return { data: null, error: error.message }
      }

      // Send real-time update
      this.broadcastNotification('notification_created', notification)

      // Handle email/push if requested
      if (request.send_email) {
        await this.sendEmailNotification(notification)
      }

      return { data: notification, error: null }
    } catch (err) {
      return { data: null, error: err instanceof Error ? err.message : 'Unknown error' }
    }
  }

  // Get notifications for a user
  async getUserNotifications(
    userId: string, 
    filters: NotificationFilters = {}
  ): Promise<{ data: Notification[]; error: string | null }> {
    try {
      let query = this.supabase
        .from('notifications')
        .select('*, notification_type(*)')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (filters.is_read !== undefined) {
        query = query.eq('is_read', filters.is_read)
      }

      if (filters.category?.length) {
        query = query.in('notification_type.category', filters.category)
      }

      if (filters.priority?.length) {
        query = query.in('priority', filters.priority)
      }

      if (filters.limit) {
        query = query.limit(filters.limit)
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1)
      }

      const { data, error } = await query

      if (error) {
        return { data: [], error: error.message }
      }

      return { data: data || [], error: null }
    } catch (err) {
      return { data: [], error: err instanceof Error ? err.message : 'Unknown error' }
    }
  }

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<{ success: boolean; error: string | null }> {
    try {
      const { error } = await this.supabase.rpc('mark_notification_read', {
        notification_id: notificationId
      })

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, error: null }
    } catch (err) {
      return { success: false, error: err instanceof Error ? err.message : 'Unknown error' }
    }
  }

  // Mark all notifications as read for user
  async markAllAsRead(userId?: string): Promise<{ success: boolean; count: number; error: string | null }> {
    try {
      const { data: count, error } = await this.supabase.rpc('mark_all_notifications_read', {
        user_uuid: userId
      })

      if (error) {
        return { success: false, count: 0, error: error.message }
      }

      // Broadcast update
      this.broadcastNotification('notifications_read', { 
        user_id: userId || (await this.getCurrentUserId()), 
        count: count || 0 
      })

      return { success: true, count: count || 0, error: null }
    } catch (err) {
      return { success: false, count: 0, error: err instanceof Error ? err.message : 'Unknown error' }
    }
  }

  // Get unread count for user
  async getUnreadCount(userId: string): Promise<{ count: number; error: string | null }> {
    try {
      const { count, error } = await this.supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('is_read', false)

      if (error) {
        return { count: 0, error: error.message }
      }

      return { count: count || 0, error: null }
    } catch (err) {
      return { count: 0, error: err instanceof Error ? err.message : 'Unknown error' }
    }
  }

  // Subscribe to real-time notifications
  subscribeToNotifications(userId: string, callback: (event: NotificationEvent) => void) {
    this.realtimeChannel = this.supabase
      .channel(`notifications:${userId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          callback({
            type: 'notification_created',
            payload: payload.new as Notification
          })
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          callback({
            type: 'notification_updated',
            payload: payload.new as Notification
          })
        }
      )
      .subscribe()

    return this.realtimeChannel
  }

  // Unsubscribe from real-time notifications
  unsubscribeFromNotifications() {
    if (this.realtimeChannel) {
      this.supabase.removeChannel(this.realtimeChannel)
      this.realtimeChannel = null
    }
  }

  // Helper methods
  private async getCurrentUserId(): Promise<string | null> {
    const { data: { user } } = await this.supabase.auth.getUser()
    return user?.id || null
  }

  private async sendEmailNotification(notification: Notification) {
    // Check user's email preferences
    const { data: preferences } = await this.supabase
      .from('user_notification_preferences')
      .select('email_enabled')
      .eq('user_id', notification.user_id)
      .eq('notification_type_id', notification.notification_type_id)
      .single()

    if (!preferences?.email_enabled) {
      return // User has email notifications disabled for this type
    }

    // Get user email
    const { data: user } = await this.supabase
      .from('users')
      .select('email')
      .eq('id', notification.user_id)
      .single()

    if (!user?.email) return

    // Send email using email service (integrate with Phase 8A)
    // This will be integrated with the existing email service
    console.log('Would send email notification to:', user.email, notification.title)
  }

  private broadcastNotification(type: string, payload: any) {
    // Broadcast to real-time subscribers
    if (this.realtimeChannel) {
      this.realtimeChannel.send({
        type: 'broadcast',
        event: type,
        payload
      })
    }
  }
}
