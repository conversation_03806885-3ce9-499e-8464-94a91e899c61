// Notification helpers for common use cases
export class NotificationHelpers {
  private service: NotificationService

  constructor(service: NotificationService) {
    this.service = service
  }

  // User management notifications
  async notifyUserInvited(adminUserId: string, inviterName: string, invitedEmail: string, role: string) {
    return this.service.createNotification({
      user_id: adminUserId,
      type_key: 'user_invited',
      title: 'User Invitation Sent',
      message: `${inviterName} invited ${invitedEmail} as ${role}`,
      action_url: '/settings/users',
      action_label: 'View Users',
      send_email: true
    })
  }

  async notifyUserJoined(adminUserId: string, newUserName: string, newUserEmail: string) {
    return this.service.createNotification({
      user_id: adminUserId,
      type_key: 'user_joined',
      title: 'New Team Member',
      message: `${newUserName} (${newUserEmail}) has joined your team`,
      action_url: '/settings/users',
      action_label: 'View Users'
    })
  }

  async notifyRoleChanged(userId: string, newRole: string, changedBy: string) {
    return this.service.createNotification({
      user_id: userId,
      type_key: 'user_role_changed',
      title: 'Your Role Updated',
      message: `Your role has been changed to ${newRole} by ${changedBy}`,
      priority: 'high'
    })
  }

  // Job management notifications
  async notifyJobApplication(recruiterId: string, jobTitle: string, candidateName: string, jobId: string) {
    return this.service.createNotification({
      user_id: recruiterId,
      type_key: 'job_application',
      title: 'New Application',
      message: `${candidateName} applied for ${jobTitle}`,
      action_url: `/jobs/${jobId}/applications`,
      action_label: 'View Application',
      entity_type: 'job',
      entity_id: jobId,
      priority: 'high',
      send_email: true
    })
  }

  async notifyInterviewScheduled(candidateUserId: string, jobTitle: string, interviewDate: string) {
    return this.service.createNotification({
      user_id: candidateUserId,
      type_key: 'interview_scheduled',
      title: 'Interview Scheduled',
      message: `Your interview for ${jobTitle} is scheduled for ${interviewDate}`,
      action_url: '/interviews',
      action_label: 'View Details',
      priority: 'high',
      send_email: true
    })
  }

  // System notifications
  async notifySystemMaintenance(userIds: string[], startTime: string, duration: string) {
    const promises = userIds.map(userId =>
      this.service.createNotification({
        user_id: userId,
        type_key: 'system_maintenance',
        title: 'Scheduled Maintenance',
        message: `System maintenance scheduled for ${startTime} (${duration})`,
        priority: 'normal',
        send_email: true
      })
    )
    return Promise.all(promises)
  }
}
