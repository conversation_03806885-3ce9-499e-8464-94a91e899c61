// Shared enum utilities for customer-app and candidate-app
// This provides client-side enum access with caching

export interface EnumValue {
  key: string
  label: string
  color?: string
  active?: boolean
  sort_order?: number
  subcategories?: EnumValue[]
}

export interface ApplicationEnum {
  id: string
  category: string
  display_name: string
  description?: string
  values: EnumValue[]
  is_system: boolean
  is_editable: boolean
  is_hierarchical: boolean
  version: number
  created_at: string
  updated_at: string
}

// Client-side cache
let clientEnumCache: Map<string, ApplicationEnum> = new Map()
let clientCacheTimestamp: number = 0
const CLIENT_CACHE_DURATION = 10 * 60 * 1000 // 10 minutes for client-side

/**
 * Fetch enums from the API endpoint
 */
async function fetchEnumsFromAPI(): Promise<ApplicationEnum[]> {
  try {
    const response = await fetch('/api/enums')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const data = await response.json()
    return data.enums || []
  } catch (error) {
    console.error('Error fetching enums from API:', error)
    throw error
  }
}

/**
 * Get all application enums (client-side)
 */
export async function getApplicationEnums(): Promise<ApplicationEnum[]> {
  const now = Date.now()
  
  // Check if cache is valid
  if (clientEnumCache.size > 0 && (now - clientCacheTimestamp) < CLIENT_CACHE_DURATION) {
    return Array.from(clientEnumCache.values())
  }

  try {
    const enums = await fetchEnumsFromAPI()
    
    // Update cache
    clientEnumCache.clear()
    enums.forEach(enumItem => {
      clientEnumCache.set(enumItem.category, enumItem)
    })
    
    clientCacheTimestamp = now
    return enums

  } catch (error) {
    // Return cached data if available, even if stale
    if (clientEnumCache.size > 0) {
      console.warn('Returning stale enum cache due to fetch error')
      return Array.from(clientEnumCache.values())
    }
    throw error
  }
}

/**
 * Get enum by category (client-side)
 */
export async function getEnumByCategory(category: string): Promise<ApplicationEnum | null> {
  const now = Date.now()
  
  // Check cache first
  if (clientEnumCache.has(category) && (now - clientCacheTimestamp) < CLIENT_CACHE_DURATION) {
    return clientEnumCache.get(category) || null
  }

  try {
    await getApplicationEnums() // This will refresh the cache
    return clientEnumCache.get(category) || null
  } catch (error) {
    console.error(`Error getting enum for category ${category}:`, error)
    return null
  }
}

/**
 * Get enum values by category
 */
export async function getEnumValues(category: string): Promise<EnumValue[]> {
  try {
    const enumData = await getEnumByCategory(category)
    return enumData?.values || []
  } catch (error) {
    console.error(`Error getting enum values for ${category}:`, error)
    return []
  }
}

/**
 * Get active enum values only
 */
export async function getActiveEnumValues(category: string): Promise<EnumValue[]> {
  try {
    const values = await getEnumValues(category)
    return values.filter(value => value.active !== false)
  } catch (error) {
    console.error(`Error getting active enum values for ${category}:`, error)
    return []
  }
}

/**
 * Get enum options for select components
 */
export async function getEnumOptions(category: string): Promise<Array<{value: string, label: string, color?: string}>> {
  try {
    const values = await getActiveEnumValues(category)
    return values.map(value => ({
      value: value.key,
      label: value.label,
      color: value.color
    }))
  } catch (error) {
    console.error(`Error getting enum options for ${category}:`, error)
    return []
  }
}

/**
 * Get hierarchical enum values (flattened for dropdowns)
 */
export async function getHierarchicalEnumOptions(category: string): Promise<Array<{value: string, label: string, color?: string, isSubcategory?: boolean}>> {
  try {
    const enumData = await getEnumByCategory(category)
    if (!enumData?.is_hierarchical) {
      return getEnumOptions(category)
    }

    const options: Array<{value: string, label: string, color?: string, isSubcategory?: boolean}> = []
    
    enumData.values.forEach(value => {
      if (value.active !== false) {
        // Add main value
        options.push({
          value: value.key,
          label: value.label,
          color: value.color,
          isSubcategory: false
        })
        
        // Add subcategories if they exist
        if (value.subcategories && Array.isArray(value.subcategories)) {
          value.subcategories.forEach(subcategory => {
            options.push({
              value: `${value.key}_${subcategory.key}`,
              label: `  ${subcategory.label}`, // Indent with spaces
              color: value.color,
              isSubcategory: true
            })
          })
        }
      }
    })
    
    return options

  } catch (error) {
    console.error(`Error getting hierarchical enum options for ${category}:`, error)
    return []
  }
}

/**
 * Get enum value display information
 */
export async function getEnumValueInfo(category: string, key: string): Promise<{label: string, color: string} | null> {
  try {
    const values = await getEnumValues(category)
    
    // First check direct match
    let value = values.find(v => v.key === key)
    
    // If not found and this might be a hierarchical key, check subcategories
    if (!value && key.includes('_')) {
      const [parentKey, subKey] = key.split('_', 2)
      const parentValue = values.find(v => v.key === parentKey)
      
      if (parentValue?.subcategories) {
        const subcategory = parentValue.subcategories.find(sub => sub.key === subKey)
        if (subcategory) {
          return {
            label: `${parentValue.label} - ${subcategory.label}`,
            color: parentValue.color || '#6b7280'
          }
        }
      }
    }
    
    if (value) {
      return {
        label: value.label,
        color: value.color || '#6b7280'
      }
    }
    
    return null

  } catch (error) {
    console.error(`Error getting enum value info for ${category}.${key}:`, error)
    return null
  }
}

/**
 * Get enum value label
 */
export async function getEnumValueLabel(category: string, key: string): Promise<string> {
  const info = await getEnumValueInfo(category, key)
  return info?.label || key
}

/**
 * Get enum value color
 */
export async function getEnumValueColor(category: string, key: string): Promise<string> {
  const info = await getEnumValueInfo(category, key)
  return info?.color || '#6b7280'
}

/**
 * Clear client-side cache
 */
export function clearEnumCache(): void {
  clientEnumCache.clear()
  clientCacheTimestamp = 0
}

/**
 * Preload common enums for better UX
 */
export async function preloadEnums(categories?: string[]): Promise<void> {
  const defaultCategories = [
    'job_statuses',
    'candidate_statuses',
    'work_authorization_types',
    'employment_types',
    'skill_levels',
    'interview_types',
    'interview_statuses'
  ]

  const categoriesToLoad = categories || defaultCategories

  try {
    // Load all enums first
    await getApplicationEnums()
    
    // Verify specific categories are cached
    for (const category of categoriesToLoad) {
      await getEnumByCategory(category)
    }
    
    console.log('Enums preloaded successfully:', categoriesToLoad)
  } catch (error) {
    console.error('Error preloading enums:', error)
  }
}

/**
 * Format enum value for display with color badge
 */
export function formatEnumValueDisplay(label: string, color: string): string {
  return `<span class="inline-flex items-center gap-1">
    <span class="w-2 h-2 rounded-full" style="background-color: ${color}"></span>
    ${label}
  </span>`
}

/**
 * Get cache statistics
 */
export function getEnumCacheStats() {
  return {
    size: clientEnumCache.size,
    age: Date.now() - clientCacheTimestamp,
    maxAge: CLIENT_CACHE_DURATION,
    categories: Array.from(clientEnumCache.keys()),
    isStale: (Date.now() - clientCacheTimestamp) > CLIENT_CACHE_DURATION
  }
}
