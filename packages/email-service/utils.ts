// PSII Email Service Integration
// File: packages/email-service/utils.ts
// Utility functions for email service integration

import { createClient } from '@supabase/supabase-js'
import { createEmailService } from './manager'
import type { EmailSendRequest } from '@psii/shared-types'

export interface EmailServiceUtils {
  sendWelcomeEmail: (email: string, companyId: string, userToken?: string) => Promise<boolean>
  sendPasswordResetEmail: (email: string, companyId: string, resetUrl: string) => Promise<boolean>
  sendInvitationEmail: (email: string, companyId: string, invitationUrl: string, companyName: string) => Promise<boolean>
  testEmailConfiguration: (email: string, companyId: string) => Promise<boolean>
}

export function createEmailServiceUtils(): EmailServiceUtils {
  const supabase = createClient(
    process.env.PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  )
  
  const emailService = createEmailService(supabase)

  return {
    async sendWelcomeEmail(email: string, companyId: string, userToken?: string): Promise<boolean> {
      try {
        const request: EmailSendRequest = {
          to: email,
          template_type: 'welcome',
          variables: {
            email,
            token: userToken,
            welcome_url: `${process.env.PUBLIC_SITE_URL}/welcome?token=${userToken}`,
            company_name: 'ProcureServe'
          },
          company_id: companyId
        }
        
        const result = await emailService.sendEmail(request)
        return result.success
      } catch (error) {
        console.error('Failed to send welcome email:', error)
        return false
      }
    },

    async sendPasswordResetEmail(email: string, companyId: string, resetUrl: string): Promise<boolean> {
      try {
        const request: EmailSendRequest = {
          to: email,
          template_type: 'password_reset',
          variables: {
            email,
            reset_url: resetUrl,
            company_name: 'ProcureServe'
          },
          company_id: companyId
        }
        
        const result = await emailService.sendEmail(request)
        return result.success
      } catch (error) {
        console.error('Failed to send password reset email:', error)
        return false
      }
    },

    async sendInvitationEmail(email: string, companyId: string, invitationUrl: string, companyName: string): Promise<boolean> {
      try {
        const request: EmailSendRequest = {
          to: email,
          template_type: 'user_invitation',
          variables: {
            email,
            invitation_url: invitationUrl,
            company_name: companyName
          },
          company_id: companyId
        }
        
        const result = await emailService.sendEmail(request)
        return result.success
      } catch (error) {
        console.error('Failed to send invitation email:', error)
        return false
      }
    },

    async testEmailConfiguration(email: string, companyId: string): Promise<boolean> {
      try {
        const request: EmailSendRequest = {
          to: email,
          template_type: 'notification',
          variables: {
            subject: 'Email Configuration Test',
            message: 'This is a test email to verify your email configuration is working correctly.',
            test_timestamp: new Date().toISOString()
          },
          company_id: companyId
        }
        
        const result = await emailService.sendEmail(request)
        return result.success
      } catch (error) {
        console.error('Failed to send test email:', error)
        return false
      }
    }
  }
}
