import type { EmailSendRequest, EmailSendResult, EmailServiceConfig, EmailTemplateType } from '@psii/shared-types'

// Base email provider interface
export interface EmailProvider {
  name: string
  send(request: EmailSendRequest, config: EmailServiceConfig): Promise<EmailSendResult>
  validateConfig(config: Partial<EmailServiceConfig>): { valid: boolean; errors: string[] }
}

// Supabase Auth email provider
export class SupabaseEmailProvider implements EmailProvider {
  name = 'supabase'
  
  async send(request: EmailSendRequest, config: EmailServiceConfig): Promise<EmailSendResult> {
    // For development/testing - log instead of sending
    if (process.env.NODE_ENV === 'development') {
      console.log(`📧 [DEV] Email would be sent via Supabase:`)
      console.log(`To: ${request.to}`)
      console.log(`Template: ${request.template_type}`)
      console.log(`Variables:`, request.variables)
      
      return {
        success: true,
        message_id: `dev-${Date.now()}`,
        provider_used: 'supabase'
      }
    }

    try {
      // In production, use Supabase Auth email
      const { createClient } = await import('@supabase/supabase-js')
      const supabase = createClient(
        process.env.PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      )

      if (request.template_type === 'user_invitation') {
        const { error } = await supabase.auth.admin.inviteUserByEmail(request.to, {
          redirectTo: `${process.env.PUBLIC_SITE_URL}/accept-invitation?token=${request.variables.token}`
        })
        
        if (error) throw error
      } else if (request.template_type === 'password_reset') {
        const { error } = await supabase.auth.resetPasswordForEmail(request.to, {
          redirectTo: `${process.env.PUBLIC_SITE_URL}/reset-password`
        })
        
        if (error) throw error
      }

      return {
        success: true,
        message_id: `supabase-${Date.now()}`,
        provider_used: 'supabase'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider_used: 'supabase'
      }
    }
  }

  validateConfig(config: Partial<EmailServiceConfig>): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!config.from_email) errors.push('From email is required')
    if (!config.from_name) errors.push('From name is required')
    
    return { valid: errors.length === 0, errors }
  }
}

// Resend provider (modern email API)
export class ResendProvider implements EmailProvider {
  name = 'resend'

  async send(request: EmailSendRequest, config: EmailServiceConfig): Promise<EmailSendResult> {
    try {
      const { Resend } = await import('resend')
      const resend = new Resend(config.api_key || process.env.RESEND_API_KEY)

      const { data, error } = await resend.emails.send({
        from: `${config.from_name} <${config.from_email}>`,
        to: [request.to],
        subject: this.getSubjectForTemplate(request.template_type, request.variables),
        html: this.getHtmlForTemplate(request.template_type, request.variables),
        text: this.getTextForTemplate(request.template_type, request.variables),
        replyTo: config.reply_to_email || config.from_email
      })

      if (error) {
        console.error('Resend API error:', error)
        return {
          success: false,
          error: error.message || 'Unknown error',
          provider_used: 'resend'
        }
      }

      return {
        success: true,
        message_id: data?.id || `resend-${Date.now()}`,
        provider_used: 'resend'
      }
    } catch (error) {
      console.error('Resend error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider_used: 'resend'
      }
    }
  }

  validateConfig(config: Partial<EmailServiceConfig>): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!config.api_key && !process.env.RESEND_API_KEY) errors.push('Resend API key or RESEND_API_KEY environment variable is required')
    if (!config.from_email && !process.env.EMAIL_FROM_ADDRESS) errors.push('From email or EMAIL_FROM_ADDRESS environment variable is required')
    if (!config.from_name && !process.env.EMAIL_FROM_NAME) errors.push('From name or EMAIL_FROM_NAME environment variable is required')

    return { valid: errors.length === 0, errors }
  }

  private getSubjectForTemplate(type: EmailTemplateType, variables: Record<string, any>): string {
    const subjects: Record<EmailTemplateType, string> = {
      user_invitation: `Activate Your ${variables.company_name || 'ProcureServe'} Account`,
      password_reset: 'Reset Your Password - ProcureServe',
      welcome: `Welcome to ${variables.company_name || 'ProcureServe'}`,
      notification: variables.subject || 'Notification from ProcureServe',
      registration_submitted: 'Business Registration Received - ProcureServe',
      registration_approved: 'Welcome to ProcureServe - Account Activation Required',
      registration_rejected: 'ProcureServe Registration Update Required'
    }
    return subjects[type] || 'Notification from ProcureServe'
  }

  private getHtmlForTemplate(type: EmailTemplateType, variables: Record<string, any>): string {
    // Use provided HTML template if available
    if (variables.html) {
      return variables.html
    }

    // Fallback templates for basic types
    const templates = {
      user_invitation: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">ProcureServe</h1>
            <p style="color: #64748b; margin: 5px 0;">Business Registration Platform</p>
          </div>
          
          <div style="background: #dcfce7; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #16a34a;">
            <h2 style="color: #15803d; margin-top: 0;">🎉 Account Activation Required</h2>
            <p style="color: #166534; line-height: 1.6;">
              Welcome to <strong>${variables.company_name || 'ProcureServe'}</strong>! 
              Your business registration has been approved.
            </p>
          </div>
          
          <div style="text-align: center; margin: 25px 0;">
            <a href="${variables.activation_link || variables.invitation_url}" 
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;">
              Activate Account & Set Password
            </a>
          </div>
          
          <p style="color: #64748b; font-size: 14px; line-height: 1.6; text-align: center;">
            This activation link will expire in 7 days. If you need a new link, 
            visit our activation request page.
          </p>
          
          <div style="text-align: center; margin-top: 30px;">
            <p style="color: #64748b; font-size: 14px;">
              Need help? Contact us at 
              <a href="mailto:<EMAIL>" style="color: #2563eb;"><EMAIL></a>
            </p>
          </div>
        </div>
      `,
      password_reset: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2>Reset Your Password</h2>
          <p>Click <a href="${variables.reset_url}">here</a> to reset your password.</p>
          <p>This link will expire in 1 hour.</p>
        </div>
      `
    }
    return templates[type] || `<p>${variables.message || 'Notification'}</p>`
  }

  private getTextForTemplate(type: EmailTemplateType, variables: Record<string, any>): string {
    // Use provided text template if available
    if (variables.text) {
      return variables.text
    }

    // Fallback templates
    const templates = {
      user_invitation: `Welcome to ${variables.company_name || 'ProcureServe'}! Your business registration has been approved. Activate your account: ${variables.activation_link || variables.invitation_url}`,
      password_reset: `Reset your password by visiting: ${variables.reset_url}`
    }
    return templates[type] || variables.message || 'Notification'
  }
}

// SMTP provider (for Gmail, custom SMTP, etc.)
export class SMTPProvider implements EmailProvider {
  name = 'smtp'
  
  async send(request: EmailSendRequest, config: EmailServiceConfig): Promise<EmailSendResult> {
    try {
      const nodemailer = await import('nodemailer')
      
      const transporter = nodemailer.createTransport({
        host: config.smtp_host,
        port: config.smtp_port || 587,
        secure: config.smtp_port === 465,
        auth: {
          user: config.smtp_username,
          pass: config.smtp_password
        }
      })

      const info = await transporter.sendMail({
        from: `"${config.from_name}" <${config.from_email}>`,
        to: request.to,
        subject: this.getSubjectForTemplate(request.template_type, request.variables),
        html: this.getHtmlForTemplate(request.template_type, request.variables),
        text: this.getTextForTemplate(request.template_type, request.variables)
      })

      return {
        success: true,
        message_id: info.messageId,
        provider_used: 'smtp'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider_used: 'smtp'
      }
    }
  }

  validateConfig(config: Partial<EmailServiceConfig>): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!config.smtp_host) errors.push('SMTP host is required')
    if (!config.smtp_username) errors.push('SMTP username is required')
    if (!config.smtp_password) errors.push('SMTP password is required')
    if (!config.from_email) errors.push('From email is required')
    
    return { valid: errors.length === 0, errors }
  }

  private getSubjectForTemplate(type: string, variables: Record<string, any>): string {
    // Same as ZeptoMail implementation
    const subjects = {
      user_invitation: `Welcome to ${variables.company_name || 'ProcureServe'}`,
      password_reset: 'Reset your password',
      welcome: `Welcome to ${variables.company_name || 'ProcureServe'}`,
      notification: variables.subject || 'Notification'
    }
    return subjects[type] || 'Notification'
  }

  private getHtmlForTemplate(type: EmailTemplateType, variables: Record<string, any>): string {
    // Same as ZeptoMail implementation
    const templates = {
      user_invitation: `
        <h2>You've been invited to join ${variables.company_name}</h2>
        <p>Click <a href="${variables.invitation_url}">here</a> to accept your invitation.</p>
      `,
      password_reset: `
        <h2>Reset Your Password</h2>
        <p>Click <a href="${variables.reset_url}">here</a> to reset your password.</p>
      `
    }
    return templates[type] || `<p>${variables.message || 'Notification'}</p>`
  }

  private getTextForTemplate(type: EmailTemplateType, variables: Record<string, any>): string {
    // Same as ZeptoMail implementation
    const templates = {
      user_invitation: `You've been invited to join ${variables.company_name}. Visit: ${variables.invitation_url}`,
      password_reset: `Reset your password by visiting: ${variables.reset_url}`
    }
    return templates[type] || variables.message || 'Notification'
  }
}
