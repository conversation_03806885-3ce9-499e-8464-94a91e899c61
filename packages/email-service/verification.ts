import { Resend } from 'resend';

interface EmailConfig {
  apiKey: string;
  fromName?: string;
  fromAddress?: string;
}

interface SendVerificationEmailParams {
  to: string;
  firstName: string;
  token: string;
  baseUrl: string;
  config: EmailConfig;
}

export async function sendVerificationEmail({ 
  to, 
  firstName, 
  token, 
  baseUrl, 
  config 
}: SendVerificationEmailParams): Promise<{ success: boolean; message?: string; error?: string }> {
  if (!config.apiKey) {
    console.error('Resend API key is not provided in config. Cannot send email.');
    return {
      success: false,
      error: 'Email service is not configured.'
    };
  }

  const verificationLink = `${baseUrl}/auth/verify-email?token=${token}`;
  const fromName = config.fromName || 'ProcureServe';
  const fromAddress = config.fromAddress || '<EMAIL>';

  try {
    const resend = new Resend(config.apiKey);
    const { data, error } = await resend.emails.send({
      from: `${fromName} <${fromAddress}>`,
      to: [to],
      subject: 'Welcome to ProcureServe - Please Verify Your Email',
      html: `
        <div style="font-family: Arial, sans-serif; line-height: 1.6;">
          <h2>Welcome to ProcureServe, ${firstName}!</h2>
          <p>We're excited to have you on board. Please verify your email address to complete your registration and get full access to your candidate profile.</p>
          <p>Click the button below to confirm your email:</p>
          <a href="${verificationLink}" style="background-color: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">Verify Email Address</a>
          <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
          <p><a href="${verificationLink}">${verificationLink}</a></p>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not sign up for an account, please disregard this email.</p>
          <hr>
          <p>Thank you,<br>The ProcureServe Team</p>
        </div>
      `,
      text: `Welcome to ProcureServe, ${firstName}!\n\nPlease verify your email address by clicking the link below or pasting it into your browser:\n${verificationLink}\n\nThis link will expire in 24 hours.\n\nIf you did not sign up for an account, please disregard this email.\n\nThank you,\nThe ProcureServe Team`
    });

    if (error) {
      console.error('Resend API error:', error);
      return {
        success: false,
        error: 'Failed to send verification email.'
      };
    }

    console.log('Verification email sent successfully:', data?.id);
    return {
      success: true,
      message: `Verification email sent to ${to}.`
    };
  } catch (error) {
    console.error('Error sending verification email:', error);
    return {
      success: false,
      error: 'Failed to send verification email.'
    };
  }
}

interface PasswordResetEmailParams {
  to: string;
  firstName: string;
  resetLink: string;
  config: EmailConfig;
}

export async function sendPasswordResetEmail({
  to, 
  firstName, 
  resetLink, 
  config
}: PasswordResetEmailParams): Promise<{ success: boolean; error?: string }> {
  if (!config.apiKey) {
    console.error('Resend API key is not provided in config. Cannot send email.');
    return {
      success: false,
      error: 'Email service is not configured.'
    };
  }

  const fromName = config.fromName || 'ProcureServe';
  const fromAddress = config.fromAddress || '<EMAIL>';

  try {
    const resend = new Resend(config.apiKey);
    const { data, error } = await resend.emails.send({
      from: `${fromName} <${fromAddress}>`,
      to: [to],
      subject: 'Reset Your Password',
      html: `
        <div style="font-family: Arial, sans-serif; line-height: 1.6;">
          <h2>Password Reset Request</h2>
          <p>Hi ${firstName},</p>
          <p>You requested a password reset. Click the button below to set a new password:</p>
          <a href="${resetLink}" style="background-color: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">Reset Password</a>
          <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
          <p><a href="${resetLink}">${resetLink}</a></p>
          <p>If you didn't request this, you can safely ignore this email.</p>
          <p>This link is valid for 1 hour.</p>
          <hr>
          <p>Thank you,<br>The ProcureServe Team</p>
        </div>
      `,
      text: `Hi ${firstName},\n\nYou requested a password reset. Click the link below to set a new password:\n${resetLink}\n\nIf you didn't request this, you can safely ignore this email.\n\nThis link is valid for 1 hour.\n\nThank you,\nThe ProcureServe Team`
    });

    if (error) {
      console.error('Resend API error:', error);
      return { success: false, error: 'Failed to send password reset email.' };
    }

    console.log('Password reset email sent successfully:', data?.id);
    return { success: true };
  } catch (error) {
    console.error('Error sending password reset email:', error);
    return { success: false, error: 'Failed to send password reset email.' };
  }
}