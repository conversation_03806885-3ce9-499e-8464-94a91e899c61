import crypto from 'crypto'

/**
 * Secure Activation Token System for PSII
 * Provides cryptographically secure token generation and validation
 */

export interface ActivationToken {
  token: string
  hashedToken: string
  expiresAt: Date
  maxAttempts: number
}

export interface TokenValidationResult {
  isValid: boolean
  isExpired: boolean
  attemptsExceeded: boolean
  tokenData?: {
    registrationId: string
    companyId: string
  }
}

export class SecureTokenService {
  private static readonly TOKEN_LENGTH = 32 // 32 bytes = 256 bits
  private static readonly DEFAULT_EXPIRY_DAYS = 7
  private static readonly MAX_ATTEMPTS = 3
  
  /**
   * Generate a cryptographically secure activation token
   */
  static generateActivationToken(): ActivationToken {
    // Generate cryptographically secure random bytes
    const tokenBytes = crypto.randomBytes(this.TOKEN_LENGTH)
    const token = tokenBytes.toString('hex')
    
    // Hash the token for secure storage (SHA-256)
    const hashedToken = this.hashToken(token)
    
    // Set expiration (7 days from now)
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + this.DEFAULT_EXPIRY_DAYS)
    
    return {
      token, // Send this in email (plaintext)
      hashedToken, // Store this in database
      expiresAt,
      maxAttempts: this.MAX_ATTEMPTS
    }
  }
  
  /**
   * Hash a token using SHA-256 with salt
   */
  static hashToken(token: string): string {
    // Use SHA-256 with a salt for security
    const salt = 'psii_activation_salt_2025' // Application-specific salt
    return crypto
      .createHash('sha256')
      .update(token + salt)
      .digest('hex')
  }
  
  /**
   * Validate an activation token against stored hash
   */
  static validateToken(
    providedToken: string, 
    storedHash: string, 
    expiresAt: Date, 
    currentAttempts: number
  ): TokenValidationResult {
    // Check if too many attempts
    if (currentAttempts >= this.MAX_ATTEMPTS) {
      return {
        isValid: false,
        isExpired: false,
        attemptsExceeded: true
      }
    }
    
    // Check if token is expired
    const now = new Date()
    if (now > expiresAt) {
      return {
        isValid: false,
        isExpired: true,
        attemptsExceeded: false
      }
    }
    
    // Hash provided token and compare
    const hashedProvided = this.hashToken(providedToken)
    const isValid = crypto.timingSafeEqual(
      Buffer.from(hashedProvided, 'hex'),
      Buffer.from(storedHash, 'hex')
    )
    
    return {
      isValid,
      isExpired: false,
      attemptsExceeded: false
    }
  }
  
  /**
   * Generate activation URL with secure token
   */
  static generateActivationUrl(token: string, baseUrl: string): string {
    return `${baseUrl}/activate?token=${encodeURIComponent(token)}`
  }
}
