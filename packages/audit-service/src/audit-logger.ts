import type { SupabaseClient } from '@supabase/supabase-js'

/**
 * Audit Logging Service for PSII
 * Provides immutable security audit trail for all sensitive operations
 */

export interface AuditEvent {
  eventType: string
  userId?: string
  companyId?: string
  resourceType: string
  resourceId: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  severity?: 'low' | 'medium' | 'high' | 'critical'
}

export interface AuditLogEntry extends AuditEvent {
  id: string
  createdAt: string
}

export interface AuditFilter {
  eventTypes?: string[]
  userIds?: string[]
  companyIds?: string[]
  resourceTypes?: string[]
  severities?: string[]
  dateFrom?: string
  dateTo?: string
  limit?: number
  offset?: number
}

export interface AuditSearchResult {
  logs: AuditLogEntry[]
  totalCount: number
  hasMore: boolean
}

export class AuditLogger {
  private supabase: SupabaseClient
  
  constructor(supabaseClient: SupabaseClient) {
    this.supabase = supabaseClient
  }
  
  /**
   * Log a security-related event to the audit trail
   */
  async logEvent(event: AuditEvent): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('audit_logs')
        .insert({
          event_type: event.eventType,
          user_id: event.userId || null,
          company_id: event.companyId || null,
          resource_type: event.resourceType,
          resource_id: event.resourceId,
          details: {
            ...(event.details || {}),
            severity: event.severity || 'medium',
            timestamp: new Date().toISOString()
          },
          ip_address: event.ipAddress || null,
          user_agent: event.userAgent || null
        })
      
      if (error) {
        console.error('[AUDIT] Failed to log event:', error)
        // Don't throw - audit failures shouldn't break operations
      }
    } catch (err) {
      console.error('[AUDIT] Audit logging error:', err)
    }
  }

  /**
   * Search audit logs with advanced filtering
   */
  async searchLogs(filter: AuditFilter = {}): Promise<AuditSearchResult> {
    try {
      console.log('[AUDIT-LOGGER] Starting search with filter:', filter)
      
      let query = this.supabase
        .from('audit_logs')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false })

      // Apply filters
      if (filter.eventTypes?.length) {
        console.log('[AUDIT-LOGGER] Filtering by event types:', filter.eventTypes)
        query = query.in('event_type', filter.eventTypes)
      }
      if (filter.userIds?.length) {
        console.log('[AUDIT-LOGGER] Filtering by user IDs:', filter.userIds)
        query = query.in('user_id', filter.userIds)
      }
      if (filter.companyIds?.length) {
        console.log('[AUDIT-LOGGER] Filtering by company IDs:', filter.companyIds)
        query = query.in('company_id', filter.companyIds)
      }
      if (filter.resourceTypes?.length) {
        console.log('[AUDIT-LOGGER] Filtering by resource types:', filter.resourceTypes)
        query = query.in('resource_type', filter.resourceTypes)
      }
      if (filter.dateFrom) {
        console.log('[AUDIT-LOGGER] Filtering date from:', filter.dateFrom)
        query = query.gte('created_at', filter.dateFrom)
      }
      if (filter.dateTo) {
        console.log('[AUDIT-LOGGER] Filtering date to:', filter.dateTo)
        query = query.lte('created_at', filter.dateTo)
      }
      if (filter.severities?.length) {
        console.log('[AUDIT-LOGGER] Filtering by severities:', filter.severities)
        query = query.in('details->severity', filter.severities)
      }

      // Apply pagination
      const limit = filter.limit || 50
      const offset = filter.offset || 0
      console.log('[AUDIT-LOGGER] Applying pagination:', { limit, offset })
      query = query.range(offset, offset + limit - 1)

      console.log('[AUDIT-LOGGER] Executing query...')
      const { data, count, error } = await query

      if (error) {
        console.error('[AUDIT-LOGGER] Search error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        })
        return { logs: [], totalCount: 0, hasMore: false }
      }

      console.log('[AUDIT-LOGGER] Query successful:', {
        dataLength: data?.length || 0,
        count,
        hasMore: (count || 0) > offset + limit
      })

      return {
        logs: data || [],
        totalCount: count || 0,
        hasMore: (count || 0) > offset + limit
      }
    } catch (err) {
      console.error('[AUDIT-LOGGER] Search logs unexpected error:', err)
      return { logs: [], totalCount: 0, hasMore: false }
    }
  }

  /**
   * Get audit statistics for dashboard
   */
  async getAuditStats(days = 30): Promise<{
    totalEvents: number
    securityEvents: number
    userActions: number
    systemEvents: number
    criticalEvents: number
    eventsByType: Record<string, number>
    eventsByDay: Array<{ date: string; count: number }>
  }> {
    try {
      console.log('[AUDIT-LOGGER] Getting audit stats for', days, 'days')
      
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)
      
      console.log('[AUDIT-LOGGER] Querying from date:', startDate.toISOString())

      const { data, error } = await this.supabase
        .from('audit_logs')
        .select('event_type, created_at, details')
        .gte('created_at', startDate.toISOString())

      if (error) {
        console.error('[AUDIT-LOGGER] Stats error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        })
        return {
          totalEvents: 0,
          securityEvents: 0,
          userActions: 0,
          systemEvents: 0,
          criticalEvents: 0,
          eventsByType: {},
          eventsByDay: []
        }
      }

      console.log('[AUDIT-LOGGER] Stats query returned', data?.length || 0, 'records')

      const logs = data || []
      const eventsByType: Record<string, number> = {}
      const eventsByDay: Record<string, number> = {}
      let securityEvents = 0
      let userActions = 0
      let systemEvents = 0
      let criticalEvents = 0

      logs.forEach(log => {
        // Count by type
        eventsByType[log.event_type] = (eventsByType[log.event_type] || 0) + 1

        // Count by day
        const day = log.created_at.split('T')[0]
        eventsByDay[day] = (eventsByDay[day] || 0) + 1

        // Categorize events
        if (log.event_type.includes('security') || log.event_type.includes('token')) {
          securityEvents++
        }
        if (log.event_type.includes('user') || log.event_type.includes('login')) {
          userActions++
        }
        if (log.event_type.includes('system') || log.event_type.includes('domain')) {
          systemEvents++
        }
        if (log.details?.severity === 'critical') {
          criticalEvents++
        }
      })

      const stats = {
        totalEvents: logs.length,
        securityEvents,
        userActions,
        systemEvents,
        criticalEvents,
        eventsByType,
        eventsByDay: Object.entries(eventsByDay).map(([date, count]) => ({ date, count }))
      }
      
      console.log('[AUDIT-LOGGER] Computed stats:', stats)
      return stats
    } catch (err) {
      console.error('[AUDIT-LOGGER] Get stats unexpected error:', err)
      return {
        totalEvents: 0,
        securityEvents: 0,
        userActions: 0,
        systemEvents: 0,
        criticalEvents: 0,
        eventsByType: {},
        eventsByDay: []
      }
    }
  }

  /**
   * Log registration approval event
   */
  async logRegistrationApproval(
    registrationId: string,
    companyId: string,
    approvedBy: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logEvent({
      eventType: 'registration_approved',
      userId: approvedBy,
      companyId,
      resourceType: 'business_registration',
      resourceId: registrationId,
      details: {
        action: 'approve',
        timestamp: new Date().toISOString(),
        ...details
      }
    })
  }
  
  /**
   * Log registration rejection event
   */
  async logRegistrationRejection(
    registrationId: string,
    rejectedBy: string,
    reason: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logEvent({
      eventType: 'registration_rejected',
      userId: rejectedBy,
      resourceType: 'business_registration',
      resourceId: registrationId,
      details: {
        action: 'reject',
        reason,
        timestamp: new Date().toISOString(),
        ...details
      }
    })
  }

  /**
   * Log token generation event
   */
  async logTokenGeneration(
    registrationId: string,
    companyId: string,
    generatedBy: string
  ): Promise<void> {
    await this.logEvent({
      eventType: 'activation_token_generated',
      userId: generatedBy,
      companyId,
      resourceType: 'activation_token',
      resourceId: registrationId,
      details: {
        action: 'generate_token',
        timestamp: new Date().toISOString()
      }
    })
  }
  
  /**
   * Log domain detection event
   */
  async logDomainDetection(
    registrationId: string,
    domain: string,
    detectionResult: any,
    ipAddress?: string
  ): Promise<void> {
    await this.logEvent({
      eventType: 'domain_detection',
      resourceType: 'business_registration',
      resourceId: registrationId,
      ipAddress,
      severity: 'medium',
      details: {
        action: 'domain_detection',
        domain,
        detection_result: detectionResult,
        routing_authority: detectionResult.routing_authority,
        existing_company: detectionResult.existing_company?.company_name || null
      }
    })
  }

  /**
   * Log security event (critical events)
   */
  async logSecurityEvent(
    eventType: string,
    resourceType: string,
    resourceId: string,
    details: Record<string, any>,
    userId?: string,
    ipAddress?: string
  ): Promise<void> {
    await this.logEvent({
      eventType: `security_${eventType}`,
      userId,
      resourceType,
      resourceId,
      ipAddress,
      severity: 'critical',
      details: {
        ...details,
        security_event: true,
        alert_required: true
      }
    })
  }

  /**
   * Export audit logs for compliance (CSV format)
   */
  async exportLogsCSV(filter: AuditFilter = {}): Promise<string> {
    const result = await this.searchLogs({ ...filter, limit: 10000 })
    const headers = [
      'Timestamp',
      'Event Type', 
      'User ID',
      'Company ID',
      'Resource Type',
      'Resource ID',
      'Severity',
      'IP Address',
      'Details'
    ]
    
    const csvRows = [headers.join(',')]
    
    result.logs.forEach(log => {
      const row = [
        log.createdAt,
        log.eventType,
        log.userId || '',
        log.companyId || '',
        log.resourceType,
        log.resourceId,
        log.details?.severity || 'medium',
        log.ipAddress || '',
        JSON.stringify(log.details || {}).replace(/,/g, ';')
      ]
      csvRows.push(row.map(field => `"${field}"`).join(','))
    })
    
    return csvRows.join('\n')
  }
}
