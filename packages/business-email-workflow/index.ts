      const consoleEmails = getConsoleAdminEmails();
      
      for (const adminEmail of consoleEmails) {
        try {
          const result = await sendConsoleAdminNotification(registration, detectionResult, adminEmail);
          if (result.success) {
            emailsSent++;
          } else {
            errors.push(`Console admin notification failed to ${adminEmail}: ${result.error}`);
          }
        } catch (error) {
          errors.push(`Console admin notification error for ${adminEmail}: ${error}`);
        }
      }
      
      console.log(`[ADMIN_NOTIFICATIONS] Console admin emails sent: ${emailsSent}`);
    } else {
      // EXISTING_CUSTOMER: Get company admin emails
      const { data: users, error: usersError } = await adminClient
        .from('users')
        .select('email, profile')
        .eq('company_id', detectionResult.company_id)
        .eq('role', 'admin')
        .eq('is_active', true);

      const adminEmails = users?.map(u => u.email) || [];
      
      // Add primary contact email if available
      if (companies?.[0]?.primary_contact?.email) {
        adminEmails.push(companies[0].primary_contact.email);
      }

      // Remove duplicates
      const uniqueAdminEmails = [...new Set(adminEmails)];
      
      if (uniqueAdminEmails.length === 0) {
        console.warn(`[ADMIN_NOTIFICATIONS] No company admin emails found, routing to console`);
        const consoleEmails = getConsoleAdminEmails();
        
        for (const adminEmail of consoleEmails) {
          try {
            const result = await sendConsoleAdminNotification(registration, detectionResult, adminEmail);
            if (result.success) {
              emailsSent++;
            } else {
              errors.push(`Fallback console notification failed to ${adminEmail}: ${result.error}`);
            }
          } catch (error) {
            errors.push(`Fallback console notification error for ${adminEmail}: ${error}`);
          }
        }
      } else {
        // Send company admin notifications
        for (const adminEmail of uniqueAdminEmails) {
          try {
            const result = await sendCompanyAdminNotification(registration, detectionResult, adminEmail);
            if (result.success) {
              emailsSent++;
            } else {
              errors.push(`Company admin notification failed to ${adminEmail}: ${result.error}`);
            }
          } catch (error) {
            errors.push(`Company admin notification error for ${adminEmail}: ${error}`);
          }
        }
      }
      
      console.log(`[ADMIN_NOTIFICATIONS] Company admin emails sent: ${emailsSent}`);
    }

    return {
      success: errors.length === 0,
      emails_sent: emailsSent,
      errors
    };

  } catch (error) {
    console.error('[ADMIN_NOTIFICATIONS] Workflow error:', error);
    errors.push(error instanceof Error ? error.message : 'Unknown workflow error');
    
    return {
      success: false,
      emails_sent: emailsSent,
      errors
    };
  }
}

/**
 * Get console admin emails from environment
 */
function getConsoleAdminEmails(): string[] {
  const consoleEmails = [
    process.env.CONSOLE_ADMIN_EMAIL || '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  return consoleEmails.filter(email => email && email.includes('@'));
}

/**
 * Send console admin notification email
 */
async function sendConsoleAdminNotification(
  registration: BusinessRegistration,
  detectionResult: DomainDetectionResult,
  adminEmail: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { Resend } = await import('resend');
    const resend = new Resend(process.env.RESEND_API_KEY);

    const subject = `New Business Registration: ${registration.company_name}`;
    const reviewUrl = `${process.env.PUBLIC_CONSOLE_APP_URL || 'http://localhost:3008'}/companies/pending/${registration.id}`;

    const htmlContent = buildConsoleAdminNotificationHtml(registration, detectionResult, reviewUrl);
    const textContent = buildConsoleAdminNotificationText(registration, detectionResult, reviewUrl);

    const { data, error } = await resend.emails.send({
      from: `${process.env.EMAIL_FROM_NAME || 'ProcureServe'} <${process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'}>`,
      to: [adminEmail],
      subject,
      html: htmlContent,
      text: textContent,
      replyTo: process.env.EMAIL_REPLY_TO || '<EMAIL>'
    });

    if (error) {
      console.error('[CONSOLE_ADMIN_EMAIL] Resend error:', error);
      return { success: false, error: error.message };
    }

    console.log(`[CONSOLE_ADMIN_EMAIL] Sent to ${adminEmail}, message ID: ${data?.id}`);
    return { success: true };

  } catch (error) {
    console.error('[CONSOLE_ADMIN_EMAIL] Send error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Email service error' 
    };
  }
}

/**
 * Send company admin notification email
 */
async function sendCompanyAdminNotification(
  registration: BusinessRegistration,
  detectionResult: DomainDetectionResult,
  adminEmail: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { Resend } = await import('resend');
    const resend = new Resend(process.env.RESEND_API_KEY);

    const subject = `New Team Member Registration: ${registration.contact_person_name}`;
    const reviewUrl = `${process.env.PUBLIC_CUSTOMER_APP_URL || 'http://localhost:3004'}/admin/registrations/${registration.id}`;

    const htmlContent = buildCompanyAdminNotificationHtml(registration, detectionResult, reviewUrl);
    const textContent = buildCompanyAdminNotificationText(registration, detectionResult, reviewUrl);

    const { data, error } = await resend.emails.send({
      from: `${process.env.EMAIL_FROM_NAME || 'ProcureServe'} <${process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'}>`,
      to: [adminEmail],
      subject,
      html: htmlContent,
      text: textContent,
      replyTo: process.env.EMAIL_REPLY_TO || '<EMAIL>'
    });

    if (error) {
      console.error('[COMPANY_ADMIN_EMAIL] Resend error:', error);
      return { success: false, error: error.message };
    }

    console.log(`[COMPANY_ADMIN_EMAIL] Sent to ${adminEmail}, message ID: ${data?.id}`);
    return { success: true };

  } catch (error) {
    console.error('[COMPANY_ADMIN_EMAIL] Send error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Email service error' 
    };
  }
}
/**
 * HTML template for console admin notifications
 */
function buildConsoleAdminNotificationHtml(
  registration: BusinessRegistration,
  detectionResult: DomainDetectionResult,
  reviewUrl: string
): string {
  const detectionBadge = detectionResult.type === 'NEW_COMPANY' 
    ? '<span style="background: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600;">NEW COMPANY</span>'
    : '<span style="background: #dcfce7; color: #15803d; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600;">EXISTING CUSTOMER</span>';

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin: 0;">ProcureServe Console</h1>
        <p style="color: #64748b; margin: 5px 0;">Business Registration Review Required</p>
      </div>
      
      <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #f59e0b;">
        <h2 style="color: #92400e; margin-top: 0;">⚡ New Registration Pending Review</h2>
        <p style="color: #92400e; line-height: 1.6;">
          A new business registration requires your immediate attention.
        </p>
      </div>
      
      <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h3 style="color: #1e293b; margin-top: 0;">Registration Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
          <tr style="border-bottom: 1px solid #e2e8f0;">
            <td style="padding: 8px 0; font-weight: 600; color: #475569;">Company:</td>
            <td style="padding: 8px 0; color: #1e293b;">${registration.company_name}</td>
          </tr>
          <tr style="border-bottom: 1px solid #e2e8f0;">
            <td style="padding: 8px 0; font-weight: 600; color: #475569;">Contact:</td>
            <td style="padding: 8px 0; color: #1e293b;">${registration.contact_person_name}</td>
          </tr>
          <tr style="border-bottom: 1px solid #e2e8f0;">
            <td style="padding: 8px 0; font-weight: 600; color: #475569;">Email:</td>
            <td style="padding: 8px 0; color: #1e293b;">${registration.contact_person_email}</td>
          </tr>
          <tr style="border-bottom: 1px solid #e2e8f0;">
            <td style="padding: 8px 0; font-weight: 600; color: #475569;">Domain:</td>
            <td style="padding: 8px 0; color: #1e293b;">${registration.company_domain}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #475569;">Type:</td>
            <td style="padding: 8px 0;">${detectionBadge}</td>
          </tr>
        </table>
      </div>
      
      <div style="text-align: center; margin: 25px 0;">
        <a href="${reviewUrl}" 
           style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;">
          Review Application
        </a>
      </div>
      
      <div style="background: #f1f5f9; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="color: #1e293b; margin-top: 0;">Required Actions:</h4>
        <ul style="color: #475569; line-height: 1.6; margin: 0; padding-left: 20px;">
          <li>Review company information and contact details</li>
          <li>Verify business legitimacy and requirements</li>
          <li>Approve or reject the registration with feedback</li>
          <li>Monitor for any follow-up requirements</li>
        </ul>
      </div>
      
      <div style="text-align: center; margin-top: 30px;">
        <p style="color: #64748b; font-size: 14px;">
          Console Dashboard: <a href="${process.env.PUBLIC_CONSOLE_APP_URL || 'http://localhost:3008'}" style="color: #2563eb;">Admin Portal</a>
        </p>
      </div>
    </div>
  `;
}

/**
 * Text template for console admin notifications
 */
function buildConsoleAdminNotificationText(
  registration: BusinessRegistration,
  detectionResult: DomainDetectionResult,
  reviewUrl: string
): string {
  return `
    ProcureServe Console - New Registration Pending Review
    
    A new business registration requires your immediate attention.
    
    Registration Details:
    Company: ${registration.company_name}
    Contact: ${registration.contact_person_name}
    Email: ${registration.contact_person_email}
    Domain: ${registration.company_domain}
    Type: ${detectionResult.type}
    
    Required Actions:
    - Review company information and contact details
    - Verify business legitimacy and requirements
    - Approve or reject the registration with feedback
    - Monitor for any follow-up requirements
    
    Review Application: ${reviewUrl}
    
    Console Dashboard: ${process.env.PUBLIC_CONSOLE_APP_URL || 'http://localhost:3008'}
  `;
}

/**
 * HTML template for company admin notifications
 */
function buildCompanyAdminNotificationHtml(
  registration: BusinessRegistration,
  detectionResult: DomainDetectionResult,
  reviewUrl: string
): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin: 0;">ProcureServe</h1>
        <p style="color: #64748b; margin: 5px 0;">Team Member Registration Request</p>
      </div>
      
      <div style="background: #dbeafe; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2563eb;">
        <h2 style="color: #1e40af; margin-top: 0;">👥 New Team Member Request</h2>
        <p style="color: #1e40af; line-height: 1.6;">
          Someone from your organization has requested access to ProcureServe.
        </p>
      </div>
      
      <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h3 style="color: #1e293b; margin-top: 0;">Request Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
          <tr style="border-bottom: 1px solid #e2e8f0;">
            <td style="padding: 8px 0; font-weight: 600; color: #475569;">Name:</td>
            <td style="padding: 8px 0; color: #1e293b;">${registration.contact_person_name}</td>
          </tr>
          <tr style="border-bottom: 1px solid #e2e8f0;">
            <td style="padding: 8px 0; font-weight: 600; color: #475569;">Email:</td>
            <td style="padding: 8px 0; color: #1e293b;">${registration.contact_person_email}</td>
          </tr>
          <tr style="border-bottom: 1px solid #e2e8f0;">
            <td style="padding: 8px 0; font-weight: 600; color: #475569;">Company:</td>
            <td style="padding: 8px 0; color: #1e293b;">${registration.company_name}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #475569;">Domain:</td>
            <td style="padding: 8px 0; color: #1e293b;">${registration.company_domain}</td>
          </tr>
        </table>
      </div>
      
      <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h3 style="color: #92400e; margin-top: 0;">⏰ Action Required</h3>
        <p style="color: #92400e; line-height: 1.6;">
          Please review this request within <strong>3 business days</strong>. If no action is taken, 
          the request will be escalated to our support team.
        </p>
      </div>
      
      <div style="text-align: center; margin: 25px 0;">
        <a href="${reviewUrl}" 
           style="background: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block; margin-right: 10px;">
          Review Request
        </a>
      </div>
      
      <div style="text-align: center; margin-top: 30px;">
        <p style="color: #64748b; font-size: 14px;">
          Questions? Contact us at 
          <a href="mailto:${process.env.EMAIL_REPLY_TO || '<EMAIL>'}" style="color: #2563eb;">
            ${process.env.EMAIL_REPLY_TO || '<EMAIL>'}
          </a>
        </p>
      </div>
    </div>
  `;
}

/**
 * Text template for company admin notifications
 */
function buildCompanyAdminNotificationText(
  registration: BusinessRegistration,
  detectionResult: DomainDetectionResult,
  reviewUrl: string
): string {
  return `
    ProcureServe - New Team Member Registration Request
    
    Someone from your organization has requested access to ProcureServe.
    
    Request Details:
    Name: ${registration.contact_person_name}
    Email: ${registration.contact_person_email}
    Company: ${registration.company_name}
    Domain: ${registration.company_domain}
    
    Action Required:
    Please review this request within 3 business days. If no action is taken, 
    the request will be escalated to our support team.
    
    Review Request: ${reviewUrl}
    
    Questions? Contact us at ${process.env.EMAIL_REPLY_TO || '<EMAIL>'}
  `;
}
