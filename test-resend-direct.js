import { Resend } from 'resend'

const RESEND_API_KEY = 're_8MBgcdYb_DaURr46BfLnvsymyY1jqxqjt'
const FROM_EMAIL = '<EMAIL>'

const resend = new Resend(RESEND_API_KEY)

console.log('Testing Resend API configuration...')

async function testEmail() {
  try {
    const { data, error } = await resend.emails.send({
      from: `ProcureServe Test <${FROM_EMAIL}>`,
      to: ['vasa<PERSON><EMAIL>'],
      subject: 'Email Configuration Test',
      html: '<p>✅ Email configuration is working correctly!</p>'
    })

    if (error) {
      console.error('❌ Email test failed:', error)
      return
    }

    console.log('✅ Email test successful:', data)
  } catch (error) {
    console.error('❌ Email test error:', error)
  }
}

testEmail()
