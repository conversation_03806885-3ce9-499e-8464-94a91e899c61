#!/usr/bin/env node

// Test activation with correct localhost URL
import { createClient } from '@supabase/supabase-js';
import { randomBytes, createHash } from 'crypto';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

console.log('🔧 Testing Activation with Localhost URL');

async function testLocalhostActivation() {
  const supabase = createClient(
    process.env.PUBLIC_SUPABASE_URL, 
    process.env.SUPABASE_SERVICE_ROLE_KEY, 
    { auth: { autoRefreshToken: false, persistSession: false } }
  );

  try {
    // Get the test registration
    const { data: registration } = await supabase
      .from('business_registrations')
      .select('*')
      .eq('contact_person_email', '<EMAIL>')
      .single();

    if (!registration) {
      console.log('❌ Test registration not found');
      return;
    }

    console.log('✅ Found registration:', registration.company_name);

    // Generate activation token with localhost URL
    const token = randomBytes(32).toString('hex');
    const hashedToken = createHash('sha256').update(token).digest('hex');
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);
    
    // Use localhost URL for development
    const activationUrl = `http://localhost:3004/activate?token=${token}`;

    console.log('🔐 Generated activation token with localhost URL');
    console.log('   Token:', token.substring(0, 8) + '...');
    console.log('   URL:', activationUrl);

    // Store activation token
    const { data: tokenResult, error: tokenError } = await supabase
      .from('activation_tokens')
      .insert({
        company_id: registration.activated_company_id || registration.detected_company_id,
        registration_id: registration.id,
        token_hash: hashedToken,
        contact_email: registration.contact_person_email,
        contact_name: registration.contact_person_name,
        process_permissions: ['recruitment', 'bench_sales'],
        expires_at: expiresAt.toISOString()
      })
      .select('*')
      .single();

    if (tokenError) {
      console.log('❌ Failed to store token:', tokenError.message);
      return;
    }

    console.log('✅ Activation token stored successfully');

    // Update registration status
    await supabase
      .from('business_registrations')
      .update({
        status: 'approved',
        reviewed_at: new Date().toISOString()
      })
      .eq('id', registration.id);

    console.log('✅ Registration marked as approved');

    // Send email with localhost activation URL
    const { Resend } = await import('resend');
    const resend = new Resend(process.env.RESEND_API_KEY);

    const emailData = {
      from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_FROM_ADDRESS}>`,
      to: [registration.contact_person_email],
      subject: '🎉 Registration Approved - Activate Your Account (Development)',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">ProcureServe</h1>
            <p style="color: #64748b; margin: 5px 0;">Registration Approved - Development Environment</p>
          </div>
          
          <div style="background: #dcfce7; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #16a34a;">
            <h2 style="color: #15803d; margin-top: 0;">🎉 Welcome to ProcureServe!</h2>
            <p style="color: #15803d; line-height: 1.6;">
              Congratulations! Your business registration for <strong>${registration.company_name}</strong> has been approved.
            </p>
          </div>
          
          <div style="text-align: center; margin: 25px 0;">
            <a href="${activationUrl}" 
               style="background: #16a34a; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; font-size: 16px;">
              Activate Your Account
            </a>
          </div>
          
          <div style="background: #dbeafe; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h3 style="color: #1e40af; margin-top: 0;">🖥️ Development Environment</h3>
            <p style="color: #1e40af; line-height: 1.6; margin: 0;">
              This activation link points to your local development server. Make sure your customer app is running on <strong>http://localhost:3004</strong>
            </p>
          </div>
          
          <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h3 style="color: #92400e; margin-top: 0;">⏰ Important</h3>
            <p style="color: #92400e; line-height: 1.6; margin: 0;">
              This activation link expires in 7 days. Please activate your account soon to avoid delays.
            </p>
          </div>
          
          <p style="color: #64748b; font-size: 14px; text-align: center;">
            Development test sent on ${new Date().toISOString()}
          </p>
        </div>
      `
    };

    const emailResult = await resend.emails.send(emailData);

    if (emailResult.data) {
      console.log('✅ Activation email sent with localhost URL!');
      console.log('   Message ID:', emailResult.data.id);
      console.log('\n📋 Next Steps:');
      console.log('   1. Check your email inbox');
      console.log('   2. Make sure customer app is running: npm run dev:customer');
      console.log('   3. Click the activation link in the email');
      console.log('   4. Complete account setup with password');
    } else {
      console.log('❌ Email failed:', emailResult.error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testLocalhostActivation().catch(console.error);
