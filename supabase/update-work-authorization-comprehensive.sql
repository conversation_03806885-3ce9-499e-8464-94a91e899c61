-- Update work_authorization_types with comprehensive EAD codes
-- Run this in Supabase Dashboard > SQL Editor to update the existing enum

UPDATE application_enums 
SET values = '[
  {"key": "us_citizen", "label": "US Citizen", "color": "#22c55e", "active": true, "sort_order": 1},
  {"key": "green_card", "label": "Green Card", "color": "#3b82f6", "active": true, "sort_order": 2},
  {"key": "h1b", "label": "H1B Visa", "color": "#f59e0b", "active": true, "sort_order": 3},
  {"key": "h4", "label": "H4 Dependent", "color": "#f97316", "active": true, "sort_order": 4},
  {"key": "opt", "label": "OPT", "color": "#8b5cf6", "active": true, "sort_order": 5},
  {"key": "cpt", "label": "CPT", "color": "#06b6d4", "active": true, "sort_order": 6},
  {"key": "l1", "label": "L1 Visa", "color": "#10b981", "active": true, "sort_order": 7},
  {"key": "l2", "label": "L2 Dependent", "color": "#84cc16", "active": true, "sort_order": 8},
  {"key": "e3_visa", "label": "E3 Visa", "color": "#a855f7", "active": true, "sort_order": 9},
  {"key": "tn_visa", "label": "TN Visa", "color": "#06b6d4", "active": true, "sort_order": 10},
  {
    "key": "ead", 
    "label": "EAD (Employment Authorization Document)", 
    "color": "#ef4444", 
    "active": true, 
    "sort_order": 11,
    "subcategories": [
      {"key": "a02", "label": "A02 - Lawful Temporary Resident", "description": "Lawful temporary resident pursuant to sections 245A or 210 of the INA"},
      {"key": "a03", "label": "A03 - Refugee", "description": "Refugee status"},
      {"key": "a04", "label": "A04 - Paroled Refugee", "description": "Paroled as refugee"},
      {"key": "a05", "label": "A05 - Asylee", "description": "Asylee (granted asylum)"},
      {"key": "a06", "label": "A06 - K1/K2", "description": "K-1 fiancé(e) or K-2 child of U.S. citizen"},
      {"key": "a07", "label": "A07 - N8/N9", "description": "N-8/N-9 International organization employee family"},
      {"key": "a08", "label": "A08 - Micronesia/Marshall/Palau", "description": "Citizen of Micronesia, Marshall Islands or Palau"},
      {"key": "a09", "label": "A09 - K3/K4", "description": "K-3 spouse or K-4 child of USC"},
      {"key": "a10", "label": "A10 - Withholding", "description": "Granted Withholding of Deportation or Removal"},
      {"key": "a11", "label": "A11 - DED", "description": "Deferred Enforced Departure"},
      {"key": "a12", "label": "A12 - TPS", "description": "Temporary Protected Status"},
      {"key": "a13", "label": "A13 - IMMACT Family Unity", "description": "IMMACT Family Unity beneficiary"},
      {"key": "a14", "label": "A14 - LIFE Act Family Unity", "description": "LIFE Act Family Unity beneficiary"},
      {"key": "a15", "label": "A15 - V1/V2/V3", "description": "V-1/V-2/V-3 LPR family member"},
      {"key": "a16", "label": "A16 - T1 Trafficking Victim", "description": "T-1 nonimmigrant (trafficking victim)"},
      {"key": "a17", "label": "A17 - E1/E2/E3 Spouse", "description": "Spouse of E-1/E-2/E-3 Treaty/Specialty worker"},
      {"key": "a18", "label": "A18 - L2 Spouse", "description": "L-2 spouse of L-1 intracompany transfer"},
      {"key": "a19", "label": "A19 - U1 Crime Victim", "description": "U-1 nonimmigrant (crime victim)"},
      {"key": "a20", "label": "A20 - U2/U3/U4/U5", "description": "U-2/U-3/U-4/U-5 U-1 family members"},
      {"key": "c01", "label": "C01 - A1/A2 Dependent", "description": "Dependent of A-1/A-2 foreign government official"},
      {"key": "c02", "label": "C02 - TECRO Dependent", "description": "Dependent of TECRO E-1 nonimmigrant"},
      {"key": "c03a", "label": "C03A - Pre-completion OPT", "description": "Pre-completion OPT F-1 students"},
      {"key": "c03b", "label": "C03B - Post-completion OPT", "description": "Post-completion OPT F-1 students"},
      {"key": "c03c", "label": "C03C - STEM OPT Extension", "description": "17-month STEM OPT extension"},
      {"key": "c03ii", "label": "C03(ii) - F1 Off-campus", "description": "F-1 off-campus employment (international org)"},
      {"key": "c03iii", "label": "C03(iii) - F1 Economic Hardship", "description": "F-1 off-campus employment (economic hardship)"},
      {"key": "c04", "label": "C04 - G1/G3/G4 Dependent", "description": "Spouse/child of G-1/G-3/G-4 (international org)"},
      {"key": "c05", "label": "C05 - J2 Dependent", "description": "J-2 spouse or child of J-1 exchange visitor"},
      {"key": "c06", "label": "C06 - M1 Practical Training", "description": "M-1 student practical training"},
      {"key": "c07", "label": "C07 - NATO Dependent", "description": "Dependent of NATO-1 through NATO-7"},
      {"key": "c08", "label": "C08 - Asylum Applicant", "description": "Asylum applicant (pending, filed after 1/4/1995)"},
      {"key": "c09", "label": "C09 - Adjustment Applicant", "description": "Adjustment of status applicant"},
      {"key": "c10", "label": "C10 - NACARA", "description": "NACARA section 203 / suspension / cancellation"},
      {"key": "c11", "label": "C11 - Parolee", "description": "Paroled for public interest/emergency"},
      {"key": "c12", "label": "C12 - CNMI E2 Spouse", "description": "E-2 CNMI investor spouse (CNMI only)"},
      {"key": "c14", "label": "C14 - Deferred Action", "description": "Alien granted deferred action"},
      {"key": "c16", "label": "C16 - Registry Applicant", "description": "Registry applicant (continuous residence since 1/1/1972)"},
      {"key": "c17i", "label": "C17(i) - B1 Personal Servant", "description": "B-1 personal/domestic servant of nonimmigrant"},
      {"key": "c17ii", "label": "C17(ii) - B1 USC Servant", "description": "B-1 domestic servant of U.S. citizen"},
      {"key": "c17iii", "label": "C17(iii) - B1 Airline Employee", "description": "B-1 employed by foreign airline"},
      {"key": "c18", "label": "C18 - Final Order", "description": "Final order of deportation/supervision"},
      {"key": "c19", "label": "C19 - TPS Applicant", "description": "Temporary Protected Status applicant"},
      {"key": "c20", "label": "C20 - SAW Applicant", "description": "Special agricultural worker legalization"},
      {"key": "c22", "label": "C22 - 245A Applicant", "description": "Legalization applicant under INA 245A"},
      {"key": "c24", "label": "C24 - LIFE Legalization", "description": "LIFE legalization applicant"},
      {"key": "c25", "label": "C25 - T2/T3/T4", "description": "T-2/T-3/T-4 trafficking victim family"},
      {"key": "c31", "label": "C31 - VAWA Self-Petitioner", "description": "VAWA self-petition beneficiary/qualified child"},
      {"key": "c33", "label": "C33 - DACA", "description": "Deferred Action for Childhood Arrivals"}
    ]
  }
]'::jsonb,
updated_at = NOW()
WHERE category = 'work_authorization_types';

-- Also update the description to reflect the comprehensive EAD support
UPDATE application_enums 
SET description = 'Comprehensive work authorization types including all official EAD codes and visa categories'
WHERE category = 'work_authorization_types';

-- Verification query
SELECT 
  category,
  display_name,
  jsonb_array_length(values) as total_values,
  jsonb_array_length(values->10->'subcategories') as ead_subcategories
FROM application_enums 
WHERE category = 'work_authorization_types';
