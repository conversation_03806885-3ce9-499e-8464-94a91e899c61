-- Enhanced Application-Wide Enum Management Schema
-- This replaces company-specific enum system for core application enums

-- Application-wide enums table (replaces company-scoped configurable_enums for core enums)
CREATE TABLE application_enums (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  category TEXT NOT NULL UNIQUE,
  display_name TEXT NOT NULL,
  description TEXT,
  values JSONB NOT NULL DEFAULT '[]',
  
  -- System metadata
  is_system BOOLEAN DEFAULT false,          -- Core system enums (protected)
  is_editable BOOLEAN DEFAULT true,         -- Can be modified by console
  is_hierarchical BOOLEAN DEFAULT false,    -- Supports subcategories (for work_authorization_types)
  
  -- Versioning and audit
  version INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES console_users(id),
  updated_by UUID REFERENCES console_users(id)
);

-- Enum operation history for application-wide enums
CREATE TABLE application_enum_operations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  enum_id UUID REFERENCES application_enums(id) ON DELETE CASCADE,
  operation_type TEXT CHECK (operation_type IN ('created', 'updated', 'value_added', 'value_removed', 'value_modified')) NOT NULL,
  user_id UUID REFERENCES console_users(id),
  changes JSONB, -- Store what changed
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_application_enums_category ON application_enums(category);
CREATE INDEX idx_application_enums_system ON application_enums(is_system);
CREATE INDEX idx_application_enum_operations_enum_id ON application_enum_operations(enum_id);
CREATE INDEX idx_application_enum_operations_timestamp ON application_enum_operations(timestamp DESC);

-- Row Level Security policies
ALTER TABLE application_enums ENABLE ROW LEVEL SECURITY;
ALTER TABLE application_enum_operations ENABLE ROW LEVEL SECURITY;

-- Console users can read all application enums
CREATE POLICY "Console users can read application enums" ON application_enums
  FOR SELECT USING (
    EXISTS (SELECT 1 FROM console_users WHERE id = auth.uid())
  );

-- Console users can manage application enums based on permissions
CREATE POLICY "Console users can manage application enums" ON application_enums
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM console_users 
      WHERE id = auth.uid() 
      AND role IN ('super_admin', 'company_admin')
    )
  );

-- All users can read enum operations (for audit)
CREATE POLICY "Console users can read enum operations" ON application_enum_operations
  FOR SELECT USING (
    EXISTS (SELECT 1 FROM console_users WHERE id = auth.uid())
  );

-- Console users can insert enum operations
CREATE POLICY "Console users can create enum operations" ON application_enum_operations
  FOR INSERT WITH CHECK (
    EXISTS (SELECT 1 FROM console_users WHERE id = auth.uid())
  );
