-- This migration creates the necessary schema for the enterprise-grade Console App.
-- It establishes a separate, secure system for administrative users, completely
-- isolated from the main customer and candidate data.

-- 1. Create console_users table
-- This table stores login and role information for console administrators.
CREATE TABLE IF NOT EXISTS public.console_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT NOT NULL UNIQUE,
    role TEXT NOT NULL CHECK (role IN ('super_admin', 'company_admin', 'company_manager')),
    company_ids TEXT[],
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMPTZ,
    mfa_enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
COMMENT ON TABLE public.console_users IS 'Stores authentication and role data for console administrators.';

-- 2. Create console_user_permissions table
-- A flexible, resource-based permission system for fine-grained access control.
CREATE TABLE IF NOT EXISTS public.console_user_permissions (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    user_id UUID NOT NULL REFERENCES public.console_users(id) ON DELETE CASCADE,
    resource TEXT NOT NULL,
    actions TEXT[] NOT NULL,
    company_id UUID,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, resource, company_id)
);
COMMENT ON TABLE public.console_user_permissions IS 'Defines specific permissions for console users on various resources.';

-- 3. Create console_security_events table
-- A comprehensive audit log for all significant security-related events.
CREATE TABLE IF NOT EXISTS public.console_security_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type TEXT NOT NULL,
    user_id TEXT,
    user_email TEXT,
    user_role TEXT,
    success BOOLEAN NOT NULL,
    error_message TEXT,
    ip_address TEXT,
    metadata JSONB,
    timestamp TIMESTAMPTZ DEFAULT NOW()
);
COMMENT ON TABLE public.console_security_events IS 'Audit trail for security-sensitive actions within the console.';

-- 4. Create console_user_invitations table
-- Manages the invitation flow for new console users.
CREATE TABLE IF NOT EXISTS public.console_user_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT NOT NULL UNIQUE,
    role TEXT NOT NULL,
    company_id UUID,
    permissions JSONB,
    token TEXT NOT NULL UNIQUE,
    expires_at TIMESTAMPTZ NOT NULL,
    invited_by UUID NOT NULL REFERENCES public.console_users(id),
    created_at TIMESTAMPTZ DEFAULT NOW()
);
COMMENT ON TABLE public.console_user_invitations IS 'Stores pending invitations for new console users.';

-- 5. Create enum_operations table
-- Tracks changes to the configurable enums for auditing and versioning.
CREATE TABLE IF NOT EXISTS public.enum_operations (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    enum_id BIGINT NOT NULL,
    operation_type TEXT NOT NULL, -- e.g., 'create', 'update', 'delete'
    user_id UUID NOT NULL REFERENCES public.console_users(id),
    details JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
COMMENT ON TABLE public.enum_operations IS 'Logs all create, update, and delete operations on enums for auditing.';

-- 6. Add Row-Level Security (RLS)
-- Enable RLS for all console-related tables as a security best practice.
ALTER TABLE public.console_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.console_user_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.console_security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.console_user_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.enum_operations ENABLE ROW LEVEL SECURITY;

-- 7. Create Indexes for Performance
-- Add indexes to foreign keys and commonly queried columns.
CREATE INDEX IF NOT EXISTS idx_console_users_email ON public.console_users(email);
CREATE INDEX IF NOT EXISTS idx_console_user_permissions_user_id ON public.console_user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_console_security_events_event_type ON public.console_security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_console_security_events_user_id ON public.console_security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_console_user_invitations_token ON public.console_user_invitations(token);
CREATE INDEX IF NOT EXISTS idx_enum_operations_enum_id ON public.enum_operations(enum_id);
CREATE INDEX IF NOT EXISTS idx_enum_operations_user_id ON public.enum_operations(user_id);
CREATE INDEX IF NOT EXISTS idx_enum_operations_created_at ON public.enum_operations(created_at DESC); 