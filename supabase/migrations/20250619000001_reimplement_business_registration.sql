-- Step 1: Create a table for incoming business registration leads.
-- This decouples the initial submission from user/company creation, allowing for an admin review process.
CREATE TABLE IF NOT EXISTS public.business_leads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_name TEXT NOT NULL,
    primary_contact JSONB NOT NULL,
    company_details JSONB NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending_review', -- e.g., pending_review, approved, rejected
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- RLS Policies for business_leads
-- Only service_role users (i.e., our backend) can insert new leads.
-- Authenticated console users (admins) can read and update them for the review process.
ALTER TABLE public.business_leads ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Enable read access for console admins" ON public.business_leads;
CREATE POLICY "Enable read access for console admins"
ON public.business_leads FOR SELECT
TO authenticated
USING (
  (SELECT role FROM public.users WHERE id = auth.uid()) = 'console_admin'
);

DROP POLICY IF EXISTS "Enable update for console admins" ON public.business_leads;
CREATE POLICY "Enable update for console admins"
ON public.business_leads FOR UPDATE
TO authenticated
USING (
  (SELECT role FROM public.users WHERE id = auth.uid()) = 'console_admin'
)
WITH CHECK (
  (SELECT role FROM public.users WHERE id = auth.uid()) = 'console_admin'
);


-- Step 2: Create a table to blacklist company domains.
-- This provides a mechanism to prevent registrations from known bad actors.
CREATE TABLE IF NOT EXISTS public.company_blacklist (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    domain TEXT NOT NULL UNIQUE,
    reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- RLS Policies for company_blacklist
-- Only console admins can manage the blacklist.
ALTER TABLE public.company_blacklist ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Enable full access for console admins" ON public.company_blacklist;
CREATE POLICY "Enable full access for console admins"
ON public.company_blacklist FOR ALL
TO authenticated
USING (
  (SELECT role FROM public.users WHERE id = auth.uid()) = 'console_admin'
)
WITH CHECK (
  (SELECT role FROM public.users WHERE id = auth.uid()) = 'console_admin'
);


-- Step 3: Drop the old, problematic RPC function.
-- This function is being replaced by the new application review workflow.
DROP FUNCTION IF EXISTS public.create_business_registration(
    TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN, BOOLEAN, TEXT, JSONB, JSONB, JSONB, TEXT, TEXT
); 