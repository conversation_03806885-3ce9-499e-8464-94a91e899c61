-- Update activation_tokens table to support the new secure activation flow
-- This migration ensures the table has all required columns for the business registration flow

-- Create activation_tokens table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.activation_tokens (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
  token TEXT NOT NULL UNIQUE,
  contact_email TEXT NOT NULL,
  contact_name TEXT NOT NULL,
  process_permissions TEXT[] DEFAULT ARRAY['recruitment', 'bench_sales'],
  used BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMPTZ,
  activated_user_id UUID REFERENCES auth.users(id),
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_activation_tokens_token ON public.activation_tokens(token);
CREATE INDEX IF NOT EXISTS idx_activation_tokens_company_id ON public.activation_tokens(company_id);
CREATE INDEX IF NOT EXISTS idx_activation_tokens_contact_email ON public.activation_tokens(contact_email);
CREATE INDEX IF NOT EXISTS idx_activation_tokens_expires_at ON public.activation_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_activation_tokens_used ON public.activation_tokens(used);

-- Add RLS policies
ALTER TABLE public.activation_tokens ENABLE ROW LEVEL SECURITY;

-- Policy for service role to manage all tokens
CREATE POLICY "Service role can manage activation tokens" ON public.activation_tokens
  FOR ALL USING (auth.role() = 'service_role');

-- Function to cleanup expired tokens (for maintenance)
CREATE OR REPLACE FUNCTION public.cleanup_expired_activation_tokens()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete expired tokens that are older than 30 days
  DELETE FROM public.activation_tokens 
  WHERE expires_at < NOW() - INTERVAL '30 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$;

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION public.set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS set_activation_tokens_updated_at ON public.activation_tokens;
CREATE TRIGGER set_activation_tokens_updated_at
  BEFORE UPDATE ON public.activation_tokens
  FOR EACH ROW EXECUTE FUNCTION public.set_updated_at();

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.activation_tokens TO service_role;
GRANT USAGE ON SCHEMA public TO service_role; 