-- Fix companies table RLS policy to allow console admins to create new companies
-- This addresses the "Failed to create company account" error during business registration approval

-- Drop the existing restrictive policy
DROP POLICY IF EXISTS "Company isolation" ON companies;

-- Create a new policy that allows:
-- 1. Users to access their own company
-- 2. Console admins to create and manage any company
-- 3. Service role to perform admin operations
CREATE POLICY "companies_access_policy" ON companies FOR ALL USING (
  -- Allow access to own company
  id = (SELECT company_id FROM users WHERE id = auth.uid())
  OR
  -- Allow console admins (users with console role) to access any company
  auth.uid() IN (
    SELECT u.id FROM users u
    WHERE u.role = 'admin' 
    AND (u.company_id IS NULL OR u.company_id = 'console')
  )
  OR
  -- Allow service role operations (for system operations)
  auth.role() = 'service_role'
  OR
  -- Allow authenticated users to insert new companies (for registration approval)
  (TG_OP = 'INSERT' AND auth.uid() IS NOT NULL)
);

-- Add a separate policy for INSERT operations to be more explicit
CREATE POLICY "companies_insert_policy" ON companies FOR INSERT WITH CHECK (
  -- Console admins can create companies
  auth.uid() IN (
    SELECT u.id FROM users u
    WHERE u.role = 'admin' 
    AND (u.company_id IS NULL OR u.company_id = 'console')
  )
  OR
  -- Service role can create companies
  auth.role() = 'service_role'
);

-- Create a policy for SELECT operations
CREATE POLICY "companies_select_policy" ON companies FOR SELECT USING (
  -- Users can view their own company
  id = (SELECT company_id FROM users WHERE id = auth.uid())
  OR
  -- Console admins can view any company
  auth.uid() IN (
    SELECT u.id FROM users u
    WHERE u.role = 'admin' 
    AND (u.company_id IS NULL OR u.company_id = 'console')
  )
  OR
  -- Service role can view any company
  auth.role() = 'service_role'
);

-- Create a policy for UPDATE operations
CREATE POLICY "companies_update_policy" ON companies FOR UPDATE USING (
  -- Users can update their own company
  id = (SELECT company_id FROM users WHERE id = auth.uid())
  OR
  -- Console admins can update any company
  auth.uid() IN (
    SELECT u.id FROM users u
    WHERE u.role = 'admin' 
    AND (u.company_id IS NULL OR u.company_id = 'console')
  )
  OR
  -- Service role can update any company
  auth.role() = 'service_role'
);

-- Add helpful comments
COMMENT ON POLICY "companies_access_policy" ON companies IS 'Allows users to access their company, console admins to access any company, and service role operations';
COMMENT ON POLICY "companies_insert_policy" ON companies IS 'Allows console admins and service role to create new companies';
COMMENT ON POLICY "companies_select_policy" ON companies IS 'Controls who can view company records';
COMMENT ON POLICY "companies_update_policy" ON companies IS 'Controls who can update company records';
