-- Description: Simplifies the create_verification_token function to only generate and store a token,
-- removing the email sending logic which will be handled by an external service.
-- This allows for a custom email provider (e.g., Zeptomail) to be used from the backend.

CREATE OR REPLACE FUNCTION public.create_verification_token(
  p_user_id UUID,
  p_email TEXT,
  p_first_name TEXT
)
RETURNS TEXT -- Returns the generated token
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  generated_token TEXT;
  verification_link TEXT;
  request_id BIGINT;
BEGIN
  -- Generate a secure, unique token
  generated_token := gen_random_uuid()::TEXT;

  -- Store the token with an expiry date (e.g., 24 hours from now)
  INSERT INTO public.email_verification_tokens (user_id, email, token, expires_at)
  VALUES (p_user_id, p_email, generated_token, NOW() + INTERVAL '24 hours');

  -- Return the token to the calling function
  <PERSON><PERSON><PERSON><PERSON> generated_token;
END;
$$;

COMMENT ON FUNCTION public.create_verification_token(UUID, TEXT, TEXT) IS 
'Creates and stores an email verification token for a user, returning the token. Email sending is handled by a separate backend service.'; 