-- Fix console_user_invitations table schema
-- This migration adds missing columns that are being used in the application

-- Add status column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'console_user_invitations'
        AND column_name = 'status'
    ) THEN
        ALTER TABLE console_user_invitations 
        ADD COLUMN status TEXT DEFAULT 'pending' 
        CHECK (status IN ('pending', 'accepted', 'expired', 'cancelled'));
    END IF;
END $$;

-- Add updated_at column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'console_user_invitations'
        AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE console_user_invitations 
        ADD COLUMN updated_at TIMESTAMPTZ DEFAULT NOW();
    END IF;
END $$;

-- Add first_name and last_name columns to console_users if they don't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'console_users'
        AND column_name = 'first_name'
    ) THEN
        ALTER TABLE console_users 
        ADD COLUMN first_name TEXT;
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'console_users'
        AND column_name = 'last_name'
    ) THEN
        ALTER TABLE console_users 
        ADD COLUMN last_name TEXT;
    END IF;
END $$;

-- Create trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Fix console_security_events table - add missing resource column
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'console_security_events'
        AND column_name = 'resource'
    ) THEN
        ALTER TABLE console_security_events 
        ADD COLUMN resource TEXT;
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'console_security_events'
        AND column_name = 'resource_id'
    ) THEN
        ALTER TABLE console_security_events 
        ADD COLUMN resource_id TEXT;
    END IF;
END $$;

-- Drop trigger if exists and recreate
DROP TRIGGER IF EXISTS update_console_user_invitations_updated_at ON console_user_invitations;
CREATE TRIGGER update_console_user_invitations_updated_at 
BEFORE UPDATE ON console_user_invitations 
FOR EACH ROW 
EXECUTE FUNCTION update_updated_at_column(); 