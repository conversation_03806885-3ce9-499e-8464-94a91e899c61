-- Enhanced Business Registration Domain Detection System
-- This migration adds domain detection capabilities and admin email selection

-- Add domain detection and admin email fields to business_leads
ALTER TABLE public.business_leads 
ADD COLUMN IF NOT EXISTS domain_extracted TEXT,
ADD COLUMN IF NOT EXISTS domain_detection_result JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS activation_admin_email TEXT,
ADD COLUMN IF NOT EXISTS activation_admin_name TEXT,
ADD COLUMN IF NOT EXISTS company_admin_notified_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS priority_level TEXT DEFAULT 'normal' CHECK (priority_level IN ('low', 'normal', 'high', 'urgent'));

-- Add domain detection metadata structure
COMMENT ON COLUMN public.business_leads.domain_detection_result IS 
'JSON structure: {
  "type": "NEW_COMPANY" | "EXISTING_CUSTOMER", 
  "matched_company_id": "uuid", 
  "confidence_score": 0.95,
  "related_domains": ["subdomain.company.com"],
  "detection_timestamp": "2025-01-20T10:30:00Z"
}';

-- Function to extract domain from email
CREATE OR REPLACE FUNCTION extract_email_domain(email_address TEXT)
RETURNS TEXT AS $$
BEGIN
  -- Extract domain part from email (everything after @)
  RETURN LOWER(TRIM(SPLIT_PART(email_address, '@', 2)));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to perform domain detection
CREATE OR REPLACE FUNCTION detect_company_domain(email_domain TEXT)
RETURNS JSONB AS $$
DECLARE
  existing_company RECORD;
  detection_result JSONB;
BEGIN
  -- Look for existing company with this domain
  SELECT id, name, domain INTO existing_company
  FROM public.companies 
  WHERE domain = email_domain 
    AND registration_status = 'approved'
  LIMIT 1;
  
  IF existing_company.id IS NOT NULL THEN
    -- Existing customer found
    detection_result := jsonb_build_object(
      'type', 'EXISTING_CUSTOMER',
      'matched_company_id', existing_company.id,
      'matched_company_name', existing_company.name,
      'confidence_score', 1.0,
      'detection_timestamp', NOW()
    );
  ELSE
    -- New company
    detection_result := jsonb_build_object(
      'type', 'NEW_COMPANY',
      'confidence_score', 1.0,
      'detection_timestamp', NOW()
    );
  END IF;
  
  RETURN detection_result;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to auto-populate domain detection on insert/update
CREATE OR REPLACE FUNCTION auto_detect_domain()
RETURNS TRIGGER AS $$
BEGIN
  -- Extract domain from primary contact email
  NEW.domain_extracted := extract_email_domain(NEW.primary_contact->>'email');
  
  -- Perform domain detection
  NEW.domain_detection_result := detect_company_domain(NEW.domain_extracted);
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic domain detection
DROP TRIGGER IF EXISTS trigger_auto_detect_domain ON public.business_leads;
CREATE TRIGGER trigger_auto_detect_domain
  BEFORE INSERT OR UPDATE ON public.business_leads
  FOR EACH ROW
  EXECUTE FUNCTION auto_detect_domain();

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_business_leads_domain_extracted 
ON public.business_leads(domain_extracted);

CREATE INDEX IF NOT EXISTS idx_business_leads_detection_type 
ON public.business_leads ((domain_detection_result->>'type'));

CREATE INDEX IF NOT EXISTS idx_business_leads_priority_status 
ON public.business_leads(priority_level, status);

-- Add domain field to companies table if not exists
ALTER TABLE public.companies 
ADD COLUMN IF NOT EXISTS domain TEXT;

-- Extract and populate domain for existing companies
UPDATE public.companies 
SET domain = extract_email_domain(primary_contact->>'email')
WHERE domain IS NULL AND primary_contact->>'email' IS NOT NULL;

-- Add unique constraint on company domain
CREATE UNIQUE INDEX IF NOT EXISTS idx_companies_domain_unique 
ON public.companies(domain) 
WHERE domain IS NOT NULL AND registration_status = 'approved';

-- Update existing business_leads with domain detection
UPDATE public.business_leads 
SET 
  domain_extracted = extract_email_domain(primary_contact->>'email'),
  domain_detection_result = detect_company_domain(extract_email_domain(primary_contact->>'email'))
WHERE domain_extracted IS NULL 
  AND primary_contact->>'email' IS NOT NULL;

-- Add RLS policy for domain detection queries
CREATE POLICY "Enable domain detection queries"
ON public.companies FOR SELECT
TO authenticated, anon
USING (registration_status = 'approved');
