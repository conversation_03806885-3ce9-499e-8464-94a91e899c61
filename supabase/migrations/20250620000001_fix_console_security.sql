-- CRITICAL SECURITY FIX: Remove company associations from console users
-- Console users are ProcureServe internal staff and should NEVER belong to client companies

-- 1. Remove company_ids column from console_users (it's a security risk)
ALTER TABLE console_users DROP COLUMN IF EXISTS company_ids;

-- 2. Add proper internal-only validation
ALTER TABLE console_users ADD CONSTRAINT console_users_email_domain_check 
CHECK (email LIKE '%@procureserve.com');

-- 3. Update console_user_invitations to remove company_id requirement
-- Console users don't belong to companies - they manage all companies
ALTER TABLE console_user_invitations DROP COLUMN IF EXISTS company_id;

-- 4. Clean up any existing permissions that reference specific companies
-- Console users should have global permissions, not company-specific ones
DELETE FROM console_user_permissions WHERE company_id IS NOT NULL;

-- 5. Update console user roles to be more specific about internal operations
COMMENT ON TABLE console_users IS 'ProcureServe internal staff with console access - NEVER client company users';
COMMENT ON TABLE console_user_invitations IS 'Invitations for ProcureServe internal staff only - not client users';

-- 6. Add security audit trigger
CREATE OR REPLACE FUNCTION audit_console_user_changes()
R<PERSON>URNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Verify email domain on insert
        IF NEW.email NOT LIKE '%@procureserve.com' THEN
            RAISE EXCEPTION 'Console users must have @procureserve.com email addresses';
        END IF;
        
        INSERT INTO console_security_events (
            event_type, user_email, user_role, success, metadata, timestamp
        ) VALUES (
            'console_user_created', NEW.email, NEW.role, true, 
            jsonb_build_object('user_id', NEW.id), NOW()
        );
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'UPDATE' THEN
        -- Verify email domain on update
        IF NEW.email NOT LIKE '%@procureserve.com' THEN
            RAISE EXCEPTION 'Console users must have @procureserve.com email addresses';
        END IF;
        
        INSERT INTO console_security_events (
            event_type, user_email, user_role, success, metadata, timestamp
        ) VALUES (
            'console_user_updated', NEW.email, NEW.role, true, 
            jsonb_build_object('user_id', NEW.id, 'changes', jsonb_build_object('old_role', OLD.role, 'new_role', NEW.role)), NOW()
        );
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DROP TRIGGER IF EXISTS console_user_audit_trigger ON console_users;
CREATE TRIGGER console_user_audit_trigger
    AFTER INSERT OR UPDATE ON console_users
    FOR EACH ROW EXECUTE FUNCTION audit_console_user_changes();
