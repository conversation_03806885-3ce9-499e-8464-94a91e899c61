-- Fix verification function permissions to ensure proper access
-- This migration ensures that the email verification functions have the correct permissions

-- Grant necessary permissions to authenticated role as well for admin operations
GRANT EXECUTE ON FUNCTION public.verify_email_token(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_verification_token(UUID, TEXT, TEXT) TO authenticated;

-- Ensure the service role can also access the auth schema for user updates
GRANT USAGE ON SCHEMA auth TO service_role;

-- Create or replace the verification function with better error handling
CREATE OR REPLACE FUNCTION public.verify_email_token(p_token TEXT)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  token_record RECORD;
  result JSON;
BEGIN
  -- Find the token
  SELECT * INTO token_record
  FROM public.email_verification_tokens
  WHERE token = p_token;

  -- Check if token exists
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'message', 'Invalid verification token.',
      'user_id', null,
      'email', null
    );
  END IF;

  -- Check if token has already been used
  IF token_record.used_at IS NOT NULL THEN
    RETURN json_build_object(
      'success', false,
      'message', 'This verification link has already been used.',
      'user_id', token_record.user_id,
      'email', token_record.email
    );
  END IF;

  -- Check if token has expired
  IF token_record.expires_at < NOW() THEN
    RETURN json_build_object(
      'success', false,
      'message', 'This verification link has expired. Please request a new one.',
      'user_id', token_record.user_id,
      'email', token_record.email
    );
  END IF;

  -- Token is valid, mark it as used
  UPDATE public.email_verification_tokens
  SET used_at = NOW()
  WHERE token = p_token;

  -- Update the user's email_confirmed_at in auth.users using service role privileges
  UPDATE auth.users
  SET email_confirmed_at = NOW(),
      updated_at = NOW()
  WHERE id = token_record.user_id;

  -- Return success
  RETURN json_build_object(
    'success', true,
    'message', 'Email verified successfully! You can now sign in.',
    'user_id', token_record.user_id,
    'email', token_record.email
  );

EXCEPTION
  WHEN OTHERS THEN
    -- Log the error with more detail
    RAISE LOG 'Error in verify_email_token for token %: %', substring(p_token, 1, 8), SQLERRM;
    
    RETURN json_build_object(
      'success', false,
      'message', 'Verification failed due to a technical issue. Please try again.',
      'user_id', COALESCE(token_record.user_id, null),
      'email', COALESCE(token_record.email, null)
    );
END;
$$;

-- Ensure proper table ownership and permissions
ALTER TABLE public.email_verification_tokens OWNER TO supabase_admin;
GRANT ALL ON public.email_verification_tokens TO supabase_admin;

COMMENT ON MIGRATION IS 'Fix email verification function permissions to resolve authentication issues';