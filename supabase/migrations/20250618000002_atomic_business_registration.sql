-- Creates a transactional function to securely handle new business registrations.
-- This single function call from the server-side ensures that the creation of
-- a company, an auth user, and a public user profile is an atomic operation.
-- If any step fails, the entire transaction is rolled back, preventing orphaned data.
CREATE OR REPLACE FUNCTION public.create_business_registration(
    company_name TEXT,
    legal_entity_type TEXT,
    tax_id TEXT,
    business_type TEXT,
    estimated_annual_volume TEXT,
    recruitment_enabled BOOLEAN,
    bench_sales_enabled BOOLEAN,
    time_zone TEXT,
    business_address JSONB,
    primary_contact JSONB,
    working_hours JSONB,
    user_email TEXT,
    user_password TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
-- SECURITY DEFINER allows this function to run with the privileges of the user who defined it (the admin),
-- which is necessary to call auth.signup.
SECURITY DEFINER
-- Set a secure search_path to prevent hijacking.
SET search_path = public
AS $$
DECLARE
    new_company_id UUID;
    new_user_id UUID;
    user_first_name TEXT;
    user_last_name TEXT;
BEGIN
    -- 1. Input Validation
    IF company_name IS NULL OR user_email IS NULL OR user_password IS NULL THEN
        RAISE EXCEPTION 'Company name, user email, and password cannot be null';
    END IF;

    -- 2. Insert Company
    INSERT INTO public.companies (
        name, legal_entity_type, tax_id, business_type, estimated_annual_volume,
        recruitment_enabled, bench_sales_enabled, time_zone, business_address,
        primary_contact, working_hours, registration_status, domain, settings
    )
    VALUES (
        company_name, legal_entity_type, tax_id, business_type, estimated_annual_volume,
        recruitment_enabled, bench_sales_enabled, time_zone, business_address,
        primary_contact, working_hours, 'pending_approval',
        LOWER(REGEXP_REPLACE(company_name, '[^a-z0-9]', '', 'g')) || '.procureserve.com',
        '{"onboarding_completed": false}'
    )
    RETURNING id INTO new_company_id;

    -- 3. Extract user data from JSON for convenience
    user_first_name := primary_contact->>'first_name';
    user_last_name := primary_contact->>'last_name';

    -- 4. Create Auth User using Supabase's built-in signup function.
    -- This securely handles password hashing and creates records in both
    -- auth.users and auth.identities.
    SELECT auth.signup(
        email := user_email,
        password := user_password,
        options := jsonb_build_object(
            'data', jsonb_build_object(
                'first_name', user_first_name,
                'last_name', user_last_name,
                'company_id', new_company_id
            )
        )
    ) INTO new_user_id;

    -- 5. Create Public User Profile
    -- This table mirrors the auth user and links to the company, holding business-specific roles and permissions.
    INSERT INTO public.users (
        id, company_id, email, first_name, last_name, role, process_permissions, current_process
    )
    VALUES (
        new_user_id,
        new_company_id,
        user_email,
        user_first_name,
        user_last_name,
        'company_admin', -- The person registering is the default admin for the company
        -- Grant permissions based on what they selected in the registration form
        CASE
            WHEN recruitment_enabled AND bench_sales_enabled THEN ARRAY['recruitment', 'bench_sales']
            WHEN recruitment_enabled THEN ARRAY['recruitment']
            WHEN bench_sales_enabled THEN ARRAY['bench_sales']
            ELSE '{}'::TEXT[]
        END,
        -- Set current_process if only one business process is selected, otherwise requires user selection
        CASE
            WHEN recruitment_enabled AND NOT bench_sales_enabled THEN 'recruitment'
            WHEN bench_sales_enabled AND NOT recruitment_enabled THEN 'bench_sales'
            ELSE NULL
        END
    );

    -- 6. Return success state with new IDs
    RETURN jsonb_build_object('company_id', new_company_id, 'user_id', new_user_id);
END;
$$; 