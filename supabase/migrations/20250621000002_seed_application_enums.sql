-- Seed core application enums
-- These are the foundational enums that the application depends on

-- Work Authorization Types (with hierarchical EAD support)
INSERT INTO application_enums (category, display_name, description, values, is_system, is_hierarchical) VALUES (
  'work_authorization_types',
  'Work Authorization Types',
  'Types of work authorization status for candidates including EAD subcategories',
  '[
    {"key": "us_citizen", "label": "US Citizen", "color": "#22c55e", "active": true, "sort_order": 1},
    {"key": "green_card", "label": "Green Card", "color": "#3b82f6", "active": true, "sort_order": 2},
    {"key": "h1b", "label": "H1B Visa", "color": "#f59e0b", "active": true, "sort_order": 3},
    {"key": "opt", "label": "OPT", "color": "#8b5cf6", "active": true, "sort_order": 4},
    {"key": "tn_visa", "label": "TN Visa", "color": "#06b6d4", "active": true, "sort_order": 5},
    {"key": "e3_visa", "label": "E3 Visa", "color": "#10b981", "active": true, "sort_order": 6},
    {
      "key": "ead", 
      "label": "EAD (Employment Authorization Document)", 
      "color": "#f97316", 
      "active": true, 
      "sort_order": 7,
      "subcategories": [
        {"key": "h4_ead", "label": "H4 EAD", "description": "H4 spouse employment authorization"},
        {"key": "l2_ead", "label": "L2 EAD", "description": "L2 spouse employment authorization"},
        {"key": "e2_ead", "label": "E2 EAD", "description": "E2 treaty investor spouse"},
        {"key": "asylum_ead", "label": "Asylum EAD", "description": "Asylum-based employment authorization"},
        {"key": "refugee_ead", "label": "Refugee EAD", "description": "Refugee employment authorization"},
        {"key": "tps_ead", "label": "TPS EAD", "description": "Temporary Protected Status"},
        {"key": "daca_ead", "label": "DACA EAD", "description": "Deferred Action for Childhood Arrivals"}
      ]
    }
  ]'::jsonb,
  true,
  true
);

-- Job Statuses
INSERT INTO application_enums (category, display_name, description, values, is_system) VALUES (
  'job_statuses',
  'Job Statuses',
  'Current status of job postings in the system',
  '[
    {"key": "draft", "label": "Draft", "color": "#6b7280", "active": true, "sort_order": 1},
    {"key": "open", "label": "Open", "color": "#22c55e", "active": true, "sort_order": 2},
    {"key": "in_progress", "label": "In Progress", "color": "#f59e0b", "active": true, "sort_order": 3},
    {"key": "on_hold", "label": "On Hold", "color": "#ef4444", "active": true, "sort_order": 4},
    {"key": "closed", "label": "Closed", "color": "#374151", "active": true, "sort_order": 5},
    {"key": "cancelled", "label": "Cancelled", "color": "#991b1b", "active": true, "sort_order": 6}
  ]'::jsonb,
  true
);

-- Interview Types (including PreScreen)
INSERT INTO application_enums (category, display_name, description, values, is_system) VALUES (
  'interview_types',
  'Interview Types',
  'Types of interviews and screening methods',
  '[
    {"key": "prescreen", "label": "Pre-Screen", "color": "#8b5cf6", "active": true, "sort_order": 1},
    {"key": "phone", "label": "Phone Interview", "color": "#3b82f6", "active": true, "sort_order": 2},
    {"key": "video", "label": "Video Interview", "color": "#22c55e", "active": true, "sort_order": 3},
    {"key": "online_assessment", "label": "Online Assessment", "color": "#f59e0b", "active": true, "sort_order": 4},
    {"key": "in_person", "label": "In-Person Interview", "color": "#ef4444", "active": true, "sort_order": 5},
    {"key": "panel", "label": "Panel Interview", "color": "#10b981", "active": true, "sort_order": 6},
    {"key": "technical", "label": "Technical Interview", "color": "#f97316", "active": true, "sort_order": 7}
  ]'::jsonb,
  true
);

-- Employment Types (comprehensive list)
INSERT INTO application_enums (category, display_name, description, values, is_system) VALUES (
  'employment_types',
  'Employment Types',
  'Types of employment arrangements and contracts',
  '[
    {"key": "full_time", "label": "Full Time", "color": "#22c55e", "active": true, "sort_order": 1},
    {"key": "part_time", "label": "Part Time", "color": "#3b82f6", "active": true, "sort_order": 2},
    {"key": "contract", "label": "Contract", "color": "#f59e0b", "active": true, "sort_order": 3},
    {"key": "contract_to_hire", "label": "Contract to Hire", "color": "#8b5cf6", "active": true, "sort_order": 4},
    {"key": "freelance", "label": "Freelance", "color": "#06b6d4", "active": true, "sort_order": 5},
    {"key": "temporary", "label": "Temporary", "color": "#ef4444", "active": true, "sort_order": 6},
    {"key": "internship", "label": "Internship", "color": "#10b981", "active": true, "sort_order": 7},
    {"key": "need_basis", "label": "Need Basis", "color": "#f97316", "active": true, "sort_order": 8},
    {"key": "project_based", "label": "Project Based", "color": "#84cc16", "active": true, "sort_order": 9},
    {"key": "seasonal", "label": "Seasonal", "color": "#a855f7", "active": true, "sort_order": 10}
  ]'::jsonb,
  true
);

-- Skill Levels (dynamic for growing skills)
INSERT INTO application_enums (category, display_name, description, values, is_system) VALUES (
  'skill_levels',
  'Skill Levels',
  'Proficiency levels for skills and competencies',
  '[
    {"key": "beginner", "label": "Beginner", "color": "#ef4444", "active": true, "sort_order": 1},
    {"key": "intermediate", "label": "Intermediate", "color": "#f59e0b", "active": true, "sort_order": 2},
    {"key": "advanced", "label": "Advanced", "color": "#22c55e", "active": true, "sort_order": 3},
    {"key": "expert", "label": "Expert", "color": "#8b5cf6", "active": true, "sort_order": 4},
    {"key": "master", "label": "Master", "color": "#dc2626", "active": true, "sort_order": 5}
  ]'::jsonb,
  false
);

-- Interview Statuses
INSERT INTO application_enums (category, display_name, description, values, is_system) VALUES (
  'interview_statuses',
  'Interview Statuses',
  'Status of interviews in the process',
  '[
    {"key": "scheduled", "label": "Scheduled", "color": "#3b82f6", "active": true, "sort_order": 1},
    {"key": "in_progress", "label": "In Progress", "color": "#f59e0b", "active": true, "sort_order": 2},
    {"key": "completed", "label": "Completed", "color": "#22c55e", "active": true, "sort_order": 3},
    {"key": "cancelled", "label": "Cancelled", "color": "#ef4444", "active": true, "sort_order": 4},
    {"key": "rescheduled", "label": "Rescheduled", "color": "#8b5cf6", "active": true, "sort_order": 5},
    {"key": "no_show", "label": "No Show", "color": "#991b1b", "active": true, "sort_order": 6}
  ]'::jsonb,
  true
);

-- Offer Statuses
INSERT INTO application_enums (category, display_name, description, values, is_system) VALUES (
  'offer_statuses',
  'Offer Statuses',
  'Status of job offers extended to candidates',
  '[
    {"key": "draft", "label": "Draft", "color": "#6b7280", "active": true, "sort_order": 1},
    {"key": "pending", "label": "Pending", "color": "#f59e0b", "active": true, "sort_order": 2},
    {"key": "extended", "label": "Extended", "color": "#3b82f6", "active": true, "sort_order": 3},
    {"key": "accepted", "label": "Accepted", "color": "#22c55e", "active": true, "sort_order": 4},
    {"key": "declined", "label": "Declined", "color": "#ef4444", "active": true, "sort_order": 5},
    {"key": "withdrawn", "label": "Withdrawn", "color": "#991b1b", "active": true, "sort_order": 6},
    {"key": "expired", "label": "Expired", "color": "#374151", "active": true, "sort_order": 7}
  ]'::jsonb,
  true
);

-- Submission Statuses
INSERT INTO application_enums (category, display_name, description, values, is_system) VALUES (
  'submission_statuses',
  'Submission Statuses',
  'Status of candidate submissions to jobs',
  '[
    {"key": "submitted", "label": "Submitted", "color": "#3b82f6", "active": true, "sort_order": 1},
    {"key": "under_review", "label": "Under Review", "color": "#f59e0b", "active": true, "sort_order": 2},
    {"key": "shortlisted", "label": "Shortlisted", "color": "#22c55e", "active": true, "sort_order": 3},
    {"key": "interview_scheduled", "label": "Interview Scheduled", "color": "#8b5cf6", "active": true, "sort_order": 4},
    {"key": "rejected", "label": "Rejected", "color": "#ef4444", "active": true, "sort_order": 5},
    {"key": "withdrawn", "label": "Withdrawn", "color": "#991b1b", "active": true, "sort_order": 6},
    {"key": "offer_extended", "label": "Offer Extended", "color": "#10b981", "active": true, "sort_order": 7},
    {"key": "hired", "label": "Hired", "color": "#059669", "active": true, "sort_order": 8}
  ]'::jsonb,
  true
);

-- Priority Levels
INSERT INTO application_enums (category, display_name, description, values, is_system) VALUES (
  'priority_levels',
  'Priority Levels',
  'Priority levels for tasks and jobs',
  '[
    {"key": "critical", "label": "Critical", "color": "#dc2626", "active": true, "sort_order": 1},
    {"key": "high", "label": "High", "color": "#f59e0b", "active": true, "sort_order": 2},
    {"key": "medium", "label": "Medium", "color": "#3b82f6", "active": true, "sort_order": 3},
    {"key": "low", "label": "Low", "color": "#6b7280", "active": true, "sort_order": 4}
  ]'::jsonb,
  true
);
