-- Create email_logs table for email audit trail
-- This table tracks all email activity for compliance and debugging

CREATE TABLE IF NOT EXISTS public.email_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  recipient TEXT NOT NULL,
  subject TEXT NOT NULL,
  template_type TEXT,
  company_id UUID REFERENCES public.companies(id),
  
  -- Email status
  success BOOLEAN NOT NULL DEFAULT false,
  message_id TEXT, -- External provider message ID
  provider_used TEXT NOT NULL DEFAULT 'unknown',
  error_message TEXT,
  
  -- Timestamps
  sent_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  delivered_at TIMESTAMPTZ,
  opened_at TIMESTAMPTZ,
  clicked_at TIMESTAMPTZ,
  
  -- Audit fields
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT valid_email CHECK (recipient ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
  CONSTRAINT valid_provider CHECK (provider_used IN ('resend', 'supabase', 'smtp', 'development', 'unknown'))
);

-- Row-level security
ALTER TABLE public.email_logs ENABLE ROW LEVEL SECURITY;

-- Policy: Users can see email logs for their company
CREATE POLICY "email_logs_company_access" ON public.email_logs FOR ALL USING (
  company_id = (SELECT company_id FROM public.users WHERE id = auth.uid())
  OR
  -- Console admins can see all email logs
  auth.uid() IN (
    SELECT id FROM public.users 
    WHERE role = 'admin' AND company_id IS NULL
  )
  OR
  -- Service role can access all
  auth.role() = 'service_role'
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_email_logs_recipient ON public.email_logs(recipient);
CREATE INDEX IF NOT EXISTS idx_email_logs_company_id ON public.email_logs(company_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_sent_at ON public.email_logs(sent_at);
CREATE INDEX IF NOT EXISTS idx_email_logs_success ON public.email_logs(success);
CREATE INDEX IF NOT EXISTS idx_email_logs_provider ON public.email_logs(provider_used);
CREATE INDEX IF NOT EXISTS idx_email_logs_message_id ON public.email_logs(message_id);

-- Trigger for updated_at
CREATE OR REPLACE FUNCTION update_email_logs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER email_logs_updated_at_trigger
  BEFORE UPDATE ON public.email_logs
  FOR EACH ROW
  EXECUTE FUNCTION update_email_logs_updated_at();

-- Function to get email statistics
CREATE OR REPLACE FUNCTION get_email_statistics(
  company_filter UUID DEFAULT NULL,
  days_back INTEGER DEFAULT 30
)
RETURNS TABLE (
  total_emails BIGINT,
  successful_emails BIGINT,
  failed_emails BIGINT,
  success_rate NUMERIC,
  primary_provider TEXT,
  avg_daily_volume NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_emails,
    COUNT(*) FILTER (WHERE success = true) as successful_emails,
    COUNT(*) FILTER (WHERE success = false) as failed_emails,
    ROUND(
      COUNT(*) FILTER (WHERE success = true) * 100.0 / NULLIF(COUNT(*), 0), 
      2
    ) as success_rate,
    MODE() WITHIN GROUP (ORDER BY provider_used) as primary_provider,
    ROUND(COUNT(*) / NULLIF(days_back, 0)::NUMERIC, 2) as avg_daily_volume
  FROM public.email_logs 
  WHERE 
    sent_at >= NOW() - (days_back || ' days')::INTERVAL
    AND (company_filter IS NULL OR company_id = company_filter);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT ALL ON public.email_logs TO postgres, service_role;
GRANT SELECT, INSERT, UPDATE ON public.email_logs TO authenticated;

-- Add helpful comments
COMMENT ON TABLE public.email_logs IS 'Audit trail for all email communications';
COMMENT ON COLUMN public.email_logs.message_id IS 'External email provider message ID for tracking';
COMMENT ON COLUMN public.email_logs.provider_used IS 'Which email service was used to send the email';
COMMENT ON FUNCTION get_email_statistics(UUID, INTEGER) IS 'Get email delivery statistics for a company or system-wide';
