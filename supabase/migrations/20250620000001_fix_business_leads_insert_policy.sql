-- Fix for business registration 500 error
-- Add missing INSERT policy for business_leads table to allow service role insertions

-- Drop any existing INSERT policy first
DROP POLICY IF EXISTS "Enable insert for service role" ON public.business_leads;

-- Create INSERT policy that allows service role to insert new business leads
CREATE POLICY "Enable insert for service role"
ON public.business_leads FOR INSERT
TO service_role
WITH CHECK (true);

-- Also add a policy for authenticated inserts from the application (if needed)
DROP POLICY IF EXISTS "Enable insert for application" ON public.business_leads;
CREATE POLICY "Enable insert for application"
ON public.business_leads FOR INSERT
TO authenticated, anon
WITH CHECK (true);

-- Update the updated_at timestamp trigger if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for business_leads if it doesn't exist
DROP TRIGGER IF EXISTS update_business_leads_updated_at ON public.business_leads;
CREATE TRIGGER update_business_leads_updated_at
    BEFORE UPDATE ON public.business_leads
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();