-- Create activation_tokens table for business registration activation workflow
-- This table stores secure tokens for new user account activation

CREATE TABLE IF NOT EXISTS public.activation_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
  registration_id UUID REFERENCES public.business_registrations(id) ON DELETE CASCADE,
  token_hash TEXT NOT NULL UNIQUE, -- Store hashed token, never plaintext
  contact_email TEXT NOT NULL,
  contact_name TEXT NOT NULL,
  process_permissions TEXT[] DEFAULT ARRAY['recruitment', 'bench_sales']::TEXT[],
  
  -- Token lifecycle
  used BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMPTZ,
  activated_user_id UUID REFERENCES auth.users(id),
  expires_at TIMESTAMPTZ NOT NULL,
  
  -- Audit fields
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  -- Constraints
  CONSTRAINT valid_expiry CHECK (expires_at > created_at),
  CONSTRAINT valid_email CHECK (contact_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Row-level security
ALTER TABLE public.activation_tokens ENABLE ROW LEVEL SECURITY;

-- Policy: Users can see tokens for their company
CREATE POLICY "activation_tokens_company_access" ON public.activation_tokens FOR ALL USING (
  company_id = (SELECT company_id FROM public.users WHERE id = auth.uid())
  OR
  -- Console admins can see all tokens (company_id is NULL for console admins)
  auth.uid() IN (
    SELECT id FROM public.users 
    WHERE role = 'admin' AND company_id IS NULL
  )
  OR
  -- Service role can access all
  auth.role() = 'service_role'
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_activation_tokens_company_id ON public.activation_tokens(company_id);
CREATE INDEX IF NOT EXISTS idx_activation_tokens_registration_id ON public.activation_tokens(registration_id);
CREATE INDEX IF NOT EXISTS idx_activation_tokens_token_hash ON public.activation_tokens(token_hash);
CREATE INDEX IF NOT EXISTS idx_activation_tokens_expires_at ON public.activation_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_activation_tokens_contact_email ON public.activation_tokens(contact_email);

-- Trigger for updated_at
CREATE OR REPLACE FUNCTION update_activation_tokens_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER activation_tokens_updated_at_trigger
  BEFORE UPDATE ON public.activation_tokens
  FOR EACH ROW
  EXECUTE FUNCTION update_activation_tokens_updated_at();

-- Function to cleanup expired tokens (run via cron job)
CREATE OR REPLACE FUNCTION cleanup_expired_activation_tokens()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM public.activation_tokens 
  WHERE expires_at < NOW() AND used = FALSE;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT ALL ON public.activation_tokens TO postgres, service_role;
GRANT SELECT, INSERT, UPDATE ON public.activation_tokens TO authenticated;

-- Add helpful comments
COMMENT ON TABLE public.activation_tokens IS 'Secure tokens for business registration account activation';
COMMENT ON COLUMN public.activation_tokens.token_hash IS 'SHA-256 hash of activation token - never store plaintext';
COMMENT ON COLUMN public.activation_tokens.process_permissions IS 'Permissions to grant when user activates account';
COMMENT ON FUNCTION cleanup_expired_activation_tokens() IS 'Removes expired unused activation tokens';
