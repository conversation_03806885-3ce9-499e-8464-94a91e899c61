-- This migration fixes a critical data integrity issue in the candidate registration flow.
-- It creates a trigger that automatically creates a new candidate profile whenever a new user
-- is added to Supabase's auth.users table with the 'candidate' user_type.
-- This ensures that every auth user has a corresponding profile, making the process atomic.

-- 1. Create the function to be triggered
CREATE OR REPLACE FUNCTION public.handle_new_candidate_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    user_type TEXT;
BEGIN
    -- Check for the custom user_type in metadata
    user_type := NEW.raw_user_meta_data->>'user_type';

    -- Only proceed if the user_type is 'candidate'
    IF user_type = 'candidate' THEN
        -- Insert a new profile into the public.candidates table
        INSERT INTO public.candidates (auth_user_id, email, first_name, last_name, name, status)
        VALUES (
            NEW.id,
            NEW.email,
            NEW.raw_user_meta_data->>'first_name',
            NEW.raw_user_meta_data->>'last_name',
            (NEW.raw_user_meta_data->>'first_name') || ' ' || (NEW.raw_user_meta_data->>'last_name'),
            'active' -- Or 'pending_confirmation' if you want to wait for email verification
        );
    END IF;

    RETURN NEW;
END;
$$;

-- 2. Create the trigger
-- This trigger fires after a new user is inserted into auth.users
DROP TRIGGER IF EXISTS on_auth_user_created_create_candidate_profile ON auth.users;
CREATE TRIGGER on_auth_user_created_create_candidate_profile
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_candidate_user(); 