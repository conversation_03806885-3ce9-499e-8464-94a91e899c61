-- Phase 8B: Real-time Notifications System
-- Database schema for notifications and user preferences

-- Notification types configuration
CREATE TABLE notification_types (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  type_key TEXT NOT NULL,
  display_name TEXT NOT NULL,
  description TEXT,
  category TEXT NOT NULL, -- 'user_management', 'jobs', 'applications', 'interviews', 'system'
  default_enabled BOOLEAN DEFAULT true,
  supports_email BOOLEAN DEFAULT true,
  supports_in_app BOOLEAN DEFAULT true,
  supports_push BOOLEAN DEFAULT false,
  icon_name TEXT,
  color TEXT DEFAULT '#3b82f6',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(company_id, type_key)
);

-- User notification preferences
CREATE TABLE user_notification_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  notification_type_id UUID REFERENCES notification_types(id) ON DELETE CASCADE,
  email_enabled BOOLEAN DEFAULT true,
  in_app_enabled BOOLEAN DEFAULT true,
  push_enabled BOOLEAN DEFAULT false,
  quiet_hours_start TIME,
  quiet_hours_end TIME,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, notification_type_id)
);

-- Main notifications table
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  notification_type_id UUID REFERENCES notification_types(id),
  
  -- Notification content
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  action_url TEXT,
  action_label TEXT,
  
  -- Status tracking
  is_read BOOLEAN DEFAULT false,
  read_at TIMESTAMPTZ,
  is_dismissed BOOLEAN DEFAULT false,
  dismissed_at TIMESTAMPTZ,
  
  -- Related entities (for context)
  entity_type TEXT, -- 'job', 'candidate', 'application', 'user', etc.
  entity_id UUID,
  
  -- Delivery tracking
  email_sent BOOLEAN DEFAULT false,
  email_sent_at TIMESTAMPTZ,
  push_sent BOOLEAN DEFAULT false,
  push_sent_at TIMESTAMPTZ,
  
  -- Metadata
  priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  expires_at TIMESTAMPTZ,
  metadata JSONB DEFAULT '{}',
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Notification batches for grouped notifications
CREATE TABLE notification_batches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  batch_type TEXT NOT NULL,
  title TEXT NOT NULL,
  summary TEXT,
  notification_count INTEGER DEFAULT 0,
  is_read BOOLEAN DEFAULT false,
  read_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Link notifications to batches
CREATE TABLE notification_batch_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  batch_id UUID REFERENCES notification_batches(id) ON DELETE CASCADE,
  notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(batch_id, notification_id)
);

-- Enable Row Level Security
ALTER TABLE notification_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_batches ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_batch_items ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Notification types are company-scoped" ON notification_types
  FOR ALL USING (
    company_id IN (
      SELECT company_id FROM users WHERE id = auth.uid()
    )
  );

CREATE POLICY "User preferences are user-scoped" ON user_notification_preferences
  FOR ALL USING (
    user_id = auth.uid() OR 
    company_id IN (
      SELECT company_id FROM users 
      WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
  );

CREATE POLICY "Notifications are user-scoped" ON notifications
  FOR ALL USING (
    user_id = auth.uid() OR
    (company_id IN (
      SELECT company_id FROM users 
      WHERE id = auth.uid() AND role IN ('admin')
    ))
  );

CREATE POLICY "Notification batches are user-scoped" ON notification_batches
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Batch items follow batch access" ON notification_batch_items
  FOR ALL USING (
    batch_id IN (
      SELECT id FROM notification_batches WHERE user_id = auth.uid()
    )
  );

-- Indexes for performance
CREATE INDEX idx_notifications_user_unread ON notifications(user_id, is_read, created_at DESC);
CREATE INDEX idx_notifications_company_recent ON notifications(company_id, created_at DESC);
CREATE INDEX idx_notifications_entity ON notifications(entity_type, entity_id);
CREATE INDEX idx_user_preferences_lookup ON user_notification_preferences(user_id, notification_type_id);
CREATE INDEX idx_notification_types_company ON notification_types(company_id, category);

-- Functions for notification management
CREATE OR REPLACE FUNCTION mark_notification_read(notification_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE notifications 
  SET is_read = true, read_at = NOW(), updated_at = NOW()
  WHERE id = notification_id AND user_id = auth.uid();
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION mark_all_notifications_read(user_uuid UUID DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
  target_user_id UUID;
  updated_count INTEGER;
BEGIN
  target_user_id := COALESCE(user_uuid, auth.uid());
  
  UPDATE notifications 
  SET is_read = true, read_at = NOW(), updated_at = NOW()
  WHERE user_id = target_user_id AND is_read = false;
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update updated_at columns
CREATE OR REPLACE FUNCTION update_notification_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_notification_types_updated_at
  BEFORE UPDATE ON notification_types
  FOR EACH ROW EXECUTE FUNCTION update_notification_updated_at();

CREATE TRIGGER trigger_user_notification_preferences_updated_at
  BEFORE UPDATE ON user_notification_preferences
  FOR EACH ROW EXECUTE FUNCTION update_notification_updated_at();

CREATE TRIGGER trigger_notifications_updated_at
  BEFORE UPDATE ON notifications
  FOR EACH ROW EXECUTE FUNCTION update_notification_updated_at();

CREATE TRIGGER trigger_notification_batches_updated_at
  BEFORE UPDATE ON notification_batches
  FOR EACH ROW EXECUTE FUNCTION update_notification_updated_at();
