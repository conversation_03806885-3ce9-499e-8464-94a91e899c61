-- Email Service Configuration
-- Migration: 20250619000003_email_service_config.sql
-- Description: Create email service configuration table for managing SMTP and email providers

-- Create email_service_config table
CREATE TABLE IF NOT EXISTS email_service_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    provider_type TEXT NOT NULL CHECK (provider_type IN ('supabase', 'zeptomail', 'ses', 'smtp', 'resend')),
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_primary BOOLEAN NOT NULL DEFAULT false,
    
    -- Provider credentials (will be encrypted in application layer)
    api_key TEXT,
    api_secret TEXT,
    smtp_host TEXT,
    smtp_port INTEGER,
    smtp_username TEXT,
    smtp_password TEXT,
    
    -- Email settings
    from_email TEXT NOT NULL,
    from_name TEXT NOT NULL,
    reply_to_email TEXT,
    
    -- Capabilities
    handles_invitations BOOLEAN NOT NULL DEFAULT true,
    handles_password_resets BOOLEAN NOT NULL DEFAULT true,
    handles_notifications BOOLEAN NOT NULL DEFAULT true,
    
    -- Usage tracking
    last_used_at TIMESTAMPTZ,
    daily_email_count INTEGER NOT NULL DEFAULT 0,
    monthly_email_limit INTEGER,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_email_service_config_company_id ON email_service_config(company_id);
CREATE INDEX IF NOT EXISTS idx_email_service_config_active ON email_service_config(company_id, is_active);
CREATE INDEX IF NOT EXISTS idx_email_service_config_primary ON email_service_config(company_id, is_primary);

-- Ensure only one primary config per company
CREATE UNIQUE INDEX IF NOT EXISTS idx_email_service_config_primary_unique 
    ON email_service_config(company_id) 
    WHERE is_primary = true;

-- Row Level Security
ALTER TABLE email_service_config ENABLE ROW LEVEL SECURITY;

-- Policies for email_service_config
CREATE POLICY "Email configs are company-scoped" ON email_service_config FOR ALL USING (
    company_id IN (
        SELECT company_id FROM users 
        WHERE id = auth.uid() 
        AND role IN ('admin', 'manager')
    )
);

-- Function to increment daily email count
CREATE OR REPLACE FUNCTION increment_daily_count(config_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_count INTEGER;
BEGIN
    UPDATE email_service_config 
    SET daily_email_count = daily_email_count + 1,
        last_used_at = NOW()
    WHERE id = config_id
    RETURNING daily_email_count INTO new_count;
    
    RETURN COALESCE(new_count, 0);
END;
$$;

-- Insert default ZeptoMail configuration for existing companies
INSERT INTO email_service_config (
    company_id, 
    provider_type, 
    is_active, 
    is_primary,
    from_email, 
    from_name,
    reply_to_email,
    handles_invitations,
    handles_password_resets,
    handles_notifications
) 
SELECT 
    id as company_id,
    'zeptomail' as provider_type,
    true as is_active,
    true as is_primary,
    '<EMAIL>' as from_email,
    'ProcureServe Team' as from_name,
    '<EMAIL>' as reply_to_email,
    true as handles_invitations,
    true as handles_password_resets,
    true as handles_notifications
FROM companies 
WHERE NOT EXISTS (
    SELECT 1 FROM email_service_config esc 
    WHERE esc.company_id = companies.id
);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_email_service_config_updated_at
    BEFORE UPDATE ON email_service_config
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
