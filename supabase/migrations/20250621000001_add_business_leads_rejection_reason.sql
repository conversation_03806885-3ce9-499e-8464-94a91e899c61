-- Add rejection_reason column to business_leads table
-- This allows storing the reason when a business registration is rejected

ALTER TABLE public.business_leads 
ADD COLUMN IF NOT EXISTS rejection_reason TEXT;

-- Add index for better query performance on status
CREATE INDEX IF NOT EXISTS idx_business_leads_status ON public.business_leads(status);

-- Update the RLS policies to ensure proper access control
-- Drop and recreate the policies to ensure they're up to date

-- Policy for service role to insert new leads
DROP POLICY IF EXISTS "Enable insert for service role" ON public.business_leads;
CREATE POLICY "Enable insert for service role"
ON public.business_leads FOR INSERT
TO service_role
WITH CHECK (true);

-- Policy for authenticated inserts from applications
DROP POLICY IF EXISTS "Enable insert for application" ON public.business_leads;
CREATE POLICY "Enable insert for application"
ON public.business_leads FOR INSERT
TO authenticated, anon
WITH CHECK (true);

-- Policy for console admins to read all leads
DROP POLICY IF EXISTS "Enable read access for console admins" ON public.business_leads;
CREATE POLICY "Enable read access for console admins"
ON public.business_leads FOR SELECT
TO authenticated
USING (
  (SELECT role FROM public.users WHERE id = auth.uid()) = 'console_admin'
);

-- Policy for console admins to update leads (approve/reject)
DROP POLICY IF EXISTS "Enable update for console admins" ON public.business_leads;
CREATE POLICY "Enable update for console admins"
ON public.business_leads FOR UPDATE
TO authenticated
USING (
  (SELECT role FROM public.users WHERE id = auth.uid()) = 'console_admin'
)
WITH CHECK (
  (SELECT role FROM public.users WHERE id = auth.uid()) = 'console_admin'
); 