-- Email Verification System
-- Creates the email verification tokens table and verify_email_token function
-- Updated to work correctly with Supabase Cloud

-- Drop existing functions if they exist to avoid conflicts
DROP FUNCTION IF EXISTS public.verify_email_token(text);
DROP FUNCTION IF EXISTS public.create_verification_token(UUID, TEXT, TEXT);

-- Create email verification tokens table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.email_verification_tokens (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  token TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for efficient token lookups
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_token 
ON public.email_verification_tokens(token);

-- Create index for cleanup of expired tokens
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_expires_at 
ON public.email_verification_tokens(expires_at);

-- Create function to verify email tokens
CREATE OR REPLACE FUNCTION public.verify_email_token(p_token TEXT)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  token_record RECORD;
BEGIN
  -- Find the token
  SELECT * INTO token_record
  FROM public.email_verification_tokens
  WHERE token = p_token;

  -- Check if token exists
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'message', 'Invalid verification token.',
      'user_id', null,
      'email', null
    );
  END IF;

  -- Check if token has already been used
  IF token_record.used_at IS NOT NULL THEN
    RETURN json_build_object(
      'success', false,
      'message', 'This verification link has already been used.',
      'user_id', token_record.user_id,
      'email', token_record.email
    );
  END IF;

  -- Check if token has expired
  IF token_record.expires_at < NOW() THEN
    RETURN json_build_object(
      'success', false,
      'message', 'This verification link has expired. Please request a new one.',
      'user_id', token_record.user_id,
      'email', token_record.email
    );
  END IF;

  -- Token is valid, mark it as used
  UPDATE public.email_verification_tokens
  SET used_at = NOW()
  WHERE token = p_token;

  -- Update the user's email_confirmed_at in auth.users
  UPDATE auth.users
  SET email_confirmed_at = NOW(),
      updated_at = NOW()
  WHERE id = token_record.user_id;

  -- Return success
  RETURN json_build_object(
    'success', true,
    'message', 'Email verified successfully! You can now sign in.',
    'user_id', token_record.user_id,
    'email', token_record.email
  );

EXCEPTION
  WHEN OTHERS THEN
    -- Log the error (in production, you might want to use a proper logging mechanism)
    RAISE LOG 'Error in verify_email_token: %', SQLERRM;
    
    RETURN json_build_object(
      'success', false,
      'message', 'Verification failed due to a technical issue. Please try again.',
      'user_id', COALESCE(token_record.user_id, null),
      'email', COALESCE(token_record.email, null)
    );
END;
$$;

-- Create function to cleanup expired tokens (optional, for maintenance)
CREATE OR REPLACE FUNCTION public.cleanup_expired_verification_tokens()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM public.email_verification_tokens
  WHERE expires_at < NOW() - INTERVAL '7 days'; -- Keep expired tokens for 7 days for debugging
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$;

-- Add RLS policies
ALTER TABLE public.email_verification_tokens ENABLE ROW LEVEL SECURITY;

-- Only allow service role to access verification tokens
DROP POLICY IF EXISTS "Service role can manage verification tokens" ON public.email_verification_tokens;
CREATE POLICY "Service role can manage verification tokens" 
ON public.email_verification_tokens
FOR ALL 
TO service_role
USING (true);

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.email_verification_tokens TO service_role;
GRANT EXECUTE ON FUNCTION public.verify_email_token(TEXT) TO service_role;
GRANT EXECUTE ON FUNCTION public.create_verification_token(UUID, TEXT, TEXT) TO service_role;
GRANT EXECUTE ON FUNCTION public.cleanup_expired_verification_tokens() TO service_role;
GRANT EXECUTE ON FUNCTION public.verify_email_token(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_verification_token(UUID, TEXT, TEXT) TO authenticated;

COMMENT ON TABLE public.email_verification_tokens IS 
'Stores email verification tokens for user registration';

COMMENT ON FUNCTION public.verify_email_token(TEXT) IS 
'Verifies an email verification token and confirms the user email';

COMMENT ON FUNCTION public.cleanup_expired_verification_tokens() IS 
'Cleans up expired verification tokens for maintenance';