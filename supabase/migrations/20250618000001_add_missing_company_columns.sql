-- Add missing columns to companies table for business registration
-- These columns are referenced in the business registration code but missing from schema

ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS recruitment_enabled BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS bench_sales_enabled BOOLEAN DEFAULT false;

-- Update existing companies to have both processes enabled (safe default)
UPDATE companies 
SET recruitment_enabled = true, bench_sales_enabled = true 
WHERE recruitment_enabled IS NULL OR bench_sales_enabled IS NULL;

-- Add comment for clarity
COMMENT ON COLUMN companies.recruitment_enabled IS 'Whether this company has recruitment process enabled';
COMMENT ON COLUMN companies.bench_sales_enabled IS 'Whether this company has bench sales process enabled';
