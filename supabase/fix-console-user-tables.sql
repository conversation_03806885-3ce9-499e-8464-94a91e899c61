-- Check Console User Management Tables and Create Missing Ones
-- Run this in Supabase Dashboard > SQL Editor

-- First, let's check what tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE '%console%'
ORDER BY table_name;

-- Check if console_user_invitations table exists with correct structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'console_user_invitations'
ORDER BY ordinal_position;

-- If console_user_invitations doesn't exist, create it
CREATE TABLE IF NOT EXISTS console_user_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('super_admin', 'company_admin', 'company_manager')),
    company_id UUID REFERENCES companies(id),
    permissions JSONB DEFAULT '[]',
    invited_by UUID REFERENCES console_users(id),
    token TEXT NOT NULL UNIQUE,
    expires_at TIMESTAMPTZ NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired', 'cancelled')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for token lookup
CREATE INDEX IF NOT EXISTS idx_console_invitations_token ON console_user_invitations(token);
CREATE INDEX IF NOT EXISTS idx_console_invitations_email ON console_user_invitations(email);
CREATE INDEX IF NOT EXISTS idx_console_invitations_status ON console_user_invitations(status);

-- Enable RLS
ALTER TABLE console_user_invitations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for console_user_invitations
DROP POLICY IF EXISTS "Console users can manage invitations" ON console_user_invitations;
CREATE POLICY "Console users can manage invitations" ON console_user_invitations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM console_users 
            WHERE id = auth.uid() 
            AND role IN ('super_admin', 'company_admin')
        )
    );

-- Check console_users table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'console_users'
ORDER BY ordinal_position;

-- Check console_user_permissions table structure  
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'console_user_permissions'
ORDER BY ordinal_position;
