-- ProcureServe II - Supabase Cloud Setup
-- Essential database schema for development
-- Run this in Supabase Dashboard > SQL Editor

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";

-- Companies table (multi-tenant root)
CREATE TABLE IF NOT EXISTS companies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  domain TEXT UNIQUE,
  settings JSONB DEFAULT '{}',
  recruitment_enabled BOOLEAN DEFAULT true,
  bench_sales_enabled BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users table (with role-based access and process permissions)
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  company_id UUID REFERENCES companies(id),
  role TEXT CHECK (role IN ('admin', 'recruiter', 'manager', 'viewer')) DEFAULT 'viewer',
  profile J<PERSON>NB DEFAULT '{}',
  process_permissions TEXT[] DEFAULT '{}',
  current_process TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add constraints for valid process permissions
ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS valid_process_permissions 
CHECK (process_permissions <@ ARRAY['recruitment', 'bench_sales']);

ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS valid_current_process 
CHECK (current_process IN ('recruitment', 'bench_sales') OR current_process IS NULL);

-- Configurable enums table
CREATE TABLE IF NOT EXISTS configurable_enums (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  category TEXT NOT NULL,
  values JSONB NOT NULL DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(company_id, category)
);

-- Jobs table
CREATE TABLE IF NOT EXISTS jobs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  requirements JSONB DEFAULT '{}',
  status TEXT DEFAULT 'draft',
  vector_embedding vector(1536),
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Candidates table
CREATE TABLE IF NOT EXISTS candidates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT NOT NULL,
  name TEXT NOT NULL,
  phone TEXT,
  resume_text TEXT,
  resume_url TEXT,
  skills JSONB DEFAULT '[]',
  experience_level TEXT,
  work_authorization TEXT,
  vector_embedding vector(1536),
  status TEXT DEFAULT 'active',
  auth_user_id UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Applications table
CREATE TABLE IF NOT EXISTS applications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  job_id UUID REFERENCES jobs(id) ON DELETE CASCADE,
  candidate_id UUID REFERENCES candidates(id) ON DELETE CASCADE,
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  status TEXT DEFAULT 'applied',
  cover_letter TEXT,
  resume_url TEXT,
  applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Consultants table (for bench sales)
CREATE TABLE IF NOT EXISTS consultants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone TEXT,
  skills JSONB DEFAULT '[]',
  experience_years INTEGER DEFAULT 0,
  hourly_rate DECIMAL(10,2),
  availability_status TEXT DEFAULT 'available',
  vector_embedding vector(1536),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Projects table (for bench sales)
CREATE TABLE IF NOT EXISTS projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  client_id UUID,
  title TEXT NOT NULL,
  description TEXT,
  required_skills JSONB DEFAULT '[]',
  hourly_rate DECIMAL(10,2),
  duration_weeks INTEGER,
  status TEXT DEFAULT 'open',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Clients table (for bench sales)
CREATE TABLE IF NOT EXISTS clients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  contact_person TEXT,
  email TEXT,
  phone TEXT,
  relationship_status TEXT DEFAULT 'prospect',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Placements table (for bench sales)
CREATE TABLE IF NOT EXISTS placements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  consultant_id UUID REFERENCES consultants(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  start_date DATE,
  end_date DATE,
  hourly_rate DECIMAL(10,2),
  status TEXT DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Activity logs table
CREATE TABLE IF NOT EXISTS activity_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  action TEXT NOT NULL,
  resource_type TEXT,
  resource_id UUID,
  details JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_company_id ON users(company_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_jobs_company_id ON jobs(company_id);
CREATE INDEX IF NOT EXISTS idx_candidates_email ON candidates(email);
CREATE INDEX IF NOT EXISTS idx_applications_job_id ON applications(job_id);
CREATE INDEX IF NOT EXISTS idx_applications_candidate_id ON applications(candidate_id);
CREATE INDEX IF NOT EXISTS idx_consultants_company_id ON consultants(company_id);
CREATE INDEX IF NOT EXISTS idx_projects_company_id ON projects(company_id);
CREATE INDEX IF NOT EXISTS idx_placements_company_id ON placements(company_id);

-- Insert sample company
INSERT INTO companies (id, name, domain, recruitment_enabled, bench_sales_enabled) 
VALUES (
  '550e8400-e29b-41d4-a716-************',
  'ACME Staffing Solutions',
  'acme-staffing.com',
  true,
  true
) ON CONFLICT (domain) DO NOTHING;

-- Insert sample configurable enums
INSERT INTO configurable_enums (company_id, category, values) VALUES
(
  '550e8400-e29b-41d4-a716-************',
  'work_authorization_types',
  '[
    {"key": "us_citizen", "label": "US Citizen", "color": "#22c55e"},
    {"key": "green_card", "label": "Green Card", "color": "#3b82f6"},
    {"key": "h1b", "label": "H1B Visa", "color": "#f59e0b"},
    {"key": "opt", "label": "OPT", "color": "#8b5cf6"}
  ]'::jsonb
),
(
  '550e8400-e29b-41d4-a716-************',
  'job_statuses',
  '[
    {"key": "draft", "label": "Draft", "color": "#6b7280"},
    {"key": "active", "label": "Active", "color": "#22c55e"},
    {"key": "paused", "label": "Paused", "color": "#f59e0b"},
    {"key": "filled", "label": "Filled", "color": "#3b82f6"},
    {"key": "cancelled", "label": "Cancelled", "color": "#ef4444"}
  ]'::jsonb
),
(
  '550e8400-e29b-41d4-a716-************',
  'candidate_statuses',
  '[
    {"key": "active", "label": "Active", "color": "#22c55e"},
    {"key": "placed", "label": "Placed", "color": "#3b82f6"},
    {"key": "inactive", "label": "Inactive", "color": "#6b7280"}
  ]'::jsonb
)
ON CONFLICT (company_id, category) DO NOTHING;

-- Insert sample users with process permissions
INSERT INTO users (id, email, company_id, role, profile, process_permissions, current_process) VALUES
(
  '11111111-1111-1111-1111-111111111111',
  '<EMAIL>',
  '550e8400-e29b-41d4-a716-************',
  'admin',
  '{"first_name": "John", "last_name": "Admin", "process_permissions": ["recruitment", "bench_sales"]}'::jsonb,
  ARRAY['recruitment', 'bench_sales'],
  NULL
),
(
  '22222222-2222-2222-2222-222222222222',
  '<EMAIL>',
  '550e8400-e29b-41d4-a716-************',
  'manager',
  '{"first_name": "Sarah", "last_name": "Manager", "process_permissions": ["recruitment", "bench_sales"]}'::jsonb,
  ARRAY['recruitment', 'bench_sales'],
  NULL
),
(
  '33333333-3333-3333-3333-333333333333',
  '<EMAIL>',
  '550e8400-e29b-41d4-a716-************',
  'recruiter',
  '{"first_name": "Mike", "last_name": "Recruiter", "process_permissions": ["recruitment"]}'::jsonb,
  ARRAY['recruitment'],
  'recruitment'
),
(
  '44444444-4444-4444-4444-444444444444',
  '<EMAIL>',
  '550e8400-e29b-41d4-a716-************',
  'manager',
  '{"first_name": "Lisa", "last_name": "Sales", "process_permissions": ["bench_sales"]}'::jsonb,
  ARRAY['bench_sales'],
  'bench_sales'
)
ON CONFLICT (email) DO NOTHING;

-- Success message
SELECT 'Database setup completed successfully! You can now start development.' as status;
