-- PSII Application-Wide Enum System Migration
-- Run this in Supabase Dashboard > SQL Editor
-- This creates the new application-wide enum management system

-- Step 1: Create Application-Wide Enums Table
CREATE TABLE IF NOT EXISTS application_enums (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  category TEXT NOT NULL UNIQUE,
  display_name TEXT NOT NULL,
  description TEXT,
  values JSONB NOT NULL DEFAULT '[]',
  
  -- System metadata
  is_system BOOLEAN DEFAULT false,          -- Core system enums (protected)
  is_editable BOOLEAN DEFAULT true,         -- Can be modified by console
  is_hierarchical BOOLEAN DEFAULT false,    -- Supports subcategories (for work_authorization_types)
  
  -- Versioning and audit
  version INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES console_users(id),
  updated_by UUID REFERENCES console_users(id)
);

-- Step 2: Create Enum Operations History
CREATE TABLE IF NOT EXISTS application_enum_operations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  enum_id UUID REFERENCES application_enums(id) ON DELETE CASCADE,
  operation_type TEXT CHECK (operation_type IN ('created', 'updated', 'value_added', 'value_removed', 'value_modified')) NOT NULL,
  user_id UUID REFERENCES console_users(id),
  changes JSONB,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 3: Create Performance Indexes
CREATE INDEX IF NOT EXISTS idx_application_enums_category ON application_enums(category);
CREATE INDEX IF NOT EXISTS idx_application_enums_system ON application_enums(is_system);
CREATE INDEX IF NOT EXISTS idx_application_enum_operations_enum_id ON application_enum_operations(enum_id);
CREATE INDEX IF NOT EXISTS idx_application_enum_operations_timestamp ON application_enum_operations(timestamp DESC);

-- Step 4: Enable Row Level Security
ALTER TABLE application_enums ENABLE ROW LEVEL SECURITY;
ALTER TABLE application_enum_operations ENABLE ROW LEVEL SECURITY;

-- Step 5: Create RLS Policies
DROP POLICY IF EXISTS "Console users can read application enums" ON application_enums;
CREATE POLICY "Console users can read application enums" ON application_enums
  FOR SELECT USING (
    EXISTS (SELECT 1 FROM console_users WHERE id = auth.uid())
  );

DROP POLICY IF EXISTS "Console users can manage application enums" ON application_enums;
CREATE POLICY "Console users can manage application enums" ON application_enums
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM console_users 
      WHERE id = auth.uid() 
      AND role IN ('super_admin', 'company_admin')
    )
  );

DROP POLICY IF EXISTS "Console users can read enum operations" ON application_enum_operations;
CREATE POLICY "Console users can read enum operations" ON application_enum_operations
  FOR SELECT USING (
    EXISTS (SELECT 1 FROM console_users WHERE id = auth.uid())
  );

DROP POLICY IF EXISTS "Console users can create enum operations" ON application_enum_operations;
CREATE POLICY "Console users can create enum operations" ON application_enum_operations
  FOR INSERT WITH CHECK (
    EXISTS (SELECT 1 FROM console_users WHERE id = auth.uid())
  );
