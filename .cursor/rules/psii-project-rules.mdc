---
description: 
globs: 
alwaysApply: false
---
# ProcureServe II (PSII) - Updated Project Settings & Security Framework

The project location is /Users/<USER>/Desktop/PSII 


## Project Overview
**ProcureServe II - Enterprise-Grade Supabase + SvelteKit Architecture**

Next-generation enterprise staffing platform rebuilt with Supabase Cloud-first architecture. **Three-application strategy** with enterprise-level security, compliance, and user experience standards.

## Critical Security Notice
⚠️ **ENTERPRISE SECURITY MANDATORY**: This platform handles sensitive employment data. Any security breach, data leak, or compliance failure could result in **legal action against ProcureServe**. All code must implement **enterprise-grade security** from day one.

## Application Architecture (3 Apps)

### 1. **Customer App** (app.procureserve.com)
- **Purpose**: External business customers (staffing agencies, direct employers)
- **Port**: 3004 (development)
- **Users**: Business customers using recruitment/bench sales processes
- **Security Level**: High (customer data, financial information)

### 2. **Console App** (console.procureserve.com)  
- **Purpose**: Internal ProcureServe team operations and customer support
- **Port**: 3008 (development)
- **Users**: Internal ProcureServe staff only
- **Security Level**: Critical (administrative access, customer management)

### 3. **Talent App** (talent.procureserve.com)
- **Purpose**: Job seekers and consultants (candidate portal)
- **Port**: 3006 (development)  
- **Users**: Job seekers, consultants, candidates
- **Security Level**: High (personal data, employment history)

## Technology Stack

### Backend (Supabase Cloud Only)
- **Database**: PostgreSQL 17.4 with pgvector (NO LOCAL SUPABASE)
- **Authentication**: Supabase Auth with enterprise JWT tokens
- **Storage**: Supabase Storage with encryption
- **Real-time**: Supabase Realtime for live updates
- **Edge Functions**: Deno-based serverless functions
- **Region**: Asia Pacific (Mumbai) - ap-south-1
- **Project ID**: hfzhvrknjgwtrkgyinjf

### Frontend (SvelteKit + Enterprise Security)
- **Framework**: SvelteKit with strict TypeScript
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: Secure Svelte stores with encryption
- **Routing**: SvelteKit's file-based routing with auth guards
- **Build Tool**: Vite with security plugins

### Email Service (Resend API Only)
- **Provider**: Resend API (NOT Supabase email)
- **Reason**: Superior deliverability, compliance features, audit trails
- **Templates**: Professional HTML templates with security headers
- **Monitoring**: Delivery tracking and bounce management

### Hosting & Deployment
- **Frontend**: Vercel Pro with enterprise security headers
- **Backend**: Supabase Cloud with SOC 2 compliance
- **SSL**: Automatic HTTPS with HSTS enforcement
- **CDN**: Global edge distribution with DDoS protection

## Enterprise Security Framework

### 1. **Data Protection (GDPR/CCPA Compliance)**
```typescript
// All PII must be encrypted at rest and in transit
interface SecureDataField {
  encrypted: boolean;
  classification: 'public' | 'internal' | 'confidential' | 'restricted';
  retention_policy: string;
  audit_trail: boolean;
}
```

### 2. **Multi-Tenant Security (Zero-Trust Model)**
```sql
-- Every table MUST have company-scoped RLS
CREATE POLICY "strict_company_isolation" ON sensitive_table FOR ALL USING (
  company_id = (SELECT company_id FROM auth.users WHERE id = auth.uid())
  AND is_active = true
  AND deleted_at IS NULL
);
```

### 3. **Authentication & Authorization**
- **Multi-Factor Authentication**: Required for all admin users
- **Session Management**: Secure JWT with rotation and expiry
- **Role-Based Access Control**: Principle of least privilege
- **API Rate Limiting**: Prevent brute force attacks
- **Audit Logging**: Every action tracked with immutable logs

### 4. **Input Validation & Sanitization**
```typescript
// ALL user inputs must be validated and sanitized
import { z } from 'zod';
import DOMPurify from 'isomorphic-dompurify';

const secureSchema = z.object({
  email: z.string().email().max(255),
  name: z.string().min(1).max(100).regex(/^[a-zA-Z\s-']+$/),
  // Every field must have strict validation
});
```

### 5. **Data Encryption Standards**
- **At Rest**: AES-256 encryption for all sensitive data
- **In Transit**: TLS 1.3 minimum, HSTS headers
- **API Keys**: Stored in secure environment variables only
- **Database**: Row-level encryption for PII fields

## Development Principles (Updated)

### 1. **Security-First Development**
```typescript
// Security checklist for every component
const SecurityChecklist = {
  input_validation: true,      // All inputs validated
  output_encoding: true,       // All outputs encoded
  authentication: true,       // Auth required
  authorization: true,        // Permissions checked
  audit_logging: true,        // Actions logged
  error_handling: true,       // No sensitive data in errors
  rate_limiting: true        // API endpoints protected
};
```

### 2. **Enterprise Code Quality**
- **Component Size**: Maximum 50 lines for maintainability
- **Type Safety**: Strict TypeScript with no `any` types
- **Error Handling**: Comprehensive try-catch with secure error messages
- **Testing**: Unit tests, integration tests, security tests
- **Code Review**: Mandatory security review for all changes

### 3. **Documentation Standards**
- **Security Documentation**: Every feature must document security implications
- **API Documentation**: Complete with security requirements
- **Deployment Docs**: Security configuration steps
- **Incident Response**: Security breach response procedures

## Best Practices & Recommendations Framework

### When to Ask for Guidance
Claude should **always recommend best practices** and **provide options** when:

1. **Security Decisions**: Multiple security approaches available
2. **Architecture Choices**: Trade-offs between performance and security
3. **Data Handling**: PII processing or storage decisions
4. **Third-Party Integrations**: Security implications of external services
5. **Compliance Requirements**: GDPR, CCPA, SOC 2 considerations

### Option Presentation Format
```typescript
interface SecurityRecommendation {
  option: string;
  security_level: 'basic' | 'enhanced' | 'enterprise';
  compliance: string[];
  pros: string[];
  cons: string[];
  implementation_effort: 'low' | 'medium' | 'high';
  recommendation_score: number; // 1-10
}
```

## Project Structure (Updated)

```
PSII/
├── apps/                          # Three SvelteKit Applications
│   ├── customer-app/              # app.procureserve.com (External customers)
│   ├── console-app/               # console.procureserve.com (Internal team)
│   └── talent-app/                # talent.procureserve.com (Job seekers)
├── packages/                      # Shared Libraries
│   ├── shared-types/              # TypeScript interfaces with security
│   ├── shared-utils/              # Security utilities
│   ├── database-types/            # Supabase generated types
│   ├── email-service/             # Resend-based email service
│   ├── security-service/          # Enterprise security utilities
│   └── audit-service/             # Compliance and audit logging
├── docs/                          # Documentation (MANDATORY)
│   ├── security/                  # Security documentation
│   ├── compliance/                # GDPR, CCPA, SOC 2 docs
│   ├── api/                      # API documentation
│   └── deployment/               # Deployment guides
├── scripts/                       # Utility Scripts (MANDATORY)
│   ├── security/                  # Security validation scripts
│   ├── testing/                   # Test automation
│   └── deployment/               # Deployment automation
├── tests/                         # Comprehensive Testing
│   ├── unit/                     # Unit tests
│   ├── integration/              # Integration tests
│   ├── security/                 # Security tests
│   └── e2e/                      # End-to-end tests
└── supabase/                     # Supabase Cloud Configuration
    ├── migrations/               # Database schema (versioned)
    └── functions/               # Edge functions
```

## Business Process Security

### 1. Customer App Security Flow
```mermaid
graph LR
    A[Customer Login] --> B[MFA Verification]
    B --> C[Company Context]
    C --> D[Role Validation]
    D --> E[Process Permissions]
    E --> F[Audit Log Entry]
    F --> G[Secure Dashboard]
```

### 2. Console App Security (Critical)
- **Admin Access**: Multi-factor authentication mandatory
- **Privileged Operations**: Require additional verification
- **Data Access**: All customer data access logged and audited
- **Configuration Changes**: Require approval workflow

### 3. Talent App Security
- **Personal Data**: GDPR-compliant data handling
- **Resume Upload**: Malware scanning and secure storage
- **Profile Access**: Granular privacy controls
- **Communication**: Secure messaging between candidates and recruiters

## Compliance Requirements

### 1. **GDPR Compliance**
- **Data Minimization**: Collect only necessary data
- **Right to Erasure**: Implement data deletion workflows
- **Data Portability**: Export user data in standard formats
- **Consent Management**: Granular privacy controls
- **Breach Notification**: 72-hour reporting procedures

### 2. **SOC 2 Type II**
- **Security Controls**: Documented and tested
- **Access Controls**: Principle of least privilege
- **Change Management**: Controlled deployment processes
- **Monitoring**: Continuous security monitoring
- **Incident Response**: Documented procedures

### 3. **Data Retention Policies**
```sql
-- Automatic data purging for compliance
CREATE TABLE data_retention_policies (
  table_name TEXT PRIMARY KEY,
  retention_period INTERVAL NOT NULL,
  purge_method TEXT NOT NULL,
  compliance_requirement TEXT NOT NULL
);
```

## Security Monitoring & Incident Response

### 1. **Real-Time Monitoring**
- **Failed Login Attempts**: Automatic account lockout
- **Unusual Access Patterns**: Alert security team
- **Data Export Activities**: Require additional authorization
- **System Changes**: Real-time notifications

### 2. **Incident Response Plan**
```typescript
interface SecurityIncident {
  severity: 'low' | 'medium' | 'high' | 'critical';
  type: 'data_breach' | 'unauthorized_access' | 'system_compromise';
  response_time: number; // minutes
  notification_required: boolean;
  legal_implications: boolean;
}
```

## Cost Structure (Security-Enhanced)

### Monthly Costs (Security-Focused)
- **Supabase Pro**: $25/month (SOC 2 compliant)
- **Vercel Pro**: $20/month (Enterprise security features)
- **Resend Pro**: $20/month (Enhanced deliverability & compliance)
- **Security Monitoring**: $10/month (Log analysis & alerting)
- **Total**: ~$75/month (Security-enhanced infrastructure)

## Development Workflow (Security-Integrated)

### Pre-Development Security Checklist
1. **Threat Modeling**: Identify security risks
2. **Data Classification**: Categorize data sensitivity
3. **Security Requirements**: Define security controls needed
4. **Compliance Check**: Verify regulatory requirements

### Code Development Security Gates
1. **Static Code Analysis**: Security vulnerability scanning
2. **Dependency Scanning**: Check for vulnerable packages
3. **Security Code Review**: Mandatory for all changes
4. **Penetration Testing**: Regular security testing

### Deployment Security Validation
1. **Security Configuration**: Verify all security settings
2. **Access Control Testing**: Validate permissions
3. **Encryption Verification**: Confirm data protection
4. **Monitoring Setup**: Ensure security alerts active

---

**Security Statement**: This project handles sensitive employment and personal data. Every development decision must prioritize security, compliance, and data protection. When in doubt, choose the most secure option and document the security rationale.

**Last Updated**: June 20, 2025
**Security Review**: Required before any production deployment
**Compliance Status**: GDPR/CCPA ready, SOC 2 preparation in progress
