#!/usr/bin/env node

// Check console_users table and create admin user if needed
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhmemh2cmtuamd3dHJrZ3lpbmpmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA2ODI4MiwiZXhwIjoyMDY1NjQ0MjgyfQ._scXNK1IgQuVok-iIBVxQYmcWntrI2VLInx1_Cz8knI';

console.log('👤 Console User Setup & Verification');

async function setupConsoleUser() {
  const supabase = createClient(supabaseUrl, serviceRoleKey, {
    auth: { autoRefreshToken: false, persistSession: false }
  });

  try {
    console.log('\n1️⃣ Checking if console_users table exists...');
    
    // Check if console_users table exists
    const { data: tables, error: tableError } = await supabase
      .rpc('sql', {
        query: `
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'console_users'
        `
      });

    if (tableError) {
      console.log('⚠️ Cannot check table existence via RPC, trying direct query...');
      
      // Try direct query to console_users
      const { data: consoleUsers, error: directError } = await supabase
        .from('console_users')
        .select('count');

      if (directError) {
        console.error('❌ console_users table does not exist:', directError.message);
        console.log('\n🔧 Console_users table is missing. Creating it...');
        
        // Create the console_users table
        const createTableQuery = `
          CREATE TABLE IF NOT EXISTS console_users (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            email TEXT UNIQUE NOT NULL,
            role TEXT NOT NULL DEFAULT 'viewer',
            is_active BOOLEAN NOT NULL DEFAULT true,
            last_login_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
          
          -- Enable RLS
          ALTER TABLE console_users ENABLE ROW LEVEL SECURITY;
          
          -- Create basic policy for console users
          CREATE POLICY "console_users_policy" ON console_users FOR ALL USING (true);
          
          -- Add trigger for updated_at
          CREATE OR REPLACE TRIGGER update_console_users_updated_at 
            BEFORE UPDATE ON console_users
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        `;
        
        const { error: createError } = await supabase.rpc('sql', { query: createTableQuery });
        
        if (createError) {
          console.error('❌ Failed to create console_users table:', createError);
          return;
        } else {
          console.log('✅ console_users table created successfully');
        }
      } else {
        console.log('✅ console_users table exists');
      }
    } else {
      console.log('✅ console_users table exists');
    }

    console.log('\n2️⃣ Checking existing console users...');
    
    const { data: existingUsers, error: usersError } = await supabase
      .from('console_users')
      .select('*');

    if (usersError) {
      console.error('❌ Failed to fetch console users:', usersError);
      return;
    }

    console.log(`📋 Found ${existingUsers?.length || 0} console users`);
    existingUsers?.forEach(user => {
      console.log(`   👤 ${user.email} (${user.role}) - active: ${user.is_active}`);
    });

    // Create admin user if none exist
    if (!existingUsers || existingUsers.length === 0) {
      console.log('\n3️⃣ Creating default console admin user...');
      
      // Create a console admin user
      const adminEmail = '<EMAIL>';
      const { data: newAdmin, error: adminError } = await supabase
        .from('console_users')
        .insert({
          email: adminEmail,
          role: 'admin',
          is_active: true
        })
        .select('*')
        .single();

      if (adminError) {
        console.error('❌ Failed to create admin user:', adminError);
      } else {
        console.log('✅ Console admin user created:', newAdmin);
        console.log('\n🔑 IMPORTANT: You need to:');
        console.log(`   1. Sign up with email: ${adminEmail}`);
        console.log('   2. Then the login will work with console access');
      }
    }

    console.log('\n4️⃣ Testing auth user creation and linking...');
    
    // Test creating an auth user that links to console_users
    const testEmail = '<EMAIL>';
    
    // First check if auth user already exists
    const { data: existingAuth } = await supabase.auth.admin.getUserByEmail(testEmail);
    
    if (existingAuth?.user) {
      console.log(`ℹ️ Auth user already exists: ${testEmail}`);
    } else {
      console.log(`🔧 Creating auth user: ${testEmail}`);
      
      const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
        email: testEmail,
        password: 'TempPassword123!',
        email_confirm: true
      });

      if (authError) {
        console.error('❌ Failed to create auth user:', authError);
      } else {
        console.log('✅ Auth user created:', authUser.user?.id);
        
        // Create corresponding console user record
        const { data: consoleUserRecord, error: consoleError } = await supabase
          .from('console_users')
          .insert({
            id: authUser.user?.id,  // Link to auth user
            email: testEmail,
            role: 'admin',
            is_active: true
          })
          .select('*')
          .single();

        if (consoleError) {
          console.error('❌ Failed to create console user record:', consoleError);
        } else {
          console.log('✅ Console user record created and linked');
          console.log('\n🎯 TEST CREDENTIALS:');
          console.log(`   Email: ${testEmail}`);
          console.log('   Password: TempPassword123!');
          console.log('   Role: admin');
          console.log('\n   Use these credentials to test the approval process!');
        }
      }
    }

    console.log('\n5️⃣ Verification complete!');
    console.log('\n📋 Next Steps:');
    console.log('   1. Go to console app: http://localhost:3008');
    console.log('   2. Login with the test credentials above');
    console.log('   3. Try the approval process');
    console.log('   4. Check if the "Failed to create company account" error is resolved');

  } catch (error) {
    console.error('❌ Console user setup failed:', error);
  }
}

setupConsoleUser().catch(console.error);
