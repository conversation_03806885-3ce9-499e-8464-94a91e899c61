#!/usr/bin/env node

import dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

dotenv.config({ path: join(__dirname, '.env.local') })

// Your specific invitation token from the email
const invitationToken = 'bb95046782f1a1f08d3b6c4598892604ad9fe451766d93bc7161ce582b8a919d'

console.log('🔗 Console Invitation Activation Links\n')

console.log('Your invitation token:', invitationToken)
console.log('Email:', '<EMAIL>')
console.log('Expires:', '2025-06-28')

console.log('\n🚀 Direct Activation Links:')
console.log('✅ Console App (Development):', `http://localhost:3008/activate-console?token=${invitationToken}`)
console.log('✅ Console App (Alternative):', `http://127.0.0.1:3008/activate-console?token=${invitationToken}`)

console.log('\n📋 Steps to Activate:')
console.log('1. Make sure your console app is running: cd apps/console-app && npm run dev')
console.log('2. Click on one of the activation links above')
console.log('3. Complete the console user setup process')

console.log('\n💡 Note: The email had "undefined" in the URL due to missing PUBLIC_CONSOLE_APP_URL')
console.log('   This has been fixed in the environment configuration.')
console.log('   Future invitations will have the correct URL automatically.')
