#!/usr/bin/env node

// Test the FIXED approval email workflow
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('🧪 Testing FIXED Approval Email Workflow');

async function testFixedApprovalFlow() {
  const supabase = createClient(supabaseUrl, serviceRoleKey, {
    auth: { autoRefreshToken: false, persistSession: false }
  });

  try {
    console.log('\n1️⃣ Checking tables exist...');
    
    // Check activation_tokens table
    const { data: tokenTest, error: tokenError } = await supabase
      .from('activation_tokens')
      .select('*')
      .limit(1);

    if (tokenError) {
      console.log('❌ activation_tokens table issue:', tokenError.message);
    } else {
      console.log('✅ activation_tokens table accessible');
    }

    // Check email_logs table
    const { data: logTest, error: logError } = await supabase
      .from('email_logs')
      .select('*')
      .limit(1);

    if (logError) {
      console.log('❌ email_logs table issue:', logError.message);
    } else {
      console.log('✅ email_logs table accessible');
    }

    console.log('\n2️⃣ Testing direct workflow functions...');
    
    // Test the workflow functions directly
    try {
      const { handleRegistrationApproved } = await import('../apps/console-app/src/lib/business-email/workflow.js');
      
      // Get the test registration
      const { data: registration } = await supabase
        .from('business_registrations')
        .select('*')
        .eq('contact_person_email', '<EMAIL>')
        .single();

      if (!registration) {
        console.log('❌ Test registration not found');
        return;
      }

      console.log('✅ Found test registration:', registration.id);
      console.log('   Company:', registration.company_name);
      console.log('   Status:', registration.status);
      console.log('   Company ID:', registration.activated_company_id);

      // Test the approval workflow
      console.log('\n3️⃣ Testing approval workflow...');
      
      const result = await handleRegistrationApproved(
        registration,
        registration.activated_company_id,
        {} // No options, let it generate new token
      );

      if (result.success) {
        console.log('✅ Approval workflow completed successfully!');
        console.log('   Check your email for the activation link');
        console.log('   Check Resend dashboard for delivery confirmation');
        
        // Check if activation token was created
        const { data: tokens, error: tokenCheckError } = await supabase
          .from('activation_tokens')
          .select('*')
          .eq('contact_email', registration.contact_person_email)
          .order('created_at', { ascending: false })
          .limit(1);

        if (tokenCheckError) {
          console.log('❌ Error checking activation tokens:', tokenCheckError.message);
        } else if (tokens && tokens.length > 0) {
          console.log('✅ Activation token created successfully');
          console.log('   Token ID:', tokens[0].id);
          console.log('   Expires at:', tokens[0].expires_at);
        } else {
          console.log('⚠️ No activation token found');
        }

        // Check if email was logged
        const { data: emailLogs, error: logCheckError } = await supabase
          .from('email_logs')
          .select('*')
          .eq('recipient', registration.contact_person_email)
          .order('sent_at', { ascending: false })
          .limit(1);

        if (logCheckError) {
          console.log('❌ Error checking email logs:', logCheckError.message);
        } else if (emailLogs && emailLogs.length > 0) {
          console.log('✅ Email activity logged successfully');
          console.log('   Success:', emailLogs[0].success);
          console.log('   Provider:', emailLogs[0].provider_used);
          console.log('   Message ID:', emailLogs[0].message_id);
        } else {
          console.log('⚠️ No email log found');
        }
        
      } else {
        console.log('❌ Approval workflow failed:', result.error);
      }

    } catch (importError) {
      console.error('❌ Import error (this is expected in Node.js context):', importError.message);
      console.log('   The workflow file uses SvelteKit imports, which is normal');
      
      // Test the email service directly using Resend
      console.log('\n3️⃣ Testing direct Resend email (fallback test)...');
      
      const { Resend } = await import('resend');
      const resend = new Resend(process.env.RESEND_API_KEY);

      const testEmail = {
        from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_FROM_ADDRESS}>`,
        to: ['<EMAIL>'],
        subject: '🧪 FIXED Approval Email Test - PSII',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #16a34a;">🎉 Registration Approved!</h1>
            <p>Dear Vasanthan M,</p>
            <p>Your business registration for <strong>Boyar Consulting Company</strong> has been approved!</p>
            <div style="text-align: center; margin: 25px 0;">
              <a href="https://app.procureserve.com/activate?token=test-fixed-token-${Date.now()}" 
                 style="background: #16a34a; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block;">
                Activate Your Account
              </a>
            </div>
            <p style="color: #64748b;">This is a test of the FIXED approval email workflow.</p>
            <p style="color: #64748b;">Time: ${new Date().toISOString()}</p>
          </div>
        `
      };

      const emailResult = await resend.emails.send(testEmail);
      
      if (emailResult.data) {
        console.log('✅ Direct email test successful!');
        console.log('   Message ID:', emailResult.data.id);
      } else {
        console.log('❌ Direct email test failed:', emailResult.error);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testFixedApprovalFlow().catch(console.error);
