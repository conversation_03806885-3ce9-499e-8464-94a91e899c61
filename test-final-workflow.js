// Final Email Workflow Test - Phase 3 Complete
// Tests all email components and domain detection

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import { Resend } from 'resend';

config({ path: '.env.local' });

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const resend = new Resend(process.env.RESEND_API_KEY);

console.log('🧪 Final Phase 3 Email Workflow Test');
console.log('===================================\n');

async function testNewCompanyFlow() {
  console.log('1️⃣ Testing NEW_COMPANY Email Flow');
  console.log('----------------------------------');
  
  try {
    const adminClient = createClient(supabaseUrl, serviceKey, {
      auth: { autoRefreshToken: false, persistSession: false }
    });

    // Create test registration
    const { data: registration, error } = await adminClient
      .from('business_registrations')
      .insert({
        contact_person_name: 'Test User Final',
        contact_person_email: '<EMAIL>',
        company_name: 'Brand New Company LLC',
        company_domain: 'brandnewcompany.com',
        status: 'pending'
      })
      .select()
      .single();

    if (error) {
      console.error('❌ Registration failed:', error.message);
      return false;
    }

    console.log('✅ Registration created:', registration.company_name);

    // Check domain (should be NEW_COMPANY)
    const { data: existingCompanies } = await adminClient
      .from('companies')
      .select('id')
      .eq('domain', 'brandnewcompany.com');

    const isNewCompany = !existingCompanies || existingCompanies.length === 0;
    console.log('🔍 Domain detection:', isNewCompany ? 'NEW_COMPANY ✅' : 'EXISTING_CUSTOMER ❌');

    if (isNewCompany) {
      // Send console admin notification
      const adminResult = await resend.emails.send({
        from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_FROM_ADDRESS}>`,
        to: ['<EMAIL>'],
        subject: `NEW_COMPANY Test: ${registration.company_name}`,
        html: `
          <div style="font-family: Arial, sans-serif; padding: 20px;">
            <h1 style="color: #2563eb;">ProcureServe Console</h1>
            <div style="background: #fef3c7; padding: 15px; border-radius: 8px; margin: 15px 0;">
              <h2 style="color: #92400e;">⚡ NEW_COMPANY Detection Test</h2>
              <p>Company: ${registration.company_name}</p>
              <p>Domain: ${registration.company_domain}</p>
              <p>Contact: ${registration.contact_person_name}</p>
            </div>
            <p>✅ Email routing working correctly for NEW_COMPANY!</p>
          </div>
        `,
        text: `NEW_COMPANY Test - ${registration.company_name} (${registration.company_domain})`
      });

      if (adminResult.error) {
        console.error('❌ Console admin email failed:', adminResult.error.message);
        return false;
      } else {
        console.log('✅ Console admin email sent! ID:', adminResult.data.id);
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

async function testExistingCustomerFlow() {
  console.log('\n2️⃣ Testing EXISTING_CUSTOMER Email Flow');
  console.log('----------------------------------------');
  
  try {
    const adminClient = createClient(supabaseUrl, serviceKey, {
      auth: { autoRefreshToken: false, persistSession: false }
    });

    // Create test registration for existing domain
    const { data: registration, error } = await adminClient
      .from('business_registrations')
      .insert({
        contact_person_name: 'Existing Customer Test',
        contact_person_email: '<EMAIL>',
        company_name: 'Test Company Inc',
        company_domain: 'testcompany.com',
        status: 'pending'
      })
      .select()
      .single();

    if (error) {
      console.error('❌ Registration failed:', error.message);
      return false;
    }

    console.log('✅ Registration created:', registration.company_name);

    // Check domain (should be EXISTING_CUSTOMER)
    const { data: existingCompanies } = await adminClient
      .from('companies')
      .select('id, name')
      .eq('domain', 'testcompany.com');

    const isExistingCustomer = existingCompanies && existingCompanies.length > 0;
    console.log('🔍 Domain detection:', isExistingCustomer ? 'EXISTING_CUSTOMER ✅' : 'NEW_COMPANY ❌');

    if (isExistingCustomer) {
      // For testing, send to admin email instead of actual company admin
      const companyResult = await resend.emails.send({
        from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_FROM_ADDRESS}>`,
        to: ['<EMAIL>'],
        subject: `EXISTING_CUSTOMER Test: ${registration.contact_person_name}`,
        html: `
          <div style="font-family: Arial, sans-serif; padding: 20px;">
            <h1 style="color: #2563eb;">ProcureServe</h1>
            <div style="background: #dbeafe; padding: 15px; border-radius: 8px; margin: 15px 0;">
              <h2 style="color: #1e40af;">👥 EXISTING_CUSTOMER Detection Test</h2>
              <p>New Team Member: ${registration.contact_person_name}</p>
              <p>Company: ${registration.company_name}</p>
              <p>Domain: ${registration.company_domain}</p>
            </div>
            <p>✅ Email routing working correctly for EXISTING_CUSTOMER!</p>
          </div>
        `,
        text: `EXISTING_CUSTOMER Test - ${registration.contact_person_name} at ${registration.company_name}`
      });

      if (companyResult.error) {
        console.error('❌ Company admin email failed:', companyResult.error.message);
        return false;
      } else {
        console.log('✅ Company admin email sent! ID:', companyResult.data.id);
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

async function testApprovalRejectionEmails() {
  console.log('\n3️⃣ Testing Approval/Rejection Emails');
  console.log('------------------------------------');
  
  try {
    // Test approval email
    const approvalResult = await resend.emails.send({
      from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_FROM_ADDRESS}>`,
      to: ['<EMAIL>'],
      subject: 'Welcome to ProcureServe - Account Activation Required',
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px;">
          <h1 style="color: #2563eb;">ProcureServe</h1>
          <div style="background: #dcfce7; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h2 style="color: #15803d;">🎉 Registration Approved!</h2>
            <p>Congratulations! Your registration has been approved.</p>
          </div>
          <div style="text-align: center; margin: 25px 0;">
            <a href="http://localhost:3004/auth/activate?token=test-token" 
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
              Activate Account
            </a>
          </div>
          <p>✅ Approval email test completed!</p>
        </div>
      `,
      text: 'Registration Approved - Activate your account at: http://localhost:3004/auth/activate?token=test-token'
    });

    if (approvalResult.error) {
      console.error('❌ Approval email failed:', approvalResult.error.message);
      return false;
    } else {
      console.log('✅ Approval email sent! ID:', approvalResult.data.id);
    }

    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test rejection email
    const rejectionResult = await resend.emails.send({
      from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_FROM_ADDRESS}>`,
      to: ['<EMAIL>'],
      subject: 'ProcureServe Registration Update Required',
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px;">
          <h1 style="color: #2563eb;">ProcureServe</h1>
          <div style="background: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h2 style="color: #dc2626;">Registration Requires Updates</h2>
            <p>We need additional information before we can proceed with your registration.</p>
          </div>
          <div style="background: #f8fafc; padding: 15px; border-radius: 8px;">
            <h3>Review Notes:</h3>
            <p>Please contact our support team for next steps.</p>
          </div>
          <p>✅ Rejection email test completed!</p>
        </div>
      `,
      text: 'Registration Update Required - <NAME_EMAIL> for next steps.'
    });

    if (rejectionResult.error) {
      console.error('❌ Rejection email failed:', rejectionResult.error.message);
      return false;
    } else {
      console.log('✅ Rejection email sent! ID:', rejectionResult.data.id);
      return true;
    }

  } catch (error) {
    console.error('❌ Approval/Rejection test failed:', error.message);
    return false;
  }
}

async function runCompleteTest() {
  const results = [];
  
  results.push(await testNewCompanyFlow());
  results.push(await testExistingCustomerFlow());
  results.push(await testApprovalRejectionEmails());
  
  const successCount = results.filter(r => r).length;
  const totalTests = results.length;
  
  console.log('\n🎯 Phase 3 Email System Test Results');
  console.log('===================================');
  console.log(`✅ ${successCount}/${totalTests} test categories passed`);
  console.log('📧 Check your email inbox for all test messages');
  
  if (successCount === totalTests) {
    console.log('🚀 PHASE 3 COMPLETE - Email System fully operational!');
    console.log('🔥 Domain detection working correctly');
    console.log('📬 All email templates functioning');
    console.log('⚡ Ready for production deployment');
  } else {
    console.log('⚠️ Some tests failed - review error messages above');
  }
}

runCompleteTest().catch(console.error);
