// Test script to verify the login fix
async function testLoginFix() {
  console.log('🔧 Testing login fix...')
  
  try {
    // Create form data
    const formData = new FormData()
    formData.append('email', '<EMAIL>')
    formData.append('password', 'admin123')
    
    console.log('1. Submitting login form...')
    const response = await fetch('http://localhost:3008/login?/login', {
      method: 'POST',
      body: formData,
      redirect: 'manual' // Don't follow redirects automatically
    })
    
    console.log(`Response status: ${response.status}`)
    console.log(`Response headers:`)
    for (const [key, value] of response.headers.entries()) {
      console.log(`  ${key}: ${value}`)
    }
    
    if (response.status === 303) {
      const location = response.headers.get('location')
      console.log(`✅ SUCCESS: Proper redirect to ${location}`)
      
      if (location === '/dashboard') {
        console.log('✅ Login working correctly - redirects to dashboard')
      } else {
        console.log(`⚠️  Unexpected redirect location: ${location}`)
      }
    } else if (response.status >= 400) {
      const text = await response.text()
      console.log(`❌ Login failed with status ${response.status}:`)
      console.log(text.slice(0, 500) + '...')
    } else {
      console.log(`⚠️  Unexpected response status: ${response.status}`)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

console.log('🧪 Testing login behavior after fix...')
console.log('Expected: 303 redirect to /dashboard')
console.log('---')

testLoginFix()