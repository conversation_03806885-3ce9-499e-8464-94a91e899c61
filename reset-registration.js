#!/usr/bin/env node

// Reset the registration status to test approval flow again
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabase = createClient(process.env.PUBLIC_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY, {
  auth: { autoRefreshToken: false, persistSession: false }
});

async function resetRegistration() {
  // Reset the registration to pending so we can test approval again
  const { error } = await supabase
    .from('business_registrations')
    .update({ 
      status: 'pending',
      reviewed_by: null,
      reviewed_at: null 
    })
    .eq('contact_person_email', '<EMAIL>');

  if (error) {
    console.log('❌ Failed to reset:', error.message);
  } else {
    console.log('✅ Registration reset to pending status');
    console.log('   You can now test the approval flow in the console app');
    console.log('   Visit: http://localhost:3008/companies/pending');
  }
}

resetRegistration().catch(console.error);
