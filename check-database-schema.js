#!/usr/bin/env node

// Check which migrations have been applied to the database
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhmemh2cmtuamd3dHJrZ3lpbmpmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA2ODI4MiwiZXhwIjoyMDY1NjQ0MjgyfQ._scXNK1IgQuVok-iIBVxQYmcWntrI2VLInx1_Cz8knI';

console.log('📊 Checking Database Schema and Applied Migrations');

async function checkDatabaseSchema() {
  const supabase = createClient(supabaseUrl, serviceRoleKey, {
    auth: { autoRefreshToken: false, persistSession: false }
  });

  try {
    console.log('\n1️⃣ Checking applied migrations...');
    
    // Check supabase_migrations table
    const { data: migrations, error: migrationsError } = await supabase
      .from('supabase_migrations')
      .select('*')
      .order('version', { ascending: true });

    if (migrationsError) {
      console.error('❌ Failed to fetch migrations:', migrationsError);
    } else {
      console.log(`✅ Found ${migrations?.length || 0} applied migrations`);
      console.log('\nApplied migrations:');
      migrations?.forEach(migration => {
        console.log(`   📄 ${migration.version} - ${migration.name || 'No name'}`);
      });
    }

    console.log('\n2️⃣ Checking companies table structure...');
    
    // Get companies table structure using information_schema
    const { data: columns, error: columnsError } = await supabase
      .rpc('sql', {
        query: `
          SELECT 
            column_name, 
            data_type, 
            is_nullable, 
            column_default,
            character_maximum_length
          FROM information_schema.columns 
          WHERE table_name = 'companies' 
          AND table_schema = 'public'
          ORDER BY ordinal_position
        `
      });

    if (columnsError) {
      console.error('❌ Failed to fetch table structure:', columnsError);
      
      // Try alternative method
      console.log('\n🔄 Trying alternative query...');
      const { data: altColumns, error: altError } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable')
        .eq('table_name', 'companies')
        .eq('table_schema', 'public');
        
      if (altError) {
        console.error('❌ Alternative query also failed:', altError);
      } else {
        console.log('✅ Companies table columns (alternative method):');
        altColumns?.forEach(col => {
          console.log(`   📋 ${col.column_name} (${col.data_type}) - nullable: ${col.is_nullable}`);
        });
      }
    } else {
      console.log('✅ Companies table columns:');
      columns?.forEach(col => {
        console.log(`   📋 ${col.column_name} (${col.data_type}) - nullable: ${col.is_nullable} - default: ${col.column_default || 'none'}`);
      });
    }

    console.log('\n3️⃣ Checking specific columns we need...');
    
    // Test if we can select the specific columns
    const requiredColumns = ['registration_status', 'business_type', 'primary_contact'];
    
    for (const column of requiredColumns) {
      try {
        const { data, error } = await supabase
          .from('companies')
          .select(column)
          .limit(1);
          
        if (error) {
          console.log(`❌ Column '${column}' missing or inaccessible: ${error.message}`);
        } else {
          console.log(`✅ Column '${column}' exists and accessible`);
        }
      } catch (error) {
        console.log(`❌ Column '${column}' check failed: ${error.message}`);
      }
    }

    console.log('\n4️⃣ Testing company creation (dry run)...');
    
    // Test the exact insert that's failing
    const testData = {
      name: 'Test Company',
      domain: 'test-domain-' + Date.now() + '.com',
      registration_status: 'approved',
      business_type: 'staffing_agency',
      recruitment_enabled: true,
      bench_sales_enabled: true,
      primary_contact: {
        name: 'Test User',
        email: '<EMAIL>',
        phone: '************'
      }
    };

    console.log('   Test data:', JSON.stringify(testData, null, 2));

    const { data: insertResult, error: insertError } = await supabase
      .from('companies')
      .insert(testData)
      .select('id')
      .single();

    if (insertError) {
      console.error('❌ Test insert failed:', insertError);
      console.error('   Error details:', JSON.stringify(insertError, null, 2));
    } else {
      console.log('✅ Test insert successful:', insertResult?.id);
      
      // Clean up the test record
      await supabase
        .from('companies')
        .delete()
        .eq('id', insertResult.id);
      console.log('   ♻️  Test record cleaned up');
    }

  } catch (error) {
    console.error('❌ Schema check failed:', error);
  }
}

checkDatabaseSchema().catch(console.error);
