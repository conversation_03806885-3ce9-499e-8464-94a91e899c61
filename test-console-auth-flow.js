#!/usr/bin/env node

// Test console authentication and approval flow
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const anonKey = process.env.PUBLIC_SUPABASE_ANON_KEY;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhmemh2cmtuamd3dHJrZ3lpbmpmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA2ODI4MiwiZXhwIjoyMDY1NjQ0MjgyfQ._scXNK1IgQuVok-iIBVxQYmcWntrI2VLInx1_Cz8knI';

console.log('🔐 Testing Console Authentication & Approval Flow');

async function testConsoleAuth() {
  // Create both clients
  const serviceClient = createClient(supabaseUrl, serviceRoleKey, {
    auth: { autoRefreshToken: false, persistSession: false }
  });
  
  const userClient = createClient(supabaseUrl, anon<PERSON>ey);

  try {
    console.log('\n1️⃣ Testing with service role (should work)...');
    
    // Test with service role (this should work as we saw before)
    const { data: serviceCompany, error: serviceError } = await serviceClient
      .from('companies')
      .insert({
        name: 'Service Role Test Company',
        domain: 'service-test-' + Date.now() + '.com',
        registration_status: 'approved',
        business_type: 'staffing_agency',
        recruitment_enabled: true,
        bench_sales_enabled: true,
        primary_contact: { name: 'Test', email: '<EMAIL>' }
      })
      .select('id')
      .single();

    if (serviceError) {
      console.error('❌ Service role test failed:', serviceError);
    } else {
      console.log('✅ Service role can create companies:', serviceCompany.id);
      
      // Clean up
      await serviceClient.from('companies').delete().eq('id', serviceCompany.id);
      console.log('   ♻️ Cleaned up test company');
    }

    console.log('\n2️⃣ Checking console user authentication context...');
    
    // Get console users to find a valid one to test with
    const { data: consoleUsers, error: usersError } = await serviceClient
      .from('console_users')
      .select('*')
      .eq('is_active', true)
      .limit(1);

    if (usersError || !consoleUsers || consoleUsers.length === 0) {
      console.error('❌ No active console users found:', usersError);
      return;
    }

    const testUser = consoleUsers[0];
    console.log(`✅ Found console user: ${testUser.email} (${testUser.role})`);

    console.log('\n3️⃣ Simulating authenticated console user context...');
    
    // For testing, we'll simulate what happens in the approval flow
    // The key issue is that the approval code runs in a user context, not service role context
    
    // Let's check what happens when we try to create a company with user authentication
    console.log('   🔧 Testing company creation in user context...');
    
    // Check if there are auth users that correspond to console users
    const { data: authUsers, error: authError } = await serviceClient
      .rpc('sql', {
        query: `
          SELECT au.id, au.email, cu.role, cu.is_active
          FROM auth.users au
          JOIN console_users cu ON au.id = cu.id
          WHERE cu.is_active = true
          LIMIT 5
        `
      });

    if (authError) {
      console.log('⚠️ Cannot query auth users directly');
      console.log('   This is expected - auth.users is protected');
    } else {
      console.log('✅ Found linked auth users:', authUsers?.length || 0);
    }

    console.log('\n4️⃣ Testing the actual approval code path...');
    
    // Get a pending registration to test with
    const { data: pendingReg, error: regError } = await serviceClient
      .from('business_registrations')
      .select('*')
      .eq('status', 'pending')
      .limit(1)
      .single();

    if (regError || !pendingReg) {
      console.error('❌ No pending registrations to test with');
      return;
    }

    console.log(`📋 Testing with registration: ${pendingReg.company_name}`);

    // Simulate the exact approval flow using service role (which works)
    // but identify what might be different in the actual web request
    
    const approvalData = {
      name: pendingReg.company_name,
      domain: pendingReg.company_domain,
      registration_status: 'approved', 
      business_type: 'staffing_agency',
      recruitment_enabled: true,
      bench_sales_enabled: true,
      primary_contact: {
        name: pendingReg.contact_person_name,
        email: pendingReg.contact_person_email,
        phone: pendingReg.contact_person_phone
      }
    };

    console.log('   💼 Creating company with exact approval data...');
    const { data: approvalCompany, error: approvalError } = await serviceClient
      .from('companies')
      .insert(approvalData)
      .select('id')
      .single();

    if (approvalError) {
      console.error('❌ Approval simulation failed:', approvalError);
      console.error('   This suggests a different issue than RLS');
    } else {
      console.log('✅ Approval simulation works with service role');
      
      // Test updating the registration
      const { error: updateError } = await serviceClient
        .from('business_registrations')
        .update({
          status: 'approved',
          reviewed_by: testUser.id,
          reviewed_at: new Date().toISOString(),
          activated_company_id: approvalCompany.id
        })
        .eq('id', pendingReg.id);

      if (updateError) {
        console.error('❌ Registration update failed:', updateError);
      } else {
        console.log('✅ Registration update successful');
        
        // Revert changes
        await serviceClient
          .from('business_registrations')
          .update({
            status: 'pending',
            reviewed_by: null,
            reviewed_at: null,
            activated_company_id: null
          })
          .eq('id', pendingReg.id);
      }
      
      // Clean up test company
      await serviceClient.from('companies').delete().eq('id', approvalCompany.id);
      console.log('   ♻️ Test cleaned up');
    }

    console.log('\n🎯 DIAGNOSIS:');
    console.log('   ✅ Database schema is correct');
    console.log('   ✅ Service role can create companies');
    console.log('   ✅ Console users exist');
    console.log('   ✅ RLS policies allow service role operations');
    console.log('');
    console.log('   🔍 LIKELY ISSUE: The approval code may be running with');
    console.log('       a user authentication context that doesn\'t match');
    console.log('       the RLS policy requirements.');
    console.log('');
    console.log('   💡 SOLUTION: The approval code should use the');
    console.log('       service role client (adminClient) which it already does,');
    console.log('       but there might be a context switching issue.');

    console.log('\n📋 Next Steps:');
    console.log('   1. Try the approval in the console app again');
    console.log('   2. Check browser console for the detailed error message');
    console.log('   3. The improved error logging should show the exact Supabase error');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testConsoleAuth().catch(console.error);
