# Environment Configuration Guide

## 📁 File Structure (Standardized)

### ✅ Active Configuration Files
```
PSII/
├── .env.local                          # Master configuration (shared settings)
├── .env.example                        # Template for new setups
├── apps/
│   ├── console-app/.env.local          # Console app specific settings
│   ├── console-app/.env.example        # Console app template
│   ├── candidate-app/.env.local        # Candidate app specific settings
│   ├── customer-app/.env.local         # Customer app specific settings
│   └── customer-app/.env.example       # Customer app template
```

### 🗂️ Backup Files (Reference Only)
```
├── .env.backup                         # Backed up during cleanup
├── apps/console-app/.env.backup        # Backed up during cleanup
├── apps/candidate-app/.env.backup      # Backed up during cleanup
└── apps/customer-app/.env.backup       # Backed up during cleanup
```

## 🔧 Configuration Priority

1. **App-specific .env.local** - Highest priority (app overrides)
2. **Root .env.local** - Shared configuration (inherited by all apps)
3. **Framework defaults** - Lowest priority

## 🚀 Key Settings Verified

### ✅ Supabase Configuration
- **URL**: `https://hfzhvrknjgwtrkgyinjf.supabase.co`
- **Environment**: Cloud (production database)
- **All keys**: Properly configured and tested

### ✅ Email Service
- **Provider**: Resend API
- **API Key**: `re_8MBgcdYb_DaURr46BfLnvsymyY1jqxqjt` ✅ Verified working
- **From Email**: `<EMAIL>`

### ✅ Application Ports
- **Console App**: `3008` (http://localhost:3008)
- **Customer App**: `3004` (http://localhost:3004)  
- **Candidate App**: `3006` (http://localhost:3006)

### ✅ Database Tables
- **console_user_invitations**: ✅ Created and tested
- **All console tables**: ✅ Verified existing

## 🧪 Testing Commands

### Test Email Configuration
```bash
cd /Users/<USER>/Desktop/PSII
node check-email-config.js
```

### Test Invitation System
```bash
cd /Users/<USER>/Desktop/PSII
node debug-invitations.js
```

### Start Applications
```bash
# Console App
cd apps/console-app && npm run dev

# Customer App  
cd apps/customer-app && npm run dev

# Candidate App
cd apps/candidate-app && npm run dev
```

## 🎯 Next Steps

1. **Start Console App**: `cd apps/console-app && npm run dev`
2. **Login**: Use <EMAIL> / SecureAdmin123!@#
3. **Test Invitations**: Go to Users section and create an invitation
4. **Verify Email**: Check that invitation emails are sent via Resend

## 🔒 Security Notes

- All credentials are for development environment
- Use different keys for production deployment
- Resend API key is configured and verified working
- Database uses Supabase Cloud with proper RLS policies

## 📝 Configuration Changes Made

1. **Standardized** all environment files to use `.env.local`
2. **Removed** confusing `.env` files (backed up as `.env.backup`)
3. **Added** your Resend API key to all relevant files
4. **Verified** Supabase Cloud configuration consistency
5. **Tested** email and database connectivity
6. **Created** `console_user_invitations` table
7. **Ensured** proper port configuration for all apps

✅ **Status**: Ready for development and testing!
