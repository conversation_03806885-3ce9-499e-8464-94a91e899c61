#!/usr/bin/env node

// Test database functions
import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

config()

const supabase = createClient(
  process.env.PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

console.log('🧪 Testing Database Functions...\n')

async function testCreateVerificationToken() {
  console.log('🔑 Testing create_verification_token function...')
  try {
    const { data, error } = await supabase.rpc('create_verification_token', {
      p_user_id: '00000000-0000-0000-0000-000000000000',
      p_email: '<EMAIL>', 
      p_first_name: 'Test'
    })
    
    if (error) {
      console.error('❌ create_verification_token failed:', error.message)
      console.error('Full error:', error)
      return false
    }
    
    console.log('✅ create_verification_token successful')
    console.log('Token:', data)
    return true
  } catch (error) {
    console.error('❌ create_verification_token exception:', error.message)
    return false
  }
}

async function testVerifyEmailToken() {
  console.log('\n📧 Testing verify_email_token function...')
  try {
    const { data, error } = await supabase.rpc('verify_email_token', {
      p_token: 'invalid-token-test'
    })
    
    if (error) {
      console.error('❌ verify_email_token failed:', error.message)
      console.error('Full error:', error)
      return false
    }
    
    console.log('✅ verify_email_token function exists and responds')
    console.log('Response:', data)
    return true
  } catch (error) {
    console.error('❌ verify_email_token exception:', error.message)
    return false
  }
}

async function testEmailVerificationTable() {
  console.log('\n📋 Testing email_verification_tokens table...')
  try {
    const { data, error } = await supabase
      .from('email_verification_tokens')
      .select('id')
      .limit(1)
    
    if (error) {
      console.error('❌ Table access failed:', error.message)
      return false
    }
    
    console.log('✅ email_verification_tokens table is accessible')
    return true
  } catch (error) {
    console.error('❌ Table test exception:', error.message)
    return false
  }
}

async function runTests() {
  const results = []
  
  results.push(await testEmailVerificationTable())
  results.push(await testCreateVerificationToken())
  results.push(await testVerifyEmailToken())
  
  console.log('\n📊 Database Test Results:')
  console.log('Email Verification Table:', results[0] ? '✅ PASS' : '❌ FAIL')
  console.log('Create Token Function:', results[1] ? '✅ PASS' : '❌ FAIL')
  console.log('Verify Token Function:', results[2] ? '✅ PASS' : '❌ FAIL')
  
  const passCount = results.filter(r => r).length
  console.log(`\n${passCount}/3 database components working`)
  
  return results
}

runTests().catch(console.error)