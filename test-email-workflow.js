// Test Email Workflow - Phase 3 Testing
// This script tests the complete email notification system

import { sendAdminNotifications } from '@psii/business-email-workflow';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const testRegistration = {
  id: 'test-email-workflow-123',
  contact_person_email: '<EMAIL>',
  contact_person_name: 'John Test User',
  company_name: 'Test Company LLC',
  company_domain: 'newcompany.com',
  status: 'pending'
};

const testExistingCustomer = {
  id: 'test-existing-customer-456',
  contact_person_email: '<EMAIL>',
  contact_person_name: 'Jane Existing User',
  company_name: 'Test Company Inc',
  company_domain: 'testcompany.com',
  status: 'pending'
};

async function testEmailWorkflow() {
  console.log('🧪 Testing Email Workflow Phase 3');
  console.log('================================\n');

  // Test 1: New Company (should route to console admin)
  console.log('Test 1: NEW_COMPANY Domain Detection');
  console.log('Testing domain: newcompany.com (should be new)');
  
  try {
    const result1 = await sendAdminNotifications(
      testRegistration,
      process.env.PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    console.log('✅ New Company Test Result:', {
      success: result1.success,
      emails_sent: result1.emails_sent,
      errors: result1.errors
    });
  } catch (error) {
    console.error('❌ New Company Test Failed:', error);
  }

  console.log('\n---\n');

  // Test 2: Existing Customer (should route to company admin)
  console.log('Test 2: EXISTING_CUSTOMER Domain Detection');
  console.log('Testing domain: testcompany.com (should exist in DB)');
  
  try {
    const result2 = await sendAdminNotifications(
      testExistingCustomer,
      process.env.PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    console.log('✅ Existing Customer Test Result:', {
      success: result2.success,
      emails_sent: result2.emails_sent,
      errors: result2.errors
    });
  } catch (error) {
    console.error('❌ Existing Customer Test Failed:', error);
  }

  console.log('\n================================');
  console.log('🎯 Email Workflow Test Complete');
}

// Run the test
testEmailWorkflow().catch(console.error);
